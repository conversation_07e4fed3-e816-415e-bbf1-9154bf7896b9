# Comprehensive fix for all remaining issues
Write-Host "Starting comprehensive fix for Electronic Archiving System..." -ForegroundColor Green

# Fix 1: Replace nameof with string literals (C# 5.0 doesn't support nameof)
Write-Host "Fixing nameof usage..." -ForegroundColor Yellow
$files = @(
    "BusinessLogic\DepartmentService.cs",
    "BusinessLogic\DocumentService.cs", 
    "Utilities\PasswordHelper.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw -Encoding UTF8
        $content = $content -replace 'nameof\(([^)]+)\)', '"$1"'
        Set-Content -Path $file -Value $content -Encoding UTF8
        Write-Host "  Fixed nameof in $file" -ForegroundColor Green
    }
}

# Fix 2: Replace string interpolation with string.Format
Write-Host "Fixing remaining string interpolation..." -ForegroundColor Yellow

# Fix complex interpolation patterns
$interpolationFixes = @{
    'DateTime\.Now:([^}]+)' = 'DateTime.Now.ToString("$1")'
    'totalSize / 1024:F1' = '(totalSize / 1024.0).ToString("F1")'
    'totalSize / \(1024 \* 1024\):F1' = '(totalSize / (1024.0 * 1024.0)).ToString("F1")'
    'Environment\.TickCount / 1000 / 60:F1' = '(Environment.TickCount / 1000.0 / 60.0).ToString("F1")'
    'GC\.GetTotalMemory\(false\) / 1024 / 1024:F1' = '(GC.GetTotalMemory(false) / 1024.0 / 1024.0).ToString("F1")'
}

$allFiles = Get-ChildItem -Path . -Recurse -Filter "*.cs" | Where-Object { $_.Name -ne "SimpleApp.cs" }

foreach ($file in $allFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    foreach ($pattern in $interpolationFixes.Keys) {
        $replacement = $interpolationFixes[$pattern]
        $content = $content -replace $pattern, $replacement
    }
    
    # Fix remaining $ interpolations
    $content = $content -replace '\$"([^"]*)"', '"$1"'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Fixed interpolation in $($file.Name)" -ForegroundColor Green
    }
}

# Fix 3: Fix out parameters syntax
Write-Host "Fixing out parameter syntax..." -ForegroundColor Yellow
$files = Get-ChildItem -Path . -Recurse -Filter "*.cs"
foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # Fix out int syntax
    $content = $content -replace 'out int ([^)]+)', 'out $1'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Fixed out parameters in $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Comprehensive fix completed!" -ForegroundColor Green
Write-Host "Ready to build with C# 5.0 compiler!" -ForegroundColor Cyan
