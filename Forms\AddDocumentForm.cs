using System;
using System.Drawing;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.BusinessLogic;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using Ali_Mo<PERSON>.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج إضافة وثيقة جديدة
    /// Add new document form
    /// </summary>
    public partial class AddDocumentForm : Form
    {
        #region Controls
        private ComboBox cmbDocumentType;
        private TextBox txtSerialNumber;
        private DateTimePicker dtpEntryDate;
        private TextBox txtDocumentNumber;
        private DateTimePicker dtpDocumentDate;
        private TextBox txtSubject;
        private TextBox txtFrom;
        private TextBox txtTo;
        private ComboBox cmbFileBox;
        private ComboBox cmbDepartment;
        private Panel rightPanel;
        private Panel leftPanel;
        private ListBox lstAttachments;
        private Button btnAddFile;
        private Button btnSave;
        private Button btnCancel;
        #endregion

        /// <summary>
        /// منشئ نموذج إضافة الوثيقة
        /// Add document form constructor
        /// </summary>
        public AddDocumentForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "إضافة وثيقة جديدة";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.WindowState = FormWindowState.Normal;

            // إعداد الخط العربي
            // Setup Arabic font
            Font arabicFont = new Font("Tahoma", 10F, FontStyle.Regular);
            this.Font = arabicFont;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreatePanels();
            CreateRightPanelControls();
            CreateLeftPanelControls();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء اللوحات الرئيسية
        /// Create main panels
        /// </summary>
        private void CreatePanels()
        {
            // اللوحة اليمنى للبيانات
            // Right panel for data
            rightPanel = new Panel
            {
                Size = new Size(500, 600),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(rightPanel);

            // اللوحة اليسرى للمرفقات
            // Left panel for attachments
            leftPanel = new Panel
            {
                Size = new Size(450, 600),
                Location = new Point(530, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(leftPanel);
        }

        /// <summary>
        /// إنشاء عناصر التحكم في اللوحة اليمنى
        /// Create right panel controls
        /// </summary>
        private void CreateRightPanelControls()
        {
            int yPos = 20;
            int labelWidth = 120;
            int controlWidth = 300;
            int spacing = 40;

            // عنوان اللوحة
            // Panel title
            Label titleLabel = new Label
            {
                Text = "بيانات الوثيقة",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(200, 25),
                Location = new Point(20, yPos)
            };
            rightPanel.Controls.Add(titleLabel);
            yPos += spacing;

            // نوع الوثيقة
            // Document type
            CreateLabelAndControl("نوع الوثيقة:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                cmbDocumentType = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Size = new Size(controlWidth, 25)
                };
                cmbDocumentType.Items.AddRange(new[] { "صادر", "وارد" });
                cmbDocumentType.SelectedIndex = 0;
                return cmbDocumentType;
            });

            // الرقم المسلسل
            // Serial number
            CreateLabelAndControl("الرقم المسلسل:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                txtSerialNumber = new TextBox
                {
                    Size = new Size(controlWidth, 25),
                    ReadOnly = true,
                    BackColor = Color.FromArgb(240, 240, 240),
                    Text = "سيتم توليده تلقائياً"
                };
                return txtSerialNumber;
            });

            // تاريخ الإدخال
            // Entry date
            CreateLabelAndControl("تاريخ الإدخال:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                dtpEntryDate = new DateTimePicker
                {
                    Size = new Size(controlWidth, 25),
                    Format = DateTimePickerFormat.Custom,
                    CustomFormat = "dd/MM/yyyy HH:mm",
                    Enabled = false,
                    Value = DateTime.Now
                };
                return dtpEntryDate;
            });

            // رقم الوثيقة
            // Document number
            CreateLabelAndControl("رقم الوثيقة:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                txtDocumentNumber = new TextBox
                {
                    Size = new Size(controlWidth, 25)
                };
                return txtDocumentNumber;
            });

            // تاريخ الوثيقة
            // Document date
            CreateLabelAndControl("تاريخ الوثيقة:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                dtpDocumentDate = new DateTimePicker
                {
                    Size = new Size(controlWidth, 25),
                    Format = DateTimePickerFormat.Short,
                    Value = DateTime.Today
                };
                return dtpDocumentDate;
            });

            // الموضوع
            // Subject
            CreateLabelAndControl("الموضوع:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                txtSubject = new TextBox
                {
                    Size = new Size(controlWidth, 60),
                    Multiline = true,
                    ScrollBars = ScrollBars.Vertical
                };
                return txtSubject;
            });
            yPos += 20; // إضافة مساحة إضافية للنص متعدد الأسطر

            // من
            // From
            CreateLabelAndControl("من:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                txtFrom = new TextBox
                {
                    Size = new Size(controlWidth, 25)
                };
                return txtFrom;
            });

            // إلى
            // To
            CreateLabelAndControl("إلى:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                txtTo = new TextBox
                {
                    Size = new Size(controlWidth, 25)
                };
                return txtTo;
            });

            // القسم
            // Department
            CreateLabelAndControl("القسم:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                cmbDepartment = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Size = new Size(controlWidth, 25)
                };
                return cmbDepartment;
            });

            // الاضبارة
            // File box
            CreateLabelAndControl("الاضبارة:", ref yPos, labelWidth, controlWidth, spacing, () =>
            {
                cmbFileBox = new ComboBox
                {
                    DropDownStyle = ComboBoxStyle.DropDownList,
                    Size = new Size(controlWidth, 25)
                };
                return cmbFileBox;
            });
        }

        /// <summary>
        /// إنشاء تسمية وعنصر تحكم
        /// Create label and control
        /// </summary>
        private void CreateLabelAndControl(string labelText, ref int yPos, int labelWidth, int controlWidth, int spacing, Func<Control> createControl)
        {
            Label label = new Label
            {
                Text = labelText,
                Size = new Size(labelWidth, 25),
                Location = new Point(20, yPos),
                TextAlign = ContentAlignment.MiddleRight
            };
            rightPanel.Controls.Add(label);

            Control control = createControl();
            control.Location = new Point(150, yPos);
            rightPanel.Controls.Add(control);

            yPos += spacing;
        }

        /// <summary>
        /// إنشاء عناصر التحكم في اللوحة اليسرى
        /// Create left panel controls
        /// </summary>
        private void CreateLeftPanelControls()
        {
            // عنوان اللوحة
            // Panel title
            Label titleLabel = new Label
            {
                Text = "المرفقات",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(200, 25),
                Location = new Point(20, 20)
            };
            leftPanel.Controls.Add(titleLabel);

            // قائمة المرفقات
            // Attachments list
            lstAttachments = new ListBox
            {
                Size = new Size(400, 400),
                Location = new Point(20, 60),
                BorderStyle = BorderStyle.FixedSingle
            };
            leftPanel.Controls.Add(lstAttachments);

            // زر إضافة ملف
            // Add file button
            btnAddFile = new Button
            {
                Text = "إضافة ملف",
                Size = new Size(120, 35),
                Location = new Point(20, 480),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnAddFile.FlatAppearance.BorderSize = 0;
            btnAddFile.Click += BtnAddFile_Click;
            leftPanel.Controls.Add(btnAddFile);

            // زر الماسح الضوئي (مؤقت)
            // Scanner button (temporary)
            Button btnScanner = new Button
            {
                Text = "الماسح الضوئي",
                Size = new Size(120, 35),
                Location = new Point(160, 480),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F),
                Enabled = false
            };
            btnScanner.FlatAppearance.BorderSize = 0;
            leftPanel.Controls.Add(btnScanner);

            // زر حذف ملف
            // Delete file button
            Button btnDeleteFile = new Button
            {
                Text = "حذف ملف",
                Size = new Size(120, 35),
                Location = new Point(300, 480),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnDeleteFile.FlatAppearance.BorderSize = 0;
            btnDeleteFile.Click += BtnDeleteFile_Click;
            leftPanel.Controls.Add(btnDeleteFile);
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر الحفظ
            // Save button
            btnSave = new Button
            {
                Text = "حفظ الوثيقة",
                Size = new Size(120, 40),
                Location = new Point(400, 640),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11F, FontStyle.Bold)
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;
            this.Controls.Add(btnSave);

            // زر الإلغاء
            // Cancel button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(120, 40),
                Location = new Point(540, 640),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11F)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// تحميل البيانات
        /// Load data
        /// </summary>
        private void LoadData()
        {
            try
            {
                // تحميل الأقسام (بيانات وهمية للعرض)
                // Load departments (mock data for display)
                cmbDepartment.Items.Add("القسم العام");
                cmbDepartment.Items.Add("قسم الشؤون الإدارية");
                cmbDepartment.Items.Add("قسم المالية");
                cmbDepartment.SelectedIndex = 0;

                // تحميل الاضبارات (بيانات وهمية للعرض)
                // Load file boxes (mock data for display)
                cmbFileBox.Items.Add("اضبارة عامة");
                cmbFileBox.Items.Add("اضبارة المراسلات");
                cmbFileBox.Items.Add("اضبارة القرارات");
                cmbFileBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل البيانات", ex);
                MessageBox.Show("حدث خطأ أثناء تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث إضافة ملف
        /// Add file event handler
        /// </summary>
        private void BtnAddFile_Click(object sender, EventArgs e)
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Title = "اختر الملفات المراد إرفاقها";
                    openFileDialog.Filter = "جميع الملفات المدعومة|*.pdf;*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.docx;*.doc;*.txt|" +
                                          "ملفات PDF|*.pdf|" +
                                          "ملفات الصور|*.png;*.jpg;*.jpeg;*.gif;*.bmp|" +
                                          "ملفات Word|*.docx;*.doc|" +
                                          "ملفات نصية|*.txt";
                    openFileDialog.Multiselect = true;

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        foreach (string fileName in openFileDialog.FileNames)
                        {
                            lstAttachments.Items.Add(System.IO.Path.GetFileName(fileName));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة الملف", ex);
                MessageBox.Show("حدث خطأ أثناء إضافة الملف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث حذف ملف
        /// Delete file event handler
        /// </summary>
        private void BtnDeleteFile_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstAttachments.SelectedIndex >= 0)
                {
                    lstAttachments.Items.RemoveAt(lstAttachments.SelectedIndex);
                }
                else
                {
                    MessageBox.Show("يرجى اختيار ملف لحذفه", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حذف الملف", ex);
                MessageBox.Show("حدث خطأ أثناء حذف الملف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث الحفظ
        /// Save event handler
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                // Validate data
                if (!ValidateData())
                    return;

                // عرض رسالة نجاح (مؤقت)
                // Show success message (temporary)
                MessageBox.Show("تم حفظ الوثيقة بنجاح!\n(هذه رسالة تجريبية - سيتم تنفيذ الحفظ الفعلي لاحقاً)",
                    "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);

                Logger.LogInfo($"تم حفظ وثيقة جديدة: {txtSubject.Text}");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حفظ الوثيقة", ex);
                MessageBox.Show("حدث خطأ أثناء حفظ الوثيقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث الإلغاء
        /// Cancel event handler
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إلغاء إضافة الوثيقة؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        #endregion

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        private bool ValidateData()
        {
            if (string.IsNullOrWhiteSpace(txtSubject.Text))
            {
                MessageBox.Show("يرجى إدخال موضوع الوثيقة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSubject.Focus();
                return false;
            }

            if (cmbDepartment.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار القسم", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbDepartment.Focus();
                return false;
            }

            if (cmbFileBox.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار الاضبارة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbFileBox.Focus();
                return false;
            }

            return true;
        }
    }
}
