using System;
using System.Drawing;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.BusinessLogic;
using <PERSON>_<PERSON><PERSON>.Utilities;

namespace <PERSON>_<PERSON>.Forms
{
    /// <summary>
    /// نموذج إضافة وثيقة جديدة
    /// Add new document form
    /// </summary>
    public partial class AddDocumentForm : Form
    {
        /// <summary>
        /// منشئ نموذج إضافة الوثيقة
        /// Add document form constructor
        /// </summary>
        public AddDocumentForm()
        {
            InitializeComponent();
            SetupForm();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "إضافة وثيقة جديدة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);

            // إعداد الخط العربي
            // Setup Arabic font
            Font arabicFont = new Font("Tahoma", 10F, FontStyle.Regular);
            this.Font = arabicFont;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // رسالة مؤقتة
            // Temporary message
            Label tempLabel = new Label
            {
                Text = "نموذج إضافة الوثيقة قيد التطوير...\nسيتم إضافة الوظائف الكاملة قريباً",
                Font = new Font("Tahoma", 14F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(tempLabel);

            // زر إغلاق
            // Close button
            Button closeBtn = new Button
            {
                Text = "إغلاق",
                Size = new Size(100, 35),
                Location = new Point(350, 500),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            closeBtn.FlatAppearance.BorderSize = 0;
            closeBtn.Click += (s, e) => this.Close();
            this.Controls.Add(closeBtn);
        }
    }
}
