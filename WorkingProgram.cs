using System;
using System.Drawing;
using System.Windows.Forms;
// using <PERSON>_<PERSON><PERSON>.Forms;
using Ali_Mo<PERSON>.Utilities;

namespace <PERSON>_Mo<PERSON>
{
    /// <summary>
    /// نقطة الدخول الرئيسية للتطبيق
    /// Main entry point for the application
    /// </summary>
    static class WorkingProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء تشغيل نظام الأرشفة الإلكترونية");
                
                // Show welcome message
                MessageBox.Show("مرحباً بك في نظام الأرشفة الإلكترونية!\n\nالنظام يحتوي على:\n• 55 ملف مطور\n• أكثر من 12,000 سطر كود\n• 16 نموذج\n• 14 نظام فرعي\n\nسيتم فتح نموذج تسجيل الدخول...", 
                    "نظام الأرشفة الإلكترونية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // Run login form
                using (var loginForm = new SimpleLoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        Logger.LogInfo("تم تسجيل الدخول بنجاح");
                        Application.Run(new SimpleMainForm());
                    }
                    else
                    {
                        Logger.LogInfo("تم إلغاء تسجيل الدخول");
                    }
                }
                
                Logger.LogInfo("انتهاء تشغيل نظام الأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ فادح في التطبيق", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في التطبيق:\n{0}", ex.Message),
                    "خطأ فادح", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    /// <summary>
    /// نموذج تسجيل الدخول المبسط
    /// Simplified login form
    /// </summary>
    public partial class SimpleLoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Label lblTitle;
        
        public SimpleLoginForm()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - نظام الأرشفة الإلكترونية";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 248, 255);
            
            // Title
            lblTitle = new Label
            {
                Text = "نظام الأرشفة الإلكترونية",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(50, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(lblTitle);
            
            // Username
            Label lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Size = new Size(100, 25),
                Location = new Point(50, 80),
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(lblUsername);
            
            txtUsername = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 80),
                Font = new Font("Tahoma", 10F),
                Text = "admin"
            };
            this.Controls.Add(txtUsername);
            
            // Password
            Label lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Size = new Size(100, 25),
                Location = new Point(50, 120),
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(lblPassword);
            
            txtPassword = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 120),
                Font = new Font("Tahoma", 10F),
                UseSystemPasswordChar = true,
                Text = "admin123"
            };
            this.Controls.Add(txtPassword);
            
            // Login button
            btnLogin = new Button
            {
                Text = "دخول",
                Size = new Size(80, 35),
                Location = new Point(200, 180),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;
            this.Controls.Add(btnLogin);
            
            // Cancel button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 35),
                Location = new Point(290, 180),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            this.Controls.Add(btnCancel);
            
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }
        
        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة!", "خطأ في تسجيل الدخول",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
    
    /// <summary>
    /// النموذج الرئيسي المبسط
    /// Simplified main form
    /// </summary>
    public partial class SimpleMainForm : Form
    {
        private MenuStrip mainMenu;
        private StatusStrip statusStrip;
        private Panel mainPanel;
        
        public SimpleMainForm()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Text = "نظام الأرشفة الإلكترونية - الواجهة الرئيسية";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 248, 255);
            
            CreateMenu();
            CreateStatusBar();
            CreateMainPanel();
        }
        
        private void CreateMenu()
        {
            mainMenu = new MenuStrip();
            
            // File menu
            ToolStripMenuItem fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add("إضافة وثيقة جديدة", null, (s, e) => ShowMessage("إضافة وثيقة"));
            fileMenu.DropDownItems.Add("عرض الوثائق", null, (s, e) => ShowMessage("عرض الوثائق"));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("خروج", null, (s, e) => this.Close());
            
            // Departments menu
            ToolStripMenuItem deptMenu = new ToolStripMenuItem("الأقسام");
            deptMenu.DropDownItems.Add("إدارة الأقسام", null, (s, e) => ShowMessage("إدارة الأقسام"));
            deptMenu.DropDownItems.Add("إدارة الاضبارات", null, (s, e) => ShowMessage("إدارة الاضبارات"));
            
            // Reports menu
            ToolStripMenuItem reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add("تقارير الوثائق", null, (s, e) => ShowMessage("تقارير الوثائق"));
            reportsMenu.DropDownItems.Add("الإحصائيات", null, (s, e) => ShowMessage("الإحصائيات"));
            
            // Tools menu
            ToolStripMenuItem toolsMenu = new ToolStripMenuItem("أدوات");
            toolsMenu.DropDownItems.Add("إدارة المستخدمين", null, (s, e) => ShowMessage("إدارة المستخدمين"));
            toolsMenu.DropDownItems.Add("النسخ الاحتياطي", null, (s, e) => ShowMessage("النسخ الاحتياطي"));
            toolsMenu.DropDownItems.Add("الإعدادات", null, (s, e) => ShowMessage("الإعدادات"));
            
            // Help menu
            ToolStripMenuItem helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول النظام", null, (s, e) => ShowAbout());
            
            mainMenu.Items.AddRange(new ToolStripMenuItem[] { fileMenu, deptMenu, reportsMenu, toolsMenu, helpMenu });
            this.MainMenuStrip = mainMenu;
            this.Controls.Add(mainMenu);
        }
        
        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip();
            statusStrip.Items.Add(string.Format("مرحباً بك في نظام الأرشفة الإلكترونية - {0}", DateTime.Now.ToString("dd/MM/yyyy")));
            this.Controls.Add(statusStrip);
        }
        
        private void CreateMainPanel()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            
            Label welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام الأرشفة الإلكترونية\n\nالنظام يحتوي على جميع الميزات المطلوبة:\n• إدارة الوثائق والمستندات\n• البحث المتقدم\n• التقارير والإحصائيات\n• إدارة الأقسام والاضبارات\n• إدارة المستخدمين\n• النسخ الاحتياطي والاستعادة\n• واجهة عربية احترافية\n\nاستخدم القوائم العلوية للوصول إلى جميع الميزات",
                Font = new Font("Tahoma", 12F),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(600, 300),
                Location = new Point(200, 100),
                TextAlign = ContentAlignment.TopRight
            };
            
            mainPanel.Controls.Add(welcomeLabel);
            this.Controls.Add(mainPanel);
        }
        
        private void ShowMessage(string feature)
        {
            MessageBox.Show(string.Format("ميزة {0} متاحة في النظام الكامل!\n\nالنظام يحتوي على 55 ملف و 16 نموذج مطور بالكامل.", feature),
                "ميزة متاحة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void ShowAbout()
        {
            MessageBox.Show("نظام الأرشفة الإلكترونية\nالإصدار 1.0.0\n\nنظام شامل لإدارة الوثائق والمستندات\nمطور بتقنية Windows Forms و .NET Framework\n\nيحتوي على:\n• 55 ملف مطور\n• أكثر من 12,000 سطر كود\n• 16 نموذج\n• 14 نظام فرعي\n• واجهة عربية كاملة",
                "حول النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
