﻿using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using <PERSON>_<PERSON><PERSON>.BusinessLogic;
using Ali_Mo<PERSON>.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج عرض تفاصيل الوثيقة
    /// Document viewer form
    /// </summary>
    public partial class DocumentViewerForm : Form
    {
        #region Controls
        private Panel infoPanel;
        private Panel attachmentsPanel;
        private Label lblSerialNumber;
        private Label lblDocumentType;
        private Label lblDocumentDate;
        private Label lblEntryDate;
        private Label lblSubject;
        private Label lblFrom;
        private Label lblTo;
        private Label lblDepartment;
        private Label lblFileBox;
        private ListBox lstAttachments;
        private Button btnViewAttachment;
        private Button btnDownloadAttachment;
        private Button btnPrintDocument;
        private Button btnClose;
        #endregion

        private Document currentDocument;

        /// <summary>
        /// منشئ نموذج عرض الوثيقة
        /// Document viewer form constructor
        /// </summary>
        /// <param name="document">الوثيقة المراد عرضها</param>
        public DocumentViewerForm(Document document)
        {
            InitializeComponent();
            currentDocument = document;
            SetupForm();
            LoadDocumentData();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "عرض تفاصيل الوثيقة";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateInfoPanel();
            CreateAttachmentsPanel();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء لوحة المعلومات
        /// Create info panel
        /// </summary>
        private void CreateInfoPanel()
        {
            infoPanel = new Panel
            {
                Size = new Size(860, 400),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(infoPanel);

            // عنوان اللوحة
            // Panel title
            Label titleLabel = new Label
            {
                Text = "معلومات الوثيقة",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(200, 30),
                Location = new Point(20, 20)
            };
            infoPanel.Controls.Add(titleLabel);

            // أيقونة الوثيقة
            // Document icon
            Panel iconPanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(750, 15),
                BackColor = Color.FromArgb(33, 150, 243)
            };

            Label iconLabel = new Label
            {
                Text = "📄",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            infoPanel.Controls.Add(iconPanel);

            int yPos = 70;
            int spacing = 35;

            // إنشاء حقول المعلومات
            // Create info fields
            CreateInfoField("الرقم المسلسل:", ref yPos, spacing, out lblSerialNumber);
            CreateInfoField("نوع الوثيقة:", ref yPos, spacing, out lblDocumentType);
            CreateInfoField("تاريخ الوثيقة:", ref yPos, spacing, out lblDocumentDate);
            CreateInfoField("تاريخ الإدخال:", ref yPos, spacing, out lblEntryDate);
            CreateInfoField("الموضوع:", ref yPos, spacing, out lblSubject, true);
            CreateInfoField("من:", ref yPos, spacing, out lblFrom);
            CreateInfoField("إلى:", ref yPos, spacing, out lblTo);
            CreateInfoField("القسم:", ref yPos, spacing, out lblDepartment);
            CreateInfoField("الاضبارة:", ref yPos, spacing, out lblFileBox);
        }

        /// <summary>
        /// إنشاء حقل معلومات
        /// Create info field
        /// </summary>
        private void CreateInfoField(string labelText, ref int yPos, int spacing, out Label valueLabel, bool isMultiline = false)
        {
            Label label = new Label
            {
                Text = labelText,
                Size = new Size(120, 25),
                Location = new Point(20, yPos),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            infoPanel.Controls.Add(label);

            valueLabel = new Label
            {
                Size = isMultiline ? new Size(600, 50) : new Size(600, 25),
                Location = new Point(150, yPos),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(248, 248, 248),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Tahoma", 10F)
            };
            infoPanel.Controls.Add(valueLabel);

            yPos += isMultiline ? spacing + 25 : spacing;
        }

        /// <summary>
        /// إنشاء لوحة المرفقات
        /// Create attachments panel
        /// </summary>
        private void CreateAttachmentsPanel()
        {
            attachmentsPanel = new Panel
            {
                Size = new Size(860, 200),
                Location = new Point(20, 440),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(attachmentsPanel);

            // عنوان اللوحة
            // Panel title
            Label titleLabel = new Label
            {
                Text = "المرفقات",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(100, 25),
                Location = new Point(20, 20)
            };
            attachmentsPanel.Controls.Add(titleLabel);

            // قائمة المرفقات
            // Attachments list
            lstAttachments = new ListBox
            {
                Size = new Size(600, 120),
                Location = new Point(20, 50),
                BorderStyle = BorderStyle.FixedSingle,
                Font = new Font("Tahoma", 10F)
            };
            lstAttachments.SelectedIndexChanged += LstAttachments_SelectedIndexChanged;
            attachmentsPanel.Controls.Add(lstAttachments);

            // أزرار المرفقات
            // Attachment buttons
            btnViewAttachment = new Button
            {
                Text = "عرض المرفق",
                Size = new Size(100, 35),
                Location = new Point(640, 50),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F),
                Enabled = false
            };
            btnViewAttachment.FlatAppearance.BorderSize = 0;
            btnViewAttachment.Click += BtnViewAttachment_Click;
            attachmentsPanel.Controls.Add(btnViewAttachment);

            btnDownloadAttachment = new Button
            {
                Text = "تحميل المرفق",
                Size = new Size(100, 35),
                Location = new Point(640, 95),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F),
                Enabled = false
            };
            btnDownloadAttachment.FlatAppearance.BorderSize = 0;
            btnDownloadAttachment.Click += BtnDownloadAttachment_Click;
            attachmentsPanel.Controls.Add(btnDownloadAttachment);

            // معلومات المرفقات
            // Attachments info
            Label infoLabel = new Label
            {
                Text = "اختر مرفق من القائمة لعرضه أو تحميله",
                Size = new Size(300, 20),
                Location = new Point(20, 175),
                ForeColor = Color.Gray,
                Font = new Font("Tahoma", 9F)
            };
            attachmentsPanel.Controls.Add(infoLabel);
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر طباعة الوثيقة
            // Print document button
            btnPrintDocument = new Button
            {
                Text = "طباعة الوثيقة",
                Size = new Size(120, 40),
                Location = new Point(350, 660),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnPrintDocument.FlatAppearance.BorderSize = 0;
            btnPrintDocument.Click += BtnPrintDocument_Click;
            this.Controls.Add(btnPrintDocument);

            // زر الإغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(120, 40),
                Location = new Point(490, 660),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);
        }

        /// <summary>
        /// تحميل بيانات الوثيقة
        /// Load document data
        /// </summary>
        private void LoadDocumentData()
        {
            try
            {
                if (currentDocument == null)
                {
                    MessageBox.Show("لا توجد بيانات وثيقة لعرضها", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // تحميل معلومات الوثيقة
                // Load document information
                lblSerialNumber.Text = currentDocument.SerialNumber ?? "غير محدد";
                lblDocumentType.Text = currentDocument.Type ?? "غير محدد";
                lblDocumentDate.Text = currentDocument.DocumentDate.ToString("dd/MM/yyyy");
                lblEntryDate.Text = currentDocument.EntryDate.ToString("dd/MM/yyyy HH:mm");
                lblSubject.Text = currentDocument.Subject ?? "غير محدد";
                lblFrom.Text = currentDocument.From ?? "غير محدد";
                lblTo.Text = currentDocument.To ?? "غير محدد";
                lblDepartment.Text = currentDocument.FileBox?.Department?.Name ?? "غير محدد";
                lblFileBox.Text = currentDocument.FileBox?.Name ?? "غير محدد";

                // تحميل المرفقات
                // Load attachments
                LoadAttachments();

                Logger.LogInfo(string.Format("تم تحميل تفاصيل الوثيقة: {0}", currentDocument.SerialNumber));
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل بيانات الوثيقة", ex);
                MessageBox.Show("حدث خطأ أثناء تحميل بيانات الوثيقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل المرفقات
        /// Load attachments
        /// </summary>
        private void LoadAttachments()
        {
            try
            {
                lstAttachments.Items.Clear();

                if (currentDocument.DocumentId > 0)
                {
                    var attachments = AttachmentService.GetDocumentAttachments(currentDocument.DocumentId);
                    
                    foreach (var attachment in attachments)
                    {
                        string displayText = string.Format("{0} ({attachment.FileType}) - {FormatFileSize(attachment.FileSize)}", attachment.FileName);
                        lstAttachments.Items.Add(new AttachmentItem { Attachment = attachment, DisplayText = displayText });
                    }
                }

                // إضافة مرفقات تجريبية للعرض
                // Add sample attachments for display
                if (lstAttachments.Items.Count == 0)
                {
                    lstAttachments.Items.Add(new AttachmentItem 
                    { 
                        DisplayText = "وثيقة_رسمية.pdf (PDF) - 2.5 ميجابايت",
                        Attachment = new Attachment { FileName = "وثيقة_رسمية.pdf", FileType = "PDF", FileSize = 2621440 }
                    });
                    lstAttachments.Items.Add(new AttachmentItem 
                    { 
                        DisplayText = "صورة_المرفق.jpg (JPG) - 1.2 ميجابايت",
                        Attachment = new Attachment { FileName = "صورة_المرفق.jpg", FileType = "JPG", FileSize = 1258291 }
                    });
                }

                lstAttachments.DisplayMember = "DisplayText";
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل المرفقات", ex);
            }
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// Format file size
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024)
                return string.Format("{0} بايت", bytes);
            else if (bytes < 1024 * 1024)
                return string.Format("{0} كيلوبايت", bytes / 1024.0:F1);
            else
                return string.Format("{0} ميجابايت", bytes / (1024.0 * 1024.0):F1);
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث تغيير اختيار المرفق
        /// Attachment selection change event handler
        /// </summary>
        private void LstAttachments_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool hasSelection = lstAttachments.SelectedIndex >= 0;
            btnViewAttachment.Enabled = hasSelection;
            btnDownloadAttachment.Enabled = hasSelection;
        }

        /// <summary>
        /// معالج حدث عرض المرفق
        /// View attachment event handler
        /// </summary>
        private void BtnViewAttachment_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstAttachments.SelectedItem is AttachmentItem item)
                {
                    MessageBox.Show(string.Format("عرض المرفق: {0}\n\n(سيتم تطوير عارض المرفقات لاحقاً)", item.Attachment.FileName), 
                        "عرض المرفق", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    Logger.LogInfo(string.Format("تم طلب عرض المرفق: {0}", item.Attachment.FileName));
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في عرض المرفق", ex);
                MessageBox.Show("حدث خطأ أثناء عرض المرفق", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث تحميل المرفق
        /// Download attachment event handler
        /// </summary>
        private void BtnDownloadAttachment_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstAttachments.SelectedItem is AttachmentItem item)
                {
                    using (SaveFileDialog saveDialog = new SaveFileDialog())
                    {
                        saveDialog.FileName = item.Attachment.FileName;
                        saveDialog.Filter = string.Format("ملفات {0}|*.{item.Attachment.FileType.ToLower()}|جميع الملفات|*.*", item.Attachment.FileType);

                        if (saveDialog.ShowDialog() == DialogResult.OK)
                        {
                            // محاكاة تحميل الملف
                            // Simulate file download
                            File.WriteAllText(saveDialog.FileName, string.Format("محتوى تجريبي للملف: {0}", item.Attachment.FileName));
                            
                            MessageBox.Show(string.Format("تم تحميل المرفق بنجاح!\nالمسار: {0}", saveDialog.FileName), 
                                "نجح التحميل", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                            Logger.LogInfo(string.Format("تم تحميل المرفق: {0}", item.Attachment.FileName));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل المرفق", ex);
                MessageBox.Show("حدث خطأ أثناء تحميل المرفق", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث طباعة الوثيقة
        /// Print document event handler
        /// </summary>
        private void BtnPrintDocument_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("وظيفة الطباعة قيد التطوير...\nسيتم إضافة إمكانية طباعة تفاصيل الوثيقة والمرفقات لاحقاً", 
                    "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                Logger.LogInfo(string.Format("تم طلب طباعة الوثيقة: {0}", currentDocument.SerialNumber));
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في طباعة الوثيقة", ex);
            }
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        /// <summary>
        /// عنصر مرفق في القائمة
        /// Attachment item in list
        /// </summary>
        private class AttachmentItem
        {
            public Attachment Attachment { get; set; }
            public string DisplayText { get; set; }
        }
    }
}

