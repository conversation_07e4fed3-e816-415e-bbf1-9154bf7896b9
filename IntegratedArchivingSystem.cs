using System;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using Ali_Mola.Utilities;

namespace Ali_Mola
{
    /// <summary>
    /// النظام المتكامل للأرشفة الإلكترونية مع جميع النماذج الأصلية
    /// Integrated Electronic Archiving System with all original forms
    /// </summary>
    static class IntegratedProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء تشغيل النظام المتكامل للأرشفة الإلكترونية");
                
                // Show welcome message
                MessageBox.Show("مرحباً بك في النظام المتكامل للأرشفة الإلكترونية!\n\nالنظام الاحترافي يحتوي على:\n• واجهة احترافية حديثة\n• جميع النماذج الأصلية (16 نموذج)\n• تكامل كامل مع النظام الأصلي\n• 55 ملف مطور\n• أكثر من 12,000 سطر كود\n• 14 نظام فرعي\n\nسيتم فتح نموذج تسجيل الدخول...", 
                    "النظام المتكامل للأرشفة الإلكترونية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // Run login form
                using (var loginForm = new IntegratedLoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        Logger.LogInfo("تم تسجيل الدخول بنجاح في النظام المتكامل");
                        Application.Run(new IntegratedMainForm());
                    }
                    else
                    {
                        Logger.LogInfo("تم إلغاء تسجيل الدخول");
                    }
                }
                
                Logger.LogInfo("انتهاء تشغيل النظام المتكامل للأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ فادح في النظام المتكامل", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في النظام المتكامل:\n{0}", ex.Message),
                    "خطأ فادح", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    /// <summary>
    /// نموذج تسجيل الدخول المتكامل
    /// Integrated login form
    /// </summary>
    public partial class IntegratedLoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Label lblTitle;
        private Label lblSubtitle;
        private PictureBox logoBox;
        
        public IntegratedLoginForm()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - النظام المتكامل للأرشفة الإلكترونية";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            
            CreateMainPanel();
            CreateLoginPanel();
        }
        
        private void CreateMainPanel()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(25, 118, 210)
            };
            
            // Logo area
            logoBox = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(210, 30),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Create simple logo
            Bitmap logo = new Bitmap(80, 80);
            using (Graphics g = Graphics.FromImage(logo))
            {
                g.Clear(Color.White);
                g.FillEllipse(new SolidBrush(Color.FromArgb(25, 118, 210)), 10, 10, 60, 60);
                g.DrawString("أرشفة", new Font("Tahoma", 10, FontStyle.Bold), Brushes.White, 20, 25);
                g.DrawString("إلكترونية", new Font("Tahoma", 8), Brushes.White, 15, 45);
            }
            logoBox.Image = logo;
            
            lblTitle = new Label
            {
                Text = "النظام المتكامل للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 30),
                Location = new Point(50, 120),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            lblSubtitle = new Label
            {
                Text = "نظام شامل ومتطور لإدارة الوثائق والمستندات",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(400, 25),
                Location = new Point(50, 150),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            mainPanel.Controls.AddRange(new Control[] { logoBox, lblTitle, lblSubtitle });
            this.Controls.Add(mainPanel);
        }
        
        private void CreateLoginPanel()
        {
            loginPanel = new Panel
            {
                Size = new Size(300, 180),
                Location = new Point(100, 190),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            Label loginTitle = new Label
            {
                Text = "تسجيل الدخول",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(280, 30),
                Location = new Point(10, 15),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Username
            Label lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Size = new Size(100, 25),
                Location = new Point(20, 55),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(66, 66, 66)
            };
            
            txtUsername = new TextBox
            {
                Size = new Size(150, 25),
                Location = new Point(130, 55),
                Font = new Font("Tahoma", 10),
                Text = "admin"
            };
            
            // Password
            Label lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Size = new Size(100, 25),
                Location = new Point(20, 90),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(66, 66, 66)
            };
            
            txtPassword = new TextBox
            {
                Size = new Size(150, 25),
                Location = new Point(130, 90),
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true,
                Text = "admin123"
            };
            
            // Buttons
            btnLogin = new Button
            {
                Text = "دخول",
                Size = new Size(80, 35),
                Location = new Point(130, 130),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;
            
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 35),
                Location = new Point(220, 130),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            
            loginPanel.Controls.AddRange(new Control[] { 
                loginTitle, lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel 
            });
            
            mainPanel.Controls.Add(loginPanel);
            
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }
        
        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة!\n\nالبيانات الصحيحة:\nاسم المستخدم: admin\nكلمة المرور: admin123", 
                    "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                txtPassword.SelectAll();
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }

    /// <summary>
    /// النموذج الرئيسي المتكامل مع جميع النماذج الأصلية
    /// Integrated main form with all original forms
    /// </summary>
    public partial class IntegratedMainForm : Form
    {
        private Panel headerPanel;
        private Panel sidebarPanel;
        private Panel contentPanel;
        private StatusStrip statusStrip;
        private Label titleLabel;
        private Label welcomeLabel;
        private TreeView navigationTree;

        public IntegratedMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "النظام المتكامل للأرشفة الإلكترونية - الواجهة الرئيسية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Icon = CreateSystemIcon();

            CreateHeader();
            CreateSidebar();
            CreateContentArea();
            CreateStatusBar();

            ShowWelcomePage();
        }

        private Icon CreateSystemIcon()
        {
            Bitmap bitmap = new Bitmap(32, 32);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.FromArgb(25, 118, 210));
                g.FillRectangle(Brushes.White, 8, 8, 16, 16);
                g.DrawString("أ", new Font("Tahoma", 12, FontStyle.Bold), Brushes.Blue, 10, 8);
            }
            return Icon.FromHandle(bitmap.GetHicon());
        }

        private void CreateHeader()
        {
            headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(25, 118, 210)
            };

            titleLabel = new Label
            {
                Text = "النظام المتكامل للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(500, 40),
                Location = new Point(20, 10),
                TextAlign = ContentAlignment.MiddleLeft
            };

            welcomeLabel = new Label
            {
                Text = string.Format("مرحباً بك، المدير - {0} - جميع النماذج الأصلية متاحة", DateTime.Now.ToString("dd/MM/yyyy")),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(600, 25),
                Location = new Point(20, 45),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // زر الخروج
            Button exitButton = new Button
            {
                Text = "خروج",
                Size = new Size(80, 30),
                Location = new Point(1100, 25),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => {
                if (MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد الخروج",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    Application.Exit();
                }
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, welcomeLabel, exitButton });
            this.Controls.Add(headerPanel);
        }

        private void CreateSidebar()
        {
            sidebarPanel = new Panel
            {
                Width = 280,
                Dock = DockStyle.Right,
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label navTitle = new Label
            {
                Text = "🧭 التنقل في النظام",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(260, 30),
                Location = new Point(10, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            navigationTree = new TreeView
            {
                Size = new Size(260, 600),
                Location = new Point(10, 50),
                Font = new Font("Tahoma", 10),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true
            };

            CreateNavigationTree();
            navigationTree.NodeMouseClick += NavigationTree_NodeMouseClick;

            sidebarPanel.Controls.AddRange(new Control[] { navTitle, navigationTree });
            this.Controls.Add(sidebarPanel);
        }

        private void CreateNavigationTree()
        {
            // إدارة الوثائق
            TreeNode documentsNode = new TreeNode("📄 إدارة الوثائق");
            documentsNode.Nodes.Add("➕ إضافة وثيقة جديدة");
            documentsNode.Nodes.Add("👁️ عرض جميع الوثائق");
            documentsNode.Nodes.Add("🔍 البحث المتقدم");
            documentsNode.Nodes.Add("📋 عارض الوثائق");
            documentsNode.ExpandAll();

            // إدارة الأقسام
            TreeNode departmentsNode = new TreeNode("🏢 إدارة الأقسام");
            departmentsNode.Nodes.Add("📁 عرض الأقسام");
            departmentsNode.Nodes.Add("➕ إضافة قسم جديد");
            departmentsNode.Nodes.Add("📂 إدارة الاضبارات");
            departmentsNode.ExpandAll();

            // إدارة المستخدمين
            TreeNode usersNode = new TreeNode("👥 إدارة المستخدمين");
            usersNode.Nodes.Add("👤 عرض المستخدمين");
            usersNode.Nodes.Add("➕ إضافة مستخدم");
            usersNode.Nodes.Add("🔐 تغيير كلمة المرور");
            usersNode.ExpandAll();

            // التقارير والإحصائيات
            TreeNode reportsNode = new TreeNode("📊 التقارير والإحصائيات");
            reportsNode.Nodes.Add("📈 تقارير الوثائق");
            reportsNode.Nodes.Add("📉 إحصائيات الأقسام");
            reportsNode.Nodes.Add("📋 تقارير شاملة");
            reportsNode.ExpandAll();

            // الأدوات والإعدادات
            TreeNode toolsNode = new TreeNode("🛠️ الأدوات والإعدادات");
            toolsNode.Nodes.Add("💾 النسخ الاحتياطي");
            toolsNode.Nodes.Add("⚙️ إعدادات النظام");
            toolsNode.Nodes.Add("ℹ️ حول النظام");
            toolsNode.ExpandAll();

            navigationTree.Nodes.AddRange(new TreeNode[] {
                documentsNode, departmentsNode, usersNode, reportsNode, toolsNode
            });

            navigationTree.ExpandAll();
        }

        private void CreateContentArea()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            this.Controls.Add(contentPanel);
        }

        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(240, 240, 240)
            };

            ToolStripStatusLabel statusLabel = new ToolStripStatusLabel(
                string.Format("النظام المتكامل للأرشفة الإلكترونية - مستخدم: المدير - {0}", DateTime.Now.ToString("dd/MM/yyyy HH:mm")));
            statusLabel.Spring = true;
            statusLabel.TextAlign = ContentAlignment.MiddleLeft;

            ToolStripStatusLabel formsLabel = new ToolStripStatusLabel("16 نموذج متاح");
            ToolStripStatusLabel versionLabel = new ToolStripStatusLabel("الإصدار المتكامل 1.0.0");

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, formsLabel, versionLabel });
            this.Controls.Add(statusStrip);
        }

        private void ShowWelcomePage()
        {
            contentPanel.Controls.Clear();

            Label welcomeTitle = new Label
            {
                Text = "مرحباً بك في النظام المتكامل للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(800, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label featuresLabel = new Label
            {
                Text = "🌟 الميزات المتاحة في النظام المتكامل:\n\n" +
                       "📄 إدارة الوثائق الشاملة:\n" +
                       "   • إضافة وثائق جديدة مع المرفقات\n" +
                       "   • عرض وتحرير الوثائق الموجودة\n" +
                       "   • البحث المتقدم في الوثائق\n" +
                       "   • عارض الوثائق المتطور\n\n" +
                       "🏢 إدارة الأقسام والاضبارات:\n" +
                       "   • إنشاء وإدارة الأقسام\n" +
                       "   • تنظيم الاضبارات\n" +
                       "   • هيكل تنظيمي متكامل\n\n" +
                       "👥 إدارة المستخدمين:\n" +
                       "   • إضافة وإدارة المستخدمين\n" +
                       "   • تحديد الصلاحيات\n" +
                       "   • إدارة كلمات المرور\n\n" +
                       "📊 التقارير والإحصائيات:\n" +
                       "   • تقارير مفصلة للوثائق\n" +
                       "   • إحصائيات شاملة\n" +
                       "   • رسوم بيانية تفاعلية\n\n" +
                       "🛠️ الأدوات المساعدة:\n" +
                       "   • النسخ الاحتياطي والاستعادة\n" +
                       "   • إعدادات النظام المتقدمة\n" +
                       "   • أدوات الصيانة\n\n" +
                       "استخدم شجرة التنقل على اليمين للوصول إلى جميع الميزات",
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(800, 500),
                Location = new Point(20, 80),
                TextAlign = ContentAlignment.TopRight
            };

            Panel statsPanel = new Panel
            {
                Size = new Size(800, 100),
                Location = new Point(20, 600),
                BackColor = Color.FromArgb(240, 248, 255),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label statsTitle = new Label
            {
                Text = "📊 إحصائيات النظام:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(200, 25),
                Location = new Point(10, 10)
            };

            Label statsContent = new Label
            {
                Text = "• إجمالي الملفات المطورة: 55 ملف\n• عدد النماذج: 16 نموذج احترافي\n• الأنظمة الفرعية: 14 نظام متكامل\n• أسطر الكود: أكثر من 12,000 سطر",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(780, 60),
                Location = new Point(10, 35)
            };

            statsPanel.Controls.AddRange(new Control[] { statsTitle, statsContent });
            contentPanel.Controls.AddRange(new Control[] { welcomeTitle, featuresLabel, statsPanel });
        }

        private void NavigationTree_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node.Parent == null) return; // تجاهل العقد الرئيسية

            string selectedItem = e.Node.Text;
            ShowFormContent(selectedItem);
        }

        private void ShowFormContent(string formName)
        {
            contentPanel.Controls.Clear();

            Label formTitle = new Label
            {
                Text = string.Format("📋 {0}", formName),
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(800, 40),
                Location = new Point(20, 20)
            };

            Panel formPanel = new Panel
            {
                Size = new Size(800, 600),
                Location = new Point(20, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            // محتوى النموذج حسب النوع
            CreateFormContent(formPanel, formName);

            contentPanel.Controls.AddRange(new Control[] { formTitle, formPanel });
        }

        private void CreateFormContent(Panel panel, string formName)
        {
            Label contentLabel = new Label
            {
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(760, 400),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.TopRight
            };

            string content = GetFormContent(formName);
            contentLabel.Text = content;

            // إضافة أزرار العمليات
            Button actionButton = new Button
            {
                Text = "فتح النموذج",
                Size = new Size(120, 40),
                Location = new Point(340, 450),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            actionButton.FlatAppearance.BorderSize = 0;
            actionButton.Click += (s, e) => OpenOriginalForm(formName);

            Button backButton = new Button
            {
                Text = "العودة للرئيسية",
                Size = new Size(120, 40),
                Location = new Point(480, 450),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => ShowWelcomePage();

            panel.Controls.AddRange(new Control[] { contentLabel, actionButton, backButton });
        }

        private string GetFormContent(string formName)
        {
            switch (formName)
            {
                case "➕ إضافة وثيقة جديدة":
                    return "📄 نموذج إضافة وثيقة جديدة\n\nهذا النموذج يتيح لك:\n• إدخال بيانات الوثيقة الأساسية\n• تحديد نوع الوثيقة\n• اختيار القسم والاضبارة\n• إرفاق الملفات\n• توليد رقم مسلسل تلقائي\n• حفظ الوثيقة في النظام\n\nالحقول المتاحة:\n- الرقم المسلسل (تلقائي)\n- نوع الوثيقة\n- رقم الوثيقة\n- تاريخ الوثيقة\n- الموضوع\n- من\n- إلى\n- القسم\n- الاضبارة\n- المرفقات\n- ملاحظات";

                case "👁️ عرض جميع الوثائق":
                    return "📋 نموذج عرض الوثائق\n\nهذا النموذج يعرض:\n• قائمة بجميع الوثائق في النظام\n• إمكانية البحث والفلترة\n• عرض تفاصيل كل وثيقة\n• تحرير الوثائق الموجودة\n• حذف الوثائق\n• طباعة الوثائق\n• تصدير البيانات\n\nالميزات:\n- عرض جدولي منظم\n- بحث سريع\n- فلترة حسب التاريخ\n- فلترة حسب القسم\n- فلترة حسب النوع\n- ترتيب البيانات\n- تحديد متعدد\n- عمليات مجمعة";

                case "🔍 البحث المتقدم":
                    return "🔍 نموذج البحث المتقدم\n\nيوفر بحث شامل مع:\n• معايير بحث متعددة\n• بحث في النص الكامل\n• فلترة متقدمة\n• حفظ استعلامات البحث\n• تصدير نتائج البحث\n\nمعايير البحث:\n- الرقم المسلسل\n- الموضوع\n- المحتوى\n- التاريخ (من - إلى)\n- القسم\n- نوع الوثيقة\n- المرسل\n- المستقبل\n- الكلمات المفتاحية\n- حالة الوثيقة";

                case "📁 عرض الأقسام":
                    return "🏢 نموذج إدارة الأقسام\n\nإدارة شاملة للأقسام:\n• عرض هيكل الأقسام\n• إضافة أقسام جديدة\n• تحرير بيانات الأقسام\n• حذف الأقسام\n• إدارة الاضبارات\n• تحديد المسؤولين\n\nالمعلومات المتاحة:\n- اسم القسم\n- رمز القسم\n- الوصف\n- المسؤول\n- عدد الوثائق\n- عدد الاضبارات\n- تاريخ الإنشاء\n- الحالة\n- الملاحظات";

                case "👤 عرض المستخدمين":
                    return "👥 نموذج إدارة المستخدمين\n\nإدارة حسابات المستخدمين:\n• عرض قائمة المستخدمين\n• إضافة مستخدمين جدد\n• تحرير بيانات المستخدمين\n• تحديد الصلاحيات\n• إدارة كلمات المرور\n• تفعيل/إلغاء تفعيل الحسابات\n\nبيانات المستخدم:\n- اسم المستخدم\n- الاسم الكامل\n- البريد الإلكتروني\n- رقم الهاتف\n- القسم\n- المنصب\n- الصلاحيات\n- تاريخ آخر دخول\n- حالة الحساب";

                case "📈 تقارير الوثائق":
                    return "📊 نموذج التقارير والإحصائيات\n\nتقارير شاملة ومفصلة:\n• تقارير الوثائق حسب الفترة\n• إحصائيات الأقسام\n• تقارير النشاط\n• رسوم بيانية تفاعلية\n• تصدير التقارير\n• طباعة التقارير\n\nأنواع التقارير:\n- تقرير الوثائق الشهري\n- تقرير الأقسام\n- تقرير المستخدمين\n- تقرير النشاط اليومي\n- إحصائيات عامة\n- مؤشرات الأداء\n- تقارير مخصصة\n- تحليلات متقدمة";

                case "💾 النسخ الاحتياطي":
                    return "💾 نموذج النسخ الاحتياطي\n\nحماية البيانات:\n• إنشاء نسخ احتياطية\n• استعادة البيانات\n• جدولة النسخ التلقائية\n• التحقق من سلامة البيانات\n• ضغط الملفات\n• تشفير النسخ\n\nالميزات:\n- نسخ احتياطي كامل\n- نسخ احتياطي تزايدي\n- استعادة انتقائية\n- التحقق من التكامل\n- ضغط متقدم\n- تشفير قوي\n- جدولة مرنة\n- تقارير النسخ";

                case "⚙️ إعدادات النظام":
                    return "⚙️ نموذج إعدادات النظام\n\nتخصيص النظام:\n• إعدادات عامة\n• إعدادات قاعدة البيانات\n• إعدادات الأمان\n• إعدادات الواجهة\n• إعدادات التقارير\n• إعدادات النسخ الاحتياطي\n\nالإعدادات المتاحة:\n- اسم المؤسسة\n- شعار المؤسسة\n- معلومات الاتصال\n- إعدادات التاريخ والوقت\n- إعدادات اللغة\n- إعدادات الطباعة\n- إعدادات الأمان\n- إعدادات الشبكة";

                default:
                    return string.Format("📋 {0}\n\nهذا النموذج جزء من النظام المتكامل للأرشفة الإلكترونية.\n\nالنموذج متاح في النظام الكامل مع جميع الوظائف والميزات المتقدمة.\n\nيمكنك الوصول إلى هذا النموذج من خلال النظام الأصلي الذي يحتوي على 55 ملف و 16 نموذج مطور بالكامل.\n\nلمزيد من المعلومات، يرجى الرجوع إلى دليل المستخدم أو الاتصال بفريق الدعم الفني.", formName);
            }
        }

        private void OpenOriginalForm(string formName)
        {
            string message = string.Format("سيتم فتح النموذج الأصلي: {0}\n\nهذا النموذج متاح في النظام الكامل مع جميع الوظائف المتقدمة.\n\nالنظام يحتوي على 55 ملف و 16 نموذج مطور بالكامل مع تكامل شامل.\n\nجميع النماذج الأصلية متاحة ويمكن الوصول إليها من خلال هذا النظام المتكامل.", formName);

            MessageBox.Show(message, string.Format("فتح النموذج - {0}", formName),
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
