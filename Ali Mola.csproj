<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1EEB47F1-44A7-40BF-82B3-57848C8075A4}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Ali_Mola</RootNamespace>
    <AssemblyName>Ali <PERSON>la</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BusinessLogic\AttachmentService.cs" />
    <Compile Include="BusinessLogic\AuthenticationService.cs" />
    <Compile Include="BusinessLogic\BackupService.cs" />
    <Compile Include="BusinessLogic\DepartmentService.cs" />
    <Compile Include="BusinessLogic\DocumentService.cs" />
    <Compile Include="DataAccess\ArchivingSystemContext.cs" />
    <Compile Include="DataAccess\SimpleDataContext.cs" />
    <Compile Include="DataAccess\Models\Attachment.cs" />
    <Compile Include="DataAccess\Models\Department.cs" />
    <Compile Include="DataAccess\Models\Document.cs" />
    <Compile Include="DataAccess\Models\FileBox.cs" />
    <Compile Include="DataAccess\Models\User.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AddDocumentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddDocumentForm.Designer.cs">
      <DependentUpon>Forms\AddDocumentForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AddDepartmentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AddDepartmentForm.Designer.cs">
      <DependentUpon>Forms\AddDepartmentForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ChangePasswordForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ChangePasswordForm.Designer.cs">
      <DependentUpon>Forms\ChangePasswordForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DepartmentsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DepartmentsForm.Designer.cs">
      <DependentUpon>Forms\DepartmentsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DocumentViewerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DocumentViewerForm.Designer.cs">
      <DependentUpon>Forms\DocumentViewerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>Forms\LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.cs">
      <DependentUpon>Forms\MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SettingsForm.Designer.cs">
      <DependentUpon>Forms\SettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ViewDocumentsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ViewDocumentsForm.Designer.cs">
      <DependentUpon>Forms\ViewDocumentsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utilities\Logger.cs" />
    <Compile Include="Utilities\PasswordHelper.cs" />
    <Compile Include="Utilities\SerialNumberGenerator.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>