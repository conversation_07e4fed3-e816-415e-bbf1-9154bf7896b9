using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ali_Mola.DataAccess.Models
{
    /// <summary>
    /// نموذج الاضبارة - يمثل الاضبارات في النظام
    /// FileBox Model - Represents file boxes in the system
    /// </summary>
    [Table("FileBoxes")]
    public class FileBox
    {
        /// <summary>
        /// معرف الاضبارة الفريد
        /// Unique file box identifier
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int FileBoxId { get; set; }

        /// <summary>
        /// معرف القسم التابع له الاضبارة
        /// Department ID that owns this file box
        /// </summary>
        [Required]
        [Display(Name = "القسم")]
        public int DepartmentId { get; set; }

        /// <summary>
        /// اسم الاضبارة
        /// File box name
        /// </summary>
        [Required(ErrorMessage = "اسم الاضبارة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الاضبارة يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم الاضبارة")]
        public string Name { get; set; }

        /// <summary>
        /// تاريخ إنشاء الاضبارة
        /// File box creation date
        /// </summary>
        [Required]
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// القسم التابع له الاضبارة
        /// Department that owns this file box
        /// </summary>
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; }

        /// <summary>
        /// مجموعة الوثائق التابعة لهذه الاضبارة
        /// Collection of documents belonging to this file box
        /// </summary>
        public virtual ICollection<Document> Documents { get; set; }

        /// <summary>
        /// منشئ الاضبارة
        /// FileBox constructor
        /// </summary>
        public FileBox()
        {
            Documents = new HashSet<Document>();
            CreatedDate = DateTime.Now;
        }
    }
}
