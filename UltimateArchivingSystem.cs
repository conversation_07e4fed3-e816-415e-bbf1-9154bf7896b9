using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.Collections.Generic;
using System.Linq;
using Ali_Mola.Utilities;

namespace Ali_Mola
{
    /// <summary>
    /// النظام النهائي المتقدم للأرشفة الإلكترونية
    /// Ultimate Advanced Electronic Archiving System
    /// </summary>
    static class UltimateProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء تشغيل النظام النهائي المتقدم للأرشفة الإلكترونية");
                
                // Show splash screen with advanced loading
                using (var splashForm = new AdvancedSplashForm())
                {
                    splashForm.ShowDialog();
                }
                
                // Run ultimate login form
                using (var loginForm = new UltimateLoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        Logger.LogInfo("تم تسجيل الدخول بنجاح في النظام النهائي المتقدم");
                        
                        // Show advanced welcome animation
                        using (var welcomeForm = new AdvancedWelcomeForm())
                        {
                            welcomeForm.ShowDialog();
                        }
                        
                        // Run ultimate main application
                        Application.Run(new UltimateMainForm());
                    }
                    else
                    {
                        Logger.LogInfo("تم إلغاء تسجيل الدخول");
                    }
                }
                
                Logger.LogInfo("انتهاء تشغيل النظام النهائي المتقدم للأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ فادح في النظام النهائي المتقدم", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في النظام النهائي المتقدم:\n{0}\n\nسيتم إغلاق النظام للحماية.", ex.Message),
                    "خطأ فادح - النظام المتقدم", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    /// <summary>
    /// شاشة البدء المتقدمة مع شريط التحميل
    /// Advanced splash screen with loading progress
    /// </summary>
    public partial class AdvancedSplashForm : Form
    {
        private System.Windows.Forms.Timer loadingTimer;
        private ProgressBar progressBar;
        private Label statusLabel;
        private Label titleLabel;
        private PictureBox logoBox;
        private int loadingStep = 0;
        private string[] loadingMessages = {
            "تهيئة النظام المتقدم...",
            "تحميل المكونات الأساسية...",
            "تحضير الواجهة المتطورة...",
            "تفعيل الميزات المتقدمة...",
            "إعداد نظام الأمان...",
            "تحميل قاعدة البيانات...",
            "تجهيز النظام للاستخدام...",
            "اكتمال التحميل..."
        };
        
        public AdvancedSplashForm()
        {
            InitializeComponent();
            SetupLoadingAnimation();
        }
        
        private void InitializeComponent()
        {
            this.Text = "النظام النهائي المتقدم للأرشفة الإلكترونية";
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(15, 76, 129);
            this.ShowInTaskbar = false;
            this.TopMost = true;
            
            CreateSplashContent();
        }
        
        private void CreateSplashContent()
        {
            // Advanced gradient background
            this.Paint += (s, e) => {
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    this.ClientRectangle,
                    Color.FromArgb(15, 76, 129),
                    Color.FromArgb(25, 118, 210),
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, this.ClientRectangle);
                }
            };
            
            // Advanced logo with glow effect
            logoBox = new PictureBox
            {
                Size = new Size(150, 150),
                Location = new Point(225, 50),
                BackColor = Color.Transparent
            };
            
            Bitmap advancedLogo = new Bitmap(150, 150);
            using (Graphics g = Graphics.FromImage(advancedLogo))
            {
                g.Clear(Color.Transparent);
                g.SmoothingMode = SmoothingMode.AntiAlias;
                
                // Glow effect
                using (GraphicsPath path = new GraphicsPath())
                {
                    path.AddEllipse(10, 10, 130, 130);
                    using (PathGradientBrush glow = new PathGradientBrush(path))
                    {
                        glow.CenterColor = Color.FromArgb(100, Color.White);
                        glow.SurroundColors = new Color[] { Color.Transparent };
                        g.FillPath(glow, path);
                    }
                }
                
                // Main logo circle
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    new Rectangle(20, 20, 110, 110),
                    Color.FromArgb(255, 255, 255),
                    Color.FromArgb(200, 230, 255),
                    LinearGradientMode.Vertical))
                {
                    g.FillEllipse(brush, 20, 20, 110, 110);
                }
                
                // Inner circle
                using (SolidBrush innerBrush = new SolidBrush(Color.FromArgb(25, 118, 210)))
                {
                    g.FillEllipse(innerBrush, 35, 35, 80, 80);
                }
                
                // Logo text
                using (Font logoFont = new Font("Tahoma", 16, FontStyle.Bold))
                {
                    g.DrawString("أرشفة", logoFont, Brushes.White, 50, 55);
                    g.DrawString("متقدمة", new Font("Tahoma", 12), Brushes.White, 45, 85);
                }
            }
            logoBox.Image = advancedLogo;
            
            // Advanced title with shadow
            titleLabel = new Label
            {
                Text = "النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(550, 40),
                Location = new Point(25, 220),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            // Subtitle
            Label subtitleLabel = new Label
            {
                Text = "الإصدار 3.0.0 - النسخة النهائية المتطورة",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(550, 25),
                Location = new Point(25, 260),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            // Advanced progress bar
            progressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(100, 300),
                Style = ProgressBarStyle.Continuous,
                ForeColor = Color.FromArgb(76, 175, 80),
                BackColor = Color.FromArgb(50, 50, 50)
            };
            
            // Status label
            statusLabel = new Label
            {
                Text = "تهيئة النظام المتقدم...",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(400, 25),
                Location = new Point(100, 330),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            this.Controls.AddRange(new Control[] { logoBox, titleLabel, subtitleLabel, progressBar, statusLabel });
        }
        
        private void SetupLoadingAnimation()
        {
            loadingTimer = new System.Windows.Forms.Timer();
            loadingTimer.Interval = 500; // Slower for better UX
            loadingTimer.Tick += LoadingTimer_Tick;
            loadingTimer.Start();
        }
        
        private void LoadingTimer_Tick(object sender, EventArgs e)
        {
            if (loadingStep < loadingMessages.Length)
            {
                statusLabel.Text = loadingMessages[loadingStep];
                progressBar.Value = (int)((loadingStep + 1) * 100.0 / loadingMessages.Length);
                loadingStep++;
            }
            else
            {
                loadingTimer.Stop();
                loadingTimer.Dispose();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }
    }
    
    /// <summary>
    /// نموذج تسجيل الدخول النهائي المتقدم
    /// Ultimate advanced login form
    /// </summary>
    public partial class UltimateLoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private Panel headerPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Button btnForgotPassword;
        private Button btnCreateAccount;
        private Button btnHelp;
        private Label lblTitle;
        private Label lblSubtitle;
        private PictureBox logoBox;
        private System.Windows.Forms.Timer fadeTimer;
        private System.Windows.Forms.Timer pulseTimer;
        private int fadeStep = 0;
        private bool pulseDirection = true;
        private int pulseStep = 0;
        
        public UltimateLoginForm()
        {
            InitializeComponent();
            SetupAdvancedAnimations();
        }
        
        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - النظام النهائي المتقدم";
            this.Size = new Size(700, 550);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Opacity = 0;
            
            CreateAdvancedLayout();
            CreateAdvancedHeader();
            CreateAdvancedLoginPanel();
            CreateAdvancedActionButtons();
        }
        
        private void SetupAdvancedAnimations()
        {
            // Fade-in animation
            fadeTimer = new System.Windows.Forms.Timer();
            fadeTimer.Interval = 30;
            fadeTimer.Tick += FadeTimer_Tick;
            fadeTimer.Start();
            
            // Pulse animation for logo
            pulseTimer = new System.Windows.Forms.Timer();
            pulseTimer.Interval = 100;
            pulseTimer.Tick += PulseTimer_Tick;
            pulseTimer.Start();
        }
        
        private void FadeTimer_Tick(object sender, EventArgs e)
        {
            fadeStep++;
            this.Opacity = Math.Min(1.0, fadeStep * 0.03);
            
            if (this.Opacity >= 1.0)
            {
                fadeTimer.Stop();
                fadeTimer.Dispose();
            }
        }
        
        private void PulseTimer_Tick(object sender, EventArgs e)
        {
            if (pulseDirection)
            {
                pulseStep++;
                if (pulseStep >= 20) pulseDirection = false;
            }
            else
            {
                pulseStep--;
                if (pulseStep <= 0) pulseDirection = true;
            }
            
            // Apply pulse effect to logo
            if (logoBox != null)
            {
                int newSize = 120 + (pulseStep * 2);
                logoBox.Size = new Size(newSize, newSize);
                logoBox.Location = new Point(290 - (newSize - 120) / 2, 30 - (newSize - 120) / 2);
            }
        }
        
        private void CreateAdvancedLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            
            // Advanced border with gradient
            mainPanel.Paint += (s, e) => {
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    mainPanel.ClientRectangle,
                    Color.FromArgb(240, 248, 255),
                    Color.White,
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, mainPanel.ClientRectangle);
                }
                
                using (Pen pen = new Pen(Color.FromArgb(25, 118, 210), 3))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, mainPanel.Width - 1, mainPanel.Height - 1);
                }
            };
            
            this.Controls.Add(mainPanel);
        }
        
        private void CreateAdvancedHeader()
        {
            headerPanel = new Panel
            {
                Size = new Size(680, 180),
                Location = new Point(10, 10),
                BackColor = Color.Transparent
            };
            
            // Advanced gradient header
            headerPanel.Paint += (s, e) => {
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    headerPanel.ClientRectangle,
                    Color.FromArgb(25, 118, 210),
                    Color.FromArgb(15, 76, 129),
                    LinearGradientMode.Horizontal))
                {
                    e.Graphics.FillRectangle(brush, headerPanel.ClientRectangle);
                }
            };
            
            // Advanced logo with animation placeholder
            logoBox = new PictureBox
            {
                Size = new Size(120, 120),
                Location = new Point(290, 30),
                BackColor = Color.Transparent
            };
            
            Bitmap ultimateLogo = new Bitmap(120, 120);
            using (Graphics g = Graphics.FromImage(ultimateLogo))
            {
                g.Clear(Color.Transparent);
                g.SmoothingMode = SmoothingMode.AntiAlias;
                
                // Outer glow
                using (GraphicsPath path = new GraphicsPath())
                {
                    path.AddEllipse(5, 5, 110, 110);
                    using (PathGradientBrush glow = new PathGradientBrush(path))
                    {
                        glow.CenterColor = Color.FromArgb(150, Color.Gold);
                        glow.SurroundColors = new Color[] { Color.Transparent };
                        g.FillPath(glow, path);
                    }
                }
                
                // Main circle
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    new Rectangle(10, 10, 100, 100),
                    Color.White,
                    Color.FromArgb(240, 248, 255),
                    LinearGradientMode.Vertical))
                {
                    g.FillEllipse(brush, 10, 10, 100, 100);
                }
                
                // Inner design
                using (SolidBrush innerBrush = new SolidBrush(Color.FromArgb(25, 118, 210)))
                {
                    g.FillEllipse(innerBrush, 25, 25, 70, 70);
                }
                
                // Ultimate logo text
                using (Font logoFont = new Font("Tahoma", 14, FontStyle.Bold))
                {
                    g.DrawString("أرشفة", logoFont, Brushes.White, 35, 40);
                    g.DrawString("نهائية", new Font("Tahoma", 10), Brushes.White, 35, 65);
                }
            }
            logoBox.Image = ultimateLogo;
            
            lblTitle = new Label
            {
                Text = "النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(600, 35),
                Location = new Point(40, 100),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            lblSubtitle = new Label
            {
                Text = "الإصدار 3.0.0 - نظام متطور وشامل لإدارة الوثائق بأحدث التقنيات",
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(600, 30),
                Location = new Point(40, 135),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            headerPanel.Controls.AddRange(new Control[] { logoBox, lblTitle, lblSubtitle });
            mainPanel.Controls.Add(headerPanel);
        }

        private void CreateAdvancedLoginPanel()
        {
            loginPanel = new Panel
            {
                Size = new Size(450, 220),
                Location = new Point(125, 210),
                BackColor = Color.Transparent
            };

            // Advanced panel with shadow effect
            loginPanel.Paint += (s, e) => {
                // Shadow effect
                using (SolidBrush shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, 5, 5, loginPanel.Width - 5, loginPanel.Height - 5);
                }

                // Main panel background
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    new Rectangle(0, 0, loginPanel.Width - 5, loginPanel.Height - 5),
                    Color.FromArgb(250, 250, 250),
                    Color.White,
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, 0, 0, loginPanel.Width - 5, loginPanel.Height - 5);
                }

                // Border
                using (Pen pen = new Pen(Color.FromArgb(200, 200, 200), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, loginPanel.Width - 6, loginPanel.Height - 6);
                }
            };

            Label loginTitle = new Label
            {
                Text = "🔐 تسجيل الدخول الآمن",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(420, 35),
                Location = new Point(15, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Advanced username field
            Label lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Size = new Size(130, 25),
                Location = new Point(30, 70),
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(66, 66, 66),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtUsername = new TextBox
            {
                Size = new Size(220, 30),
                Location = new Point(170, 70),
                Font = new Font("Tahoma", 11),
                Text = "admin",
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(248, 248, 248)
            };

            // Advanced password field
            Label lblPassword = new Label
            {
                Text = "🔒 كلمة المرور:",
                Size = new Size(130, 25),
                Location = new Point(30, 110),
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(66, 66, 66),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtPassword = new TextBox
            {
                Size = new Size(220, 30),
                Location = new Point(170, 110),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                Text = "admin123",
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(248, 248, 248)
            };

            // Advanced login and cancel buttons
            btnLogin = new Button
            {
                Text = "🚀 دخول",
                Size = new Size(100, 40),
                Location = new Point(170, 160),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.FlatAppearance.MouseOverBackColor = Color.FromArgb(56, 155, 60);
            btnLogin.Click += BtnLogin_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(290, 160),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.FlatAppearance.MouseOverBackColor = Color.FromArgb(138, 138, 138);
            btnCancel.Click += BtnCancel_Click;

            loginPanel.Controls.AddRange(new Control[] {
                loginTitle, lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel
            });

            mainPanel.Controls.Add(loginPanel);

            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }

        private void CreateAdvancedActionButtons()
        {
            // Advanced forgot password button
            btnForgotPassword = new Button
            {
                Text = "🔑 نسيت كلمة المرور",
                Size = new Size(160, 40),
                Location = new Point(120, 450),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnForgotPassword.FlatAppearance.BorderSize = 0;
            btnForgotPassword.FlatAppearance.MouseOverBackColor = Color.FromArgb(235, 132, 0);
            btnForgotPassword.Click += BtnForgotPassword_Click;

            // Advanced create account button
            btnCreateAccount = new Button
            {
                Text = "👤 إنشاء حساب جديد",
                Size = new Size(160, 40),
                Location = new Point(300, 450),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCreateAccount.FlatAppearance.BorderSize = 0;
            btnCreateAccount.FlatAppearance.MouseOverBackColor = Color.FromArgb(13, 130, 223);
            btnCreateAccount.Click += BtnCreateAccount_Click;

            // Advanced help button
            btnHelp = new Button
            {
                Text = "❓ مساعدة",
                Size = new Size(100, 40),
                Location = new Point(480, 450),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnHelp.FlatAppearance.BorderSize = 0;
            btnHelp.FlatAppearance.MouseOverBackColor = Color.FromArgb(136, 19, 156);
            btnHelp.Click += BtnHelp_Click;

            mainPanel.Controls.AddRange(new Control[] { btnForgotPassword, btnCreateAccount, btnHelp });
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("❌ اسم المستخدم أو كلمة المرور غير صحيحة!\n\n✅ البيانات الصحيحة:\n👤 اسم المستخدم: admin\n🔒 كلمة المرور: admin123\n\n💡 تأكد من كتابة البيانات بالضبط كما هو موضح.",
                    "خطأ في تسجيل الدخول - النظام المتقدم", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                txtPassword.SelectAll();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnForgotPassword_Click(object sender, EventArgs e)
        {
            using (var forgotForm = new AdvancedForgotPasswordForm())
            {
                forgotForm.ShowDialog();
            }
        }

        private void BtnCreateAccount_Click(object sender, EventArgs e)
        {
            using (var createForm = new AdvancedCreateAccountForm())
            {
                createForm.ShowDialog();
            }
        }

        private void BtnHelp_Click(object sender, EventArgs e)
        {
            MessageBox.Show("🆘 مساعدة النظام المتقدم\n\n📋 للدخول للنظام:\n👤 اسم المستخدم: admin\n🔒 كلمة المرور: admin123\n\n🔑 إذا نسيت كلمة المرور:\nانقر على زر 'نسيت كلمة المرور'\n\n👤 لإنشاء حساب جديد:\nانقر على زر 'إنشاء حساب جديد'\n\n📞 للدعم الفني:\nراجع دليل المستخدم المرفق مع النظام\n\n🌟 النظام المتقدم يوفر:\n• واجهة متطورة وسهلة الاستخدام\n• أمان عالي وحماية للبيانات\n• ميزات متقدمة للأرشفة\n• دعم كامل للغة العربية",
                "مساعدة النظام المتقدم", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    /// <summary>
    /// شاشة الترحيب المتقدمة مع تأثيرات بصرية متطورة
    /// Advanced welcome screen with sophisticated visual effects
    /// </summary>
    public partial class AdvancedWelcomeForm : Form
    {
        private System.Windows.Forms.Timer animationTimer;
        private int animationStep = 0;
        private Label welcomeLabel;
        private Label systemLabel;
        private Label featuresLabel;
        private PictureBox logoBox;
        private Panel mainPanel;
        private ProgressBar loadingBar;

        public AdvancedWelcomeForm()
        {
            InitializeComponent();
            SetupAdvancedAnimation();
        }

        private void InitializeComponent()
        {
            this.Text = "مرحباً بك في النظام المتقدم";
            this.Size = new Size(600, 450);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(15, 76, 129);
            this.ShowInTaskbar = false;
            this.TopMost = true;

            CreateAdvancedWelcomeContent();
        }

        private void CreateAdvancedWelcomeContent()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };

            // Advanced gradient background
            mainPanel.Paint += (s, e) => {
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    mainPanel.ClientRectangle,
                    Color.FromArgb(15, 76, 129),
                    Color.FromArgb(25, 118, 210),
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, mainPanel.ClientRectangle);
                }
            };

            // Advanced animated logo
            logoBox = new PictureBox
            {
                Size = new Size(150, 150),
                Location = new Point(225, 50),
                BackColor = Color.Transparent
            };

            Bitmap advancedWelcomeLogo = new Bitmap(150, 150);
            using (Graphics g = Graphics.FromImage(advancedWelcomeLogo))
            {
                g.Clear(Color.Transparent);
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Outer glow effect
                using (GraphicsPath path = new GraphicsPath())
                {
                    path.AddEllipse(10, 10, 130, 130);
                    using (PathGradientBrush glow = new PathGradientBrush(path))
                    {
                        glow.CenterColor = Color.FromArgb(200, Color.Gold);
                        glow.SurroundColors = new Color[] { Color.Transparent };
                        g.FillPath(glow, path);
                    }
                }

                // Main circle with gradient
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    new Rectangle(20, 20, 110, 110),
                    Color.White,
                    Color.FromArgb(240, 248, 255),
                    LinearGradientMode.Vertical))
                {
                    g.FillEllipse(brush, 20, 20, 110, 110);
                }

                // Inner circle
                using (LinearGradientBrush innerBrush = new LinearGradientBrush(
                    new Rectangle(35, 35, 80, 80),
                    Color.FromArgb(25, 118, 210),
                    Color.FromArgb(15, 76, 129),
                    LinearGradientMode.Vertical))
                {
                    g.FillEllipse(innerBrush, 35, 35, 80, 80);
                }

                // Advanced logo text
                using (Font logoFont = new Font("Tahoma", 16, FontStyle.Bold))
                {
                    g.DrawString("أرشفة", logoFont, Brushes.White, 50, 55);
                    g.DrawString("متقدمة", new Font("Tahoma", 12), Brushes.White, 45, 85);
                }
            }
            logoBox.Image = advancedWelcomeLogo;
            logoBox.Visible = false;

            // Advanced welcome message
            welcomeLabel = new Label
            {
                Text = "🌟 مرحباً بك في النظام المتقدم",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(550, 40),
                Location = new Point(25, 220),
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            // System name with advanced styling
            systemLabel = new Label
            {
                Text = "النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(550, 35),
                Location = new Point(25, 270),
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            // Features highlight
            featuresLabel = new Label
            {
                Text = "✨ الإصدار 3.0.0 - ميزات متطورة وتجربة استثنائية",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(255, 215, 0),
                Size = new Size(550, 30),
                Location = new Point(25, 310),
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            // Advanced loading bar
            loadingBar = new ProgressBar
            {
                Size = new Size(400, 15),
                Location = new Point(100, 360),
                Style = ProgressBarStyle.Continuous,
                ForeColor = Color.FromArgb(76, 175, 80),
                Visible = false
            };

            mainPanel.Controls.AddRange(new Control[] { logoBox, welcomeLabel, systemLabel, featuresLabel, loadingBar });
            this.Controls.Add(mainPanel);
        }

        private void SetupAdvancedAnimation()
        {
            animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 200;
            animationTimer.Tick += AdvancedAnimationTimer_Tick;
            animationTimer.Start();
        }

        private void AdvancedAnimationTimer_Tick(object sender, EventArgs e)
        {
            animationStep++;

            switch (animationStep)
            {
                case 3: // Show logo with fade-in
                    logoBox.Visible = true;
                    break;

                case 8: // Show welcome message
                    welcomeLabel.Visible = true;
                    break;

                case 12: // Show system name
                    systemLabel.Visible = true;
                    break;

                case 16: // Show features
                    featuresLabel.Visible = true;
                    break;

                case 20: // Show loading bar
                    loadingBar.Visible = true;
                    break;

                case 25: // Update loading bar
                    loadingBar.Value = 50;
                    break;

                case 30: // Complete loading
                    loadingBar.Value = 100;
                    break;

                case 35: // Close animation
                    animationTimer.Stop();
                    animationTimer.Dispose();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                    break;
            }
        }
    }

    /// <summary>
    /// نموذج استعادة كلمة المرور المتقدم
    /// Advanced forgot password form
    /// </summary>
    public partial class AdvancedForgotPasswordForm : Form
    {
        private TextBox txtEmail;
        private Button btnSend;
        private Button btnCancel;
        private ProgressBar progressBar;

        public AdvancedForgotPasswordForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "🔑 استعادة كلمة المرور - النظام المتقدم";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label titleLabel = new Label
            {
                Text = "🔑 استعادة كلمة المرور",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(450, 35),
                Location = new Point(25, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label instructionLabel = new Label
            {
                Text = "📧 أدخل عنوان البريد الإلكتروني المرتبط بحسابك:",
                Font = new Font("Tahoma", 11),
                Size = new Size(450, 40),
                Location = new Point(25, 80),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label emailLabel = new Label
            {
                Text = "📧 البريد الإلكتروني:",
                Size = new Size(120, 25),
                Location = new Point(50, 140),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtEmail = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(180, 140),
                Font = new Font("Tahoma", 11)
            };

            progressBar = new ProgressBar
            {
                Size = new Size(250, 20),
                Location = new Point(180, 180),
                Visible = false
            };

            btnSend = new Button
            {
                Text = "📤 إرسال",
                Size = new Size(100, 40),
                Location = new Point(180, 220),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            btnSend.FlatAppearance.BorderSize = 0;
            btnSend.Click += BtnSend_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(300, 220),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] {
                titleLabel, instructionLabel, emailLabel, txtEmail, progressBar, btnSend, btnCancel
            });
        }

        private void BtnSend_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("⚠️ يرجى إدخال عنوان البريد الإلكتروني", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            progressBar.Visible = true;
            btnSend.Enabled = false;

            System.Windows.Forms.Timer sendTimer = new System.Windows.Forms.Timer();
            sendTimer.Interval = 100;
            int progress = 0;

            sendTimer.Tick += (s, ev) => {
                progress += 10;
                progressBar.Value = Math.Min(progress, 100);

                if (progress >= 100)
                {
                    sendTimer.Stop();
                    sendTimer.Dispose();

                    MessageBox.Show("✅ تم إرسال رابط استعادة كلمة المرور بنجاح!",
                        "تم الإرسال", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.Close();
                }
            };

            sendTimer.Start();
        }
    }

    /// <summary>
    /// نموذج إنشاء حساب جديد متقدم
    /// Advanced create new account form
    /// </summary>
    public partial class AdvancedCreateAccountForm : Form
    {
        private TextBox txtFullName;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private TextBox txtEmail;
        private ComboBox cmbDepartment;
        private Button btnCreate;
        private Button btnCancel;

        public AdvancedCreateAccountForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "👤 إنشاء حساب جديد - النظام المتقدم";
            this.Size = new Size(550, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label titleLabel = new Label
            {
                Text = "👤 إنشاء حساب جديد",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(500, 35),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Create form fields
            CreateFormFields();

            this.Controls.Add(titleLabel);
        }

        private void CreateFormFields()
        {
            int yPos = 70;
            int spacing = 40;

            // Full Name
            AddField("👤 الاسم الكامل:", ref txtFullName, yPos);
            yPos += spacing;

            // Username
            AddField("🔤 اسم المستخدم:", ref txtUsername, yPos);
            yPos += spacing;

            // Password
            AddField("🔒 كلمة المرور:", ref txtPassword, yPos, true);
            yPos += spacing;

            // Confirm Password
            AddField("🔐 تأكيد كلمة المرور:", ref txtConfirmPassword, yPos, true);
            yPos += spacing;

            // Email
            AddField("📧 البريد الإلكتروني:", ref txtEmail, yPos);
            yPos += spacing;

            // Department
            Label lblDepartment = new Label
            {
                Text = "🏢 القسم:",
                Size = new Size(120, 25),
                Location = new Point(50, yPos),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            cmbDepartment = new ComboBox
            {
                Size = new Size(250, 25),
                Location = new Point(180, yPos),
                Font = new Font("Tahoma", 11),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbDepartment.Items.AddRange(new string[] { "الإدارة", "المالية", "الموارد البشرية", "تقنية المعلومات" });

            this.Controls.AddRange(new Control[] { lblDepartment, cmbDepartment });
            yPos += spacing + 20;

            // Buttons
            btnCreate = new Button
            {
                Text = "✅ إنشاء الحساب",
                Size = new Size(120, 40),
                Location = new Point(180, yPos),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            btnCreate.FlatAppearance.BorderSize = 0;
            btnCreate.Click += BtnCreate_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(320, yPos),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] { btnCreate, btnCancel });
        }

        private void AddField(string labelText, ref TextBox textBox, int yPos, bool isPassword = false)
        {
            Label label = new Label
            {
                Text = labelText,
                Size = new Size(120, 25),
                Location = new Point(50, yPos),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            textBox = new TextBox
            {
                Size = new Size(250, 25),
                Location = new Point(180, yPos),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = isPassword
            };

            this.Controls.AddRange(new Control[] { label, textBox });
        }

        private void BtnCreate_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtFullName.Text) ||
                string.IsNullOrWhiteSpace(txtUsername.Text) ||
                string.IsNullOrWhiteSpace(txtPassword.Text) ||
                string.IsNullOrWhiteSpace(txtEmail.Text) ||
                cmbDepartment.SelectedIndex == -1)
            {
                MessageBox.Show("⚠️ يرجى ملء جميع الحقول المطلوبة", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("⚠️ كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show(string.Format("✅ تم إنشاء الحساب بنجاح!\n\n👤 الاسم: {0}\n🔤 اسم المستخدم: {1}\n🏢 القسم: {2}",
                txtFullName.Text, txtUsername.Text, cmbDepartment.Text),
                "تم إنشاء الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }
    }
}
