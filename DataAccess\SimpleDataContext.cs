using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Ali_Mola.DataAccess.Models;
using Ali_Mola.Utilities;

namespace Ali_Mola.DataAccess
{
    /// <summary>
    /// سياق بيانات مبسط في الذاكرة
    /// Simple in-memory data context
    /// </summary>
    public class SimpleDataContext : IDisposable
    {
        private static List<User> users = new List<User>();
        private static List<Department> departments = new List<Department>();
        private static bool isInitialized = false;

        /// <summary>
        /// منشئ السياق
        /// Context constructor
        /// </summary>
        public SimpleDataContext()
        {
            if (!isInitialized)
            {
                InitializeData();
                isInitialized = true;
            }
        }

        /// <summary>
        /// الحصول على الاتصال
        /// Get connection
        /// </summary>
        private SqlConnection GetConnection()
        {
            if (connection == null)
            {
                connection = new SqlConnection(connectionString);
            }
            return connection;
        }

        /// <summary>
        /// إنشاء الجداول
        /// Create tables
        /// </summary>
        public void CreateTables()
        {
            try
            {
                using (var conn = GetConnection())
                {
                    conn.Open();

                    // إنشاء جدول المستخدمين
                    // Create users table
                    string createUsersTable = @"
                        CREATE TABLE IF NOT EXISTS Users (
                            UserId INTEGER PRIMARY KEY AUTOINCREMENT,
                            Username NVARCHAR(50) NOT NULL UNIQUE,
                            Password NVARCHAR(255) NOT NULL,
                            FullName NVARCHAR(100),
                            Role NVARCHAR(20) NOT NULL DEFAULT 'User',
                            CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            LastLoginDate DATETIME,
                            IsActive BOOLEAN NOT NULL DEFAULT 1
                        )";

                    using (var cmd = new SqlCommand(createUsersTable, conn))
                    {
                        cmd.ExecuteNonQuery();
                    }

                    // إنشاء جدول الأقسام
                    // Create departments table
                    string createDepartmentsTable = @"
                        CREATE TABLE IF NOT EXISTS Departments (
                            DepartmentId INTEGER PRIMARY KEY AUTOINCREMENT,
                            Name NVARCHAR(100) NOT NULL,
                            CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                        )";

                    using (var cmd = new SqlCommand(createDepartmentsTable, conn))
                    {
                        cmd.ExecuteNonQuery();
                    }

                    // إنشاء المستخدم الافتراضي
                    // Create default user
                    CreateDefaultUser(conn);
                    
                    // إنشاء القسم الافتراضي
                    // Create default department
                    CreateDefaultDepartment(conn);
                }

                Logger.LogInfo("تم إنشاء الجداول بنجاح");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء الجداول", ex);
                throw;
            }
        }

        /// <summary>
        /// إنشاء المستخدم الافتراضي
        /// Create default user
        /// </summary>
        private void CreateDefaultUser(SqlConnection conn)
        {
            try
            {
                // التحقق من وجود المستخدم الافتراضي
                // Check if default user exists
                string checkUserQuery = "SELECT COUNT(*) FROM Users WHERE Username = 'admin'";
                using (var cmd = new SqlCommand(checkUserQuery, conn))
                {
                    int userCount = Convert.ToInt32(cmd.ExecuteScalar());
                    if (userCount == 0)
                    {
                        // إنشاء المستخدم الافتراضي
                        // Create default user
                        string insertUserQuery = @"
                            INSERT INTO Users (Username, Password, FullName, Role, IsActive)
                            VALUES ('admin', @password, 'مدير النظام', 'Admin', 1)";

                        using (var insertCmd = new SqlCommand(insertUserQuery, conn))
                        {
                            insertCmd.Parameters.AddWithValue("@password", PasswordHelper.HashPassword("admin123"));
                            insertCmd.ExecuteNonQuery();
                        }

                        Logger.LogInfo("تم إنشاء المستخدم الافتراضي");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء المستخدم الافتراضي", ex);
            }
        }

        /// <summary>
        /// إنشاء القسم الافتراضي
        /// Create default department
        /// </summary>
        private void CreateDefaultDepartment(SqlConnection conn)
        {
            try
            {
                // التحقق من وجود أقسام
                // Check if departments exist
                string checkDeptQuery = "SELECT COUNT(*) FROM Departments";
                using (var cmd = new SqlCommand(checkDeptQuery, conn))
                {
                    int deptCount = Convert.ToInt32(cmd.ExecuteScalar());
                    if (deptCount == 0)
                    {
                        // إنشاء القسم الافتراضي
                        // Create default department
                        string insertDeptQuery = @"
                            INSERT INTO Departments (Name)
                            VALUES ('القسم العام')";

                        using (var insertCmd = new SqlCommand(insertDeptQuery, conn))
                        {
                            insertCmd.ExecuteNonQuery();
                        }

                        Logger.LogInfo("تم إنشاء القسم الافتراضي");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء القسم الافتراضي", ex);
            }
        }

        /// <summary>
        /// التحقق من بيانات المستخدم
        /// Validate user credentials
        /// </summary>
        public User ValidateUser(string username, string password)
        {
            try
            {
                using (var conn = GetConnection())
                {
                    conn.Open();
                    string query = @"
                        SELECT UserId, Username, Password, FullName, Role, IsActive
                        FROM Users 
                        WHERE Username = @username AND IsActive = 1";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@username", username);
                        
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string storedPassword = reader["Password"].ToString();
                                if (PasswordHelper.VerifyPassword(password, storedPassword))
                                {
                                    return new User
                                    {
                                        UserId = Convert.ToInt32(reader["UserId"]),
                                        Username = reader["Username"].ToString(),
                                        Password = storedPassword,
                                        FullName = reader["FullName"].ToString(),
                                        Role = reader["Role"].ToString(),
                                        IsActive = Convert.ToBoolean(reader["IsActive"])
                                    };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في التحقق من بيانات المستخدم", ex);
            }

            return null;
        }

        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول
        /// Update last login date
        /// </summary>
        public void UpdateLastLoginDate(int userId)
        {
            try
            {
                using (var conn = GetConnection())
                {
                    conn.Open();
                    string query = "UPDATE Users SET LastLoginDate = @loginDate WHERE UserId = @userId";
                    
                    using (var cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@loginDate", DateTime.Now);
                        cmd.Parameters.AddWithValue("@userId", userId);
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحديث تاريخ آخر تسجيل دخول", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات بسيطة
        /// Get simple statistics
        /// </summary>
        public SimpleStatistics GetStatistics()
        {
            var stats = new SimpleStatistics();
            
            try
            {
                using (var conn = GetConnection())
                {
                    conn.Open();
                    
                    // عدد الأقسام
                    // Department count
                    string deptQuery = "SELECT COUNT(*) FROM Departments";
                    using (var cmd = new SqlCommand(deptQuery, conn))
                    {
                        stats.DepartmentCount = Convert.ToInt32(cmd.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على الإحصائيات", ex);
            }

            return stats;
        }

        /// <summary>
        /// تنظيف الموارد
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            connection?.Close();
            connection?.Dispose();
        }
    }

    /// <summary>
    /// إحصائيات بسيطة
    /// Simple statistics
    /// </summary>
    public class SimpleStatistics
    {
        public int DepartmentCount { get; set; }
        public int TotalDocuments { get; set; } = 0;
        public int OutgoingDocuments { get; set; } = 0;
        public int IncomingDocuments { get; set; } = 0;
        public int DocumentsThisMonth { get; set; } = 0;
    }
}
