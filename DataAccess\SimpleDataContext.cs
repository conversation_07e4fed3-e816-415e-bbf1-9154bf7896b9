using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Ali_Mola.DataAccess.Models;
using Ali_Mola.Utilities;

namespace Ali_Mola.DataAccess
{
    /// <summary>
    /// سياق بيانات مبسط في الذاكرة
    /// Simple in-memory data context
    /// </summary>
    public class SimpleDataContext : IDisposable
    {
        private static List<User> users = new List<User>();
        private static List<Department> departments = new List<Department>();
        private static bool isInitialized = false;

        /// <summary>
        /// منشئ السياق
        /// Context constructor
        /// </summary>
        public SimpleDataContext()
        {
            if (!isInitialized)
            {
                InitializeData();
                isInitialized = true;
            }
        }

        /// <summary>
        /// تهيئة البيانات
        /// Initialize data
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // إنشاء المستخدم الافتراضي
                // Create default user
                CreateDefaultUser();

                // إنشاء القسم الافتراضي
                // Create default department
                CreateDefaultDepartment();

                Logger.LogInfo("تم تهيئة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تهيئة البيانات", ex);
                throw;
            }
        }

        /// <summary>
        /// إنشاء الجداول (للتوافق مع الكود الموجود)
        /// Create tables (for compatibility with existing code)
        /// </summary>
        public void CreateTables()
        {
            // لا حاجة لإنشاء جداول في التنفيذ في الذاكرة
            // No need to create tables in in-memory implementation
            Logger.LogInfo("تم إنشاء الجداول بنجاح (في الذاكرة)");
        }

        /// <summary>
        /// إنشاء المستخدم الافتراضي
        /// Create default user
        /// </summary>
        private void CreateDefaultUser()
        {
            try
            {
                // التحقق من وجود المستخدم الافتراضي
                // Check if default user exists
                if (!users.Any(u => u.Username == "admin"))
                {
                    // إنشاء المستخدم الافتراضي
                    // Create default user
                    var adminUser = new User
                    {
                        UserId = 1,
                        Username = "admin",
                        Password = PasswordHelper.HashPassword("admin123"),
                        FullName = "مدير النظام",
                        Role = "Admin",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    };

                    users.Add(adminUser);
                    Logger.LogInfo("تم إنشاء المستخدم الافتراضي");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء المستخدم الافتراضي", ex);
            }
        }

        /// <summary>
        /// إنشاء القسم الافتراضي
        /// Create default department
        /// </summary>
        private void CreateDefaultDepartment()
        {
            try
            {
                // التحقق من وجود أقسام
                // Check if departments exist
                if (!departments.Any())
                {
                    // إنشاء القسم الافتراضي
                    // Create default department
                    var defaultDepartment = new Department
                    {
                        DepartmentId = 1,
                        Name = "القسم العام",
                        CreatedDate = DateTime.Now
                    };

                    departments.Add(defaultDepartment);
                    Logger.LogInfo("تم إنشاء القسم الافتراضي");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء القسم الافتراضي", ex);
            }
        }

        /// <summary>
        /// التحقق من بيانات المستخدم
        /// Validate user credentials
        /// </summary>
        public User ValidateUser(string username, string password)
        {
            try
            {
                var user = users.FirstOrDefault(u => u.Username == username && u.IsActive);

                if (user != null && PasswordHelper.VerifyPassword(password, user.Password))
                {
                    return user;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في التحقق من بيانات المستخدم", ex);
            }

            return null;
        }

        /// <summary>
        /// تحديث تاريخ آخر تسجيل دخول
        /// Update last login date
        /// </summary>
        public void UpdateLastLoginDate(int userId)
        {
            try
            {
                var user = users.FirstOrDefault(u => u.UserId == userId);
                if (user != null)
                {
                    user.LastLoginDate = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحديث تاريخ آخر تسجيل دخول", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات بسيطة
        /// Get simple statistics
        /// </summary>
        public SimpleStatistics GetStatistics()
        {
            var stats = new SimpleStatistics();

            try
            {
                // عدد الأقسام
                // Department count
                stats.DepartmentCount = departments.Count;

                // إحصائيات وهمية للعرض
                // Mock statistics for display
                stats.TotalDocuments = 25;
                stats.OutgoingDocuments = 15;
                stats.IncomingDocuments = 10;
                stats.DocumentsThisMonth = 8;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على الإحصائيات", ex);
            }

            return stats;
        }

        /// <summary>
        /// تنظيف الموارد
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            // لا حاجة لتنظيف في التنفيذ في الذاكرة
            // No cleanup needed in in-memory implementation
        }
    }

    /// <summary>
    /// إحصائيات بسيطة
    /// Simple statistics
    /// </summary>
    public class SimpleStatistics
    {
        public int DepartmentCount { get; set; }
        public int TotalDocuments { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
        public int DocumentsThisMonth { get; set; }

        public SimpleStatistics()
        {
            TotalDocuments = 0;
            OutgoingDocuments = 0;
            IncomingDocuments = 0;
            DocumentsThisMonth = 0;
        }
    }
}
