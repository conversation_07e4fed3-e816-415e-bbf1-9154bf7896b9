using System;
using System.Drawing;
using System.Windows.Forms;
using Ali_Mo<PERSON>.Utilities;

namespace <PERSON>_<PERSON>
{
    /// <summary>
    /// نموذج إنشاء حساب جديد
    /// Create Account Form
    /// </summary>
    public partial class CreateAccountForm : Form
    {
        private Panel mainPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private TextBox txtEmail;
        private TextBox txtFullName;
        private ComboBox cmbSecurityQuestion;
        private TextBox txtSecurityAnswer;
        private Button btnCreate;
        private Button btnCancel;
        
        public CreateAccountForm()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Text = "إنشاء حساب جديد - النظام المحسن";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = false;
            
            CreateMainLayout();
        }
        
        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            
            // Header
            Label headerLabel = new Label
            {
                Text = "🆕 إنشاء حساب جديد",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(440, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Username
            Label lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Size = new Size(120, 25),
                Location = new Point(30, 80),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            
            txtUsername = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 80),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Full Name
            Label lblFullName = new Label
            {
                Text = "👨 الاسم الكامل:",
                Size = new Size(120, 25),
                Location = new Point(30, 120),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            
            txtFullName = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 120),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Email
            Label lblEmail = new Label
            {
                Text = "📧 البريد الإلكتروني:",
                Size = new Size(120, 25),
                Location = new Point(30, 160),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            
            txtEmail = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 160),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Password
            Label lblPassword = new Label
            {
                Text = "🔒 كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 200),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            
            txtPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 200),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Confirm Password
            Label lblConfirmPassword = new Label
            {
                Text = "🔐 تأكيد كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 240),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            
            txtConfirmPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 240),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Security Question
            Label lblSecurityQuestion = new Label
            {
                Text = "❓ سؤال الأمان:",
                Size = new Size(120, 25),
                Location = new Point(30, 280),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            
            cmbSecurityQuestion = new ComboBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 280),
                Font = new Font("Tahoma", 11),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbSecurityQuestion.Items.AddRange(new string[]
            {
                "ما هو اسم مدينة ولادتك؟",
                "ما هو اسم مدرستك الابتدائية؟",
                "ما هو اسم حيوانك الأليف المفضل؟",
                "ما هو لونك المفضل؟",
                "ما هو اسم أفضل صديق لك؟"
            });
            
            // Security Answer
            Label lblSecurityAnswer = new Label
            {
                Text = "💬 إجابة سؤال الأمان:",
                Size = new Size(120, 25),
                Location = new Point(30, 320),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            
            txtSecurityAnswer = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 320),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Buttons
            btnCreate = new Button
            {
                Text = "✅ إنشاء الحساب",
                Size = new Size(120, 40),
                Location = new Point(200, 380),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCreate.FlatAppearance.BorderSize = 0;
            btnCreate.Click += BtnCreate_Click;
            
            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(340, 380),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };
            
            mainPanel.Controls.AddRange(new Control[] {
                headerLabel, lblUsername, txtUsername, lblFullName, txtFullName,
                lblEmail, txtEmail, lblPassword, txtPassword, lblConfirmPassword, txtConfirmPassword,
                lblSecurityQuestion, cmbSecurityQuestion, lblSecurityAnswer, txtSecurityAnswer,
                btnCreate, btnCancel
            });
            
            this.Controls.Add(mainPanel);
            this.AcceptButton = btnCreate;
            this.CancelButton = btnCancel;
        }
        
        private void BtnCreate_Click(object sender, EventArgs e)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFullName.Focus();
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtEmail.Text) || !txtEmail.Text.Contains("@"))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح!", "بيانات غير صحيحة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtPassword.Text) || txtPassword.Text.Length < 6)
            {
                MessageBox.Show("يرجى إدخال كلمة مرور لا تقل عن 6 أحرف!", "كلمة مرور ضعيفة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return;
            }
            
            if (txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين!", "خطأ في التأكيد", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return;
            }
            
            if (cmbSecurityQuestion.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار سؤال الأمان!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbSecurityQuestion.Focus();
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtSecurityAnswer.Text))
            {
                MessageBox.Show("يرجى إدخال إجابة سؤال الأمان!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSecurityAnswer.Focus();
                return;
            }
            
            // Create user
            if (UserManager.CreateUser(txtUsername.Text, txtPassword.Text, txtEmail.Text, 
                txtFullName.Text, cmbSecurityQuestion.Text, txtSecurityAnswer.Text))
            {
                Logger.LogInfo(string.Format("تم إنشاء حساب جديد للمستخدم: {0}", txtUsername.Text));
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم موجود مسبقاً! يرجى اختيار اسم مستخدم آخر.", 
                    "اسم مستخدم مكرر", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                txtUsername.SelectAll();
            }
        }
    }

    /// <summary>
    /// نموذج استعادة كلمة المرور
    /// Forgot Password Form
    /// </summary>
    public partial class ForgotPasswordForm : Form
    {
        private Panel mainPanel;
        private TextBox txtUsername;
        private TextBox txtEmail;
        private Label lblSecurityQuestion;
        private TextBox txtSecurityAnswer;
        private TextBox txtNewPassword;
        private TextBox txtConfirmNewPassword;
        private Button btnResetPassword;
        private Button btnCancel;
        private Button btnVerify;
        private Panel step1Panel;
        private Panel step2Panel;

        public ForgotPasswordForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "استعادة كلمة المرور - النظام المحسن";
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = false;

            CreateMainLayout();
        }

        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            Label headerLabel = new Label
            {
                Text = "🔑 استعادة كلمة المرور",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 152, 0),
                Size = new Size(440, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Step 1 Panel
            step1Panel = new Panel
            {
                Size = new Size(440, 150),
                Location = new Point(20, 70),
                BackColor = Color.Transparent
            };

            Label lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Size = new Size(120, 25),
                Location = new Point(30, 30),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtUsername = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 30),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label lblEmail = new Label
            {
                Text = "📧 البريد الإلكتروني:",
                Size = new Size(120, 25),
                Location = new Point(30, 70),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtEmail = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 70),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            btnVerify = new Button
            {
                Text = "🔍 التحقق",
                Size = new Size(100, 35),
                Location = new Point(170, 110),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnVerify.FlatAppearance.BorderSize = 0;
            btnVerify.Click += BtnVerify_Click;

            step1Panel.Controls.AddRange(new Control[] {
                lblUsername, txtUsername, lblEmail, txtEmail, btnVerify
            });

            // Step 2 Panel (initially hidden)
            step2Panel = new Panel
            {
                Size = new Size(440, 200),
                Location = new Point(20, 230),
                BackColor = Color.Transparent,
                Visible = false
            };

            lblSecurityQuestion = new Label
            {
                Text = "❓ سؤال الأمان:",
                Size = new Size(420, 25),
                Location = new Point(10, 10),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(100, 100, 100),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label lblSecurityAnswer = new Label
            {
                Text = "💬 إجابة سؤال الأمان:",
                Size = new Size(120, 25),
                Location = new Point(30, 50),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtSecurityAnswer = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 50),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label lblNewPassword = new Label
            {
                Text = "🔒 كلمة المرور الجديدة:",
                Size = new Size(120, 25),
                Location = new Point(30, 90),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtNewPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 90),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            Label lblConfirmNewPassword = new Label
            {
                Text = "🔐 تأكيد كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 130),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtConfirmNewPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 130),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            step2Panel.Controls.AddRange(new Control[] {
                lblSecurityQuestion, lblSecurityAnswer, txtSecurityAnswer,
                lblNewPassword, txtNewPassword, lblConfirmNewPassword, txtConfirmNewPassword
            });

            // Bottom buttons
            btnResetPassword = new Button
            {
                Text = "✅ إعادة تعيين كلمة المرور",
                Size = new Size(180, 40),
                Location = new Point(150, 450),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Visible = false
            };
            btnResetPassword.FlatAppearance.BorderSize = 0;
            btnResetPassword.Click += BtnResetPassword_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(350, 450),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            mainPanel.Controls.AddRange(new Control[] {
                headerLabel, step1Panel, step2Panel, btnResetPassword, btnCancel
            });

            this.Controls.Add(mainPanel);
            this.CancelButton = btnCancel;
        }

        private void BtnVerify_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم والبريد الإلكتروني!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var user = UserManager.GetUser(txtUsername.Text);
            if (user != null && user.Email.ToLower() == txtEmail.Text.ToLower())
            {
                lblSecurityQuestion.Text = string.Format("❓ {0}", user.SecurityQuestion);
                step2Panel.Visible = true;
                btnResetPassword.Visible = true;
                this.Height = 550;

                MessageBox.Show("✅ تم التحقق من البيانات بنجاح!\nيرجى الإجابة على سؤال الأمان وإدخال كلمة المرور الجديدة.",
                    "تم التحقق", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("❌ اسم المستخدم أو البريد الإلكتروني غير صحيح!", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnResetPassword_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSecurityAnswer.Text))
            {
                MessageBox.Show("يرجى إدخال إجابة سؤال الأمان!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSecurityAnswer.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtNewPassword.Text) || txtNewPassword.Text.Length < 6)
            {
                MessageBox.Show("يرجى إدخال كلمة مرور جديدة لا تقل عن 6 أحرف!", "كلمة مرور ضعيفة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return;
            }

            if (txtNewPassword.Text != txtConfirmNewPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين!", "خطأ في التأكيد",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmNewPassword.Focus();
                return;
            }

            if (UserManager.ResetPassword(txtUsername.Text, txtEmail.Text, txtSecurityAnswer.Text, txtNewPassword.Text))
            {
                Logger.LogInfo(string.Format("تم إعادة تعيين كلمة المرور للمستخدم: {0}", txtUsername.Text));
                MessageBox.Show("✅ تم إعادة تعيين كلمة المرور بنجاح!\nيمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.",
                    "نجحت العملية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("❌ إجابة سؤال الأمان غير صحيحة!", "خطأ في الإجابة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSecurityAnswer.Focus();
                txtSecurityAnswer.SelectAll();
            }
        }
    }

    /// <summary>
    /// نموذج تغيير كلمة المرور
    /// Change Password Form
    /// </summary>
    public partial class ChangePasswordForm : Form
    {
        private Panel mainPanel;
        private TextBox txtCurrentPassword;
        private TextBox txtNewPassword;
        private TextBox txtConfirmNewPassword;
        private Button btnChangePassword;
        private Button btnCancel;

        public ChangePasswordForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تغيير كلمة المرور - النظام المحسن";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = false;

            CreateMainLayout();
        }

        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            Label headerLabel = new Label
            {
                Text = "🔑 تغيير كلمة المرور",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 152, 0),
                Size = new Size(390, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Current Password
            Label lblCurrentPassword = new Label
            {
                Text = "🔒 كلمة المرور الحالية:",
                Size = new Size(150, 25),
                Location = new Point(30, 80),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtCurrentPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(190, 80),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // New Password
            Label lblNewPassword = new Label
            {
                Text = "🔐 كلمة المرور الجديدة:",
                Size = new Size(150, 25),
                Location = new Point(30, 120),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtNewPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(190, 120),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Confirm New Password
            Label lblConfirmNewPassword = new Label
            {
                Text = "✅ تأكيد كلمة المرور:",
                Size = new Size(150, 25),
                Location = new Point(30, 160),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtConfirmNewPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(190, 160),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Instructions
            Label instructionsLabel = new Label
            {
                Text = "💡 كلمة المرور الجديدة يجب أن تكون:\n• لا تقل عن 6 أحرف\n• تحتوي على أحرف وأرقام",
                Font = new Font("Tahoma", 9),
                ForeColor = Color.FromArgb(100, 100, 100),
                Size = new Size(360, 50),
                Location = new Point(30, 200),
                TextAlign = ContentAlignment.TopRight
            };

            // Buttons
            btnChangePassword = new Button
            {
                Text = "✅ تغيير كلمة المرور",
                Size = new Size(150, 40),
                Location = new Point(190, 260),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnChangePassword.FlatAppearance.BorderSize = 0;
            btnChangePassword.Click += BtnChangePassword_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(360, 260),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            mainPanel.Controls.AddRange(new Control[] {
                headerLabel,
                lblCurrentPassword, txtCurrentPassword,
                lblNewPassword, txtNewPassword,
                lblConfirmNewPassword, txtConfirmNewPassword,
                instructionsLabel,
                btnChangePassword, btnCancel
            });

            this.Controls.Add(mainPanel);
            this.AcceptButton = btnChangePassword;
            this.CancelButton = btnCancel;
        }

        private void BtnChangePassword_Click(object sender, EventArgs e)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(txtCurrentPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور الحالية!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrentPassword.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtNewPassword.Text) || txtNewPassword.Text.Length < 6)
            {
                MessageBox.Show("يرجى إدخال كلمة مرور جديدة لا تقل عن 6 أحرف!", "كلمة مرور ضعيفة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return;
            }

            if (txtNewPassword.Text != txtConfirmNewPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين!", "خطأ في التأكيد",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmNewPassword.Focus();
                return;
            }

            if (txtCurrentPassword.Text == txtNewPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية!", "كلمة مرور مكررة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return;
            }

            // For admin user, just validate current password
            if (txtCurrentPassword.Text == "admin123")
            {
                MessageBox.Show("✅ تم تغيير كلمة المرور بنجاح!\n\n💡 ملاحظة: في النظام الكامل، سيتم حفظ كلمة المرور الجديدة في قاعدة البيانات.",
                    "نجحت العملية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم تغيير كلمة المرور للمدير");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                // Try to change password for registered users
                string currentUser = "admin"; // In real system, get from session
                if (UserManager.ChangePassword(currentUser, txtCurrentPassword.Text, txtNewPassword.Text))
                {
                    Logger.LogInfo(string.Format("تم تغيير كلمة المرور للمستخدم: {0}", currentUser));
                    MessageBox.Show("✅ تم تغيير كلمة المرور بنجاح!", "نجحت العملية",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("❌ كلمة المرور الحالية غير صحيحة!", "خطأ في كلمة المرور",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCurrentPassword.Focus();
                    txtCurrentPassword.SelectAll();
                }
            }
        }
    }
}
