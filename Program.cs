using System;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.DataAccess;
using <PERSON>_<PERSON><PERSON>.Forms;
using Ali_Mo<PERSON>.Utilities;

namespace <PERSON>_Mola
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// Main application entry point
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق
        /// The main entry point for the application
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تمكين الأنماط المرئية
                // Enable visual styles
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تسجيل بداية التطبيق
                // Log application start
                Logger.LogInfo("بدء تشغيل نظام الأرشفة الإلكترونية");

                // تهيئة قاعدة البيانات
                // Initialize database
                InitializeDatabase();

                // عرض نموذج تسجيل الدخول
                // Show login form
                using (var loginForm = new LoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // تسجيل الدخول ناجح، عرض النموذج الرئيسي
                        // Login successful, show main form
                        Logger.LogInfo("تم تسجيل الدخول بنجاح، عرض النموذج الرئيسي");
                        Application.Run(new MainForm());
                    }
                    else
                    {
                        // تم إلغاء تسجيل الدخول
                        // Login cancelled
                        Logger.LogInfo("تم إلغاء تسجيل الدخول");
                    }
                }

                Logger.LogInfo("انتهاء تشغيل نظام الأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ وعرض رسالة للمستخدم
                // Log error and show message to user
                Logger.LogError("خطأ فادح في التطبيق", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في التطبيق:\n{0}", ex.Message),
                    "خطأ فادح", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// Initialize database
        /// </summary>
        private static void InitializeDatabase()
        {
            try
            {
                Logger.LogInfo("بدء تهيئة قاعدة البيانات");

                using (var context = new SimpleDataContext())
                {
                    // إنشاء الجداول والبيانات الأولية
                    // Create tables and initial data
                    context.CreateTables();
                    Logger.LogInfo("تم إنشاء قاعدة البيانات والبيانات الأولية بنجاح");
                }

                Logger.LogInfo("تم الانتهاء من تهيئة قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                Logger.LogError("فشل في تهيئة قاعدة البيانات", ex);
                throw new ApplicationException("فشل في تهيئة قاعدة البيانات.", ex);
            }
        }
    }
}
