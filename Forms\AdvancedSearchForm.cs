﻿using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;
using Ali_Mo<PERSON>.BusinessLogic;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using Ali_Mo<PERSON>.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج البحث المتقدم
    /// Advanced search form
    /// </summary>
    public partial class AdvancedSearchForm : Form
    {
        #region Controls
        private GroupBox searchCriteriaGroup;
        private TextBox txtSerialNumber;
        private TextBox txtDocumentNumber;
        private TextBox txtSubject;
        private TextBox txtFrom;
        private TextBox txtTo;
        private ComboBox cmbDocumentType;
        private ComboBox cmbDepartment;
        private ComboBox cmbFileBox;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private CheckBox chkHasAttachments;
        private CheckBox chkDateRange;
        private Button btnSearch;
        private Button btnClear;
        private Button btnSaveSearch;
        private DataGridView dgvResults;
        private Label lblResultsCount;
        private Button btnViewDocument;
        private Button btnExportResults;
        private Button btnClose;
        #endregion

        private List<Document> searchResults;

        /// <summary>
        /// منشئ نموذج البحث المتقدم
        /// Advanced search form constructor
        /// </summary>
        public AdvancedSearchForm()
        {
            InitializeComponent();
            SetupForm();
            LoadSearchOptions();
            searchResults = new List<Document>();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "البحث المتقدم في الوثائق";
            this.Size = new Size(1100, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateSearchCriteriaSection();
            CreateResultsSection();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء قسم معايير البحث
        /// Create search criteria section
        /// </summary>
        private void CreateSearchCriteriaSection()
        {
            // عنوان النموذج
            // Form title
            Label titleLabel = new Label
            {
                Text = "البحث المتقدم في الوثائق",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };
            this.Controls.Add(titleLabel);

            // أيقونة البحث
            // Search icon
            Panel iconPanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(980, 15),
                BackColor = Color.FromArgb(33, 150, 243)
            };

            Label iconLabel = new Label
            {
                Text = "🔍",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            this.Controls.Add(iconPanel);

            // مجموعة معايير البحث
            // Search criteria group
            searchCriteriaGroup = new GroupBox
            {
                Text = "معايير البحث",
                Size = new Size(1040, 280),
                Location = new Point(20, 60),
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                BackColor = Color.White
            };
            this.Controls.Add(searchCriteriaGroup);

            int yPos = 30;
            int spacing = 40;

            // الرقم المسلسل
            // Serial number
            CreateSearchField("الرقم المسلسل:", ref yPos, spacing, out txtSerialNumber, 20, 300);

            // رقم الوثيقة
            // Document number
            CreateSearchField("رقم الوثيقة:", ref yPos, spacing, out txtDocumentNumber, 350, 300);

            // الموضوع
            // Subject
            yPos = 30;
            CreateSearchField("الموضوع:", ref yPos, spacing, out txtSubject, 680, 300);

            // من
            // From
            CreateSearchField("من:", ref yPos, spacing, out txtFrom, 680, 300);

            // إلى
            // To
            yPos = 110;
            CreateSearchField("إلى:", ref yPos, spacing, out txtTo, 20, 300);

            // نوع الوثيقة
            // Document type
            Label lblDocType = new Label
            {
                Text = "نوع الوثيقة:",
                Size = new Size(100, 25),
                Location = new Point(350, yPos),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchCriteriaGroup.Controls.Add(lblDocType);

            cmbDocumentType = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(460, yPos),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            searchCriteriaGroup.Controls.Add(cmbDocumentType);

            // القسم
            // Department
            Label lblDept = new Label
            {
                Text = "القسم:",
                Size = new Size(80, 25),
                Location = new Point(680, yPos),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchCriteriaGroup.Controls.Add(lblDept);

            cmbDepartment = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(770, yPos),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbDepartment.SelectedIndexChanged += CmbDepartment_SelectedIndexChanged;
            searchCriteriaGroup.Controls.Add(cmbDepartment);

            yPos += spacing;

            // الاضبارة
            // File box
            Label lblFileBox = new Label
            {
                Text = "الاضبارة:",
                Size = new Size(80, 25),
                Location = new Point(20, yPos),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchCriteriaGroup.Controls.Add(lblFileBox);

            cmbFileBox = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(110, yPos),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            searchCriteriaGroup.Controls.Add(cmbFileBox);

            // فترة زمنية
            // Date range
            chkDateRange = new CheckBox
            {
                Text = "البحث في فترة زمنية:",
                Size = new Size(150, 25),
                Location = new Point(350, yPos),
                Font = new Font("Tahoma", 10F)
            };
            chkDateRange.CheckedChanged += ChkDateRange_CheckedChanged;
            searchCriteriaGroup.Controls.Add(chkDateRange);

            // من تاريخ
            // From date
            Label lblFromDate = new Label
            {
                Text = "من:",
                Size = new Size(40, 25),
                Location = new Point(520, yPos),
                TextAlign = ContentAlignment.MiddleRight,
                Enabled = false
            };
            searchCriteriaGroup.Controls.Add(lblFromDate);

            dtpFromDate = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(570, yPos),
                Format = DateTimePickerFormat.Short,
                Enabled = false
            };
            searchCriteriaGroup.Controls.Add(dtpFromDate);

            // إلى تاريخ
            // To date
            Label lblToDate = new Label
            {
                Text = "إلى:",
                Size = new Size(40, 25),
                Location = new Point(710, yPos),
                TextAlign = ContentAlignment.MiddleRight,
                Enabled = false
            };
            searchCriteriaGroup.Controls.Add(lblToDate);

            dtpToDate = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(760, yPos),
                Format = DateTimePickerFormat.Short,
                Enabled = false
            };
            searchCriteriaGroup.Controls.Add(dtpToDate);

            yPos += spacing;

            // وجود مرفقات
            // Has attachments
            chkHasAttachments = new CheckBox
            {
                Text = "الوثائق التي تحتوي على مرفقات فقط",
                Size = new Size(250, 25),
                Location = new Point(20, yPos),
                Font = new Font("Tahoma", 10F)
            };
            searchCriteriaGroup.Controls.Add(chkHasAttachments);

            // أزرار البحث
            // Search buttons
            btnSearch = new Button
            {
                Text = "🔍 بحث متقدم",
                Size = new Size(120, 40),
                Location = new Point(400, yPos - 5),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnSearch.FlatAppearance.BorderSize = 0;
            btnSearch.Click += BtnSearch_Click;
            searchCriteriaGroup.Controls.Add(btnSearch);

            btnClear = new Button
            {
                Text = "مسح الكل",
                Size = new Size(100, 40),
                Location = new Point(540, yPos - 5),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnClear.FlatAppearance.BorderSize = 0;
            btnClear.Click += BtnClear_Click;
            searchCriteriaGroup.Controls.Add(btnClear);

            btnSaveSearch = new Button
            {
                Text = "حفظ البحث",
                Size = new Size(100, 40),
                Location = new Point(660, yPos - 5),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnSaveSearch.FlatAppearance.BorderSize = 0;
            btnSaveSearch.Click += BtnSaveSearch_Click;
            searchCriteriaGroup.Controls.Add(btnSaveSearch);
        }

        /// <summary>
        /// إنشاء حقل بحث
        /// Create search field
        /// </summary>
        private void CreateSearchField(string labelText, ref int yPos, int spacing, out TextBox textBox, int x, int width)
        {
            Label label = new Label
            {
                Text = labelText,
                Size = new Size(100, 25),
                Location = new Point(x, yPos),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchCriteriaGroup.Controls.Add(label);

            textBox = new TextBox
            {
                Size = new Size(width, 25),
                Location = new Point(x + 110, yPos),
                Font = new Font("Tahoma", 10F)
            };
            searchCriteriaGroup.Controls.Add(textBox);

            yPos += spacing;
        }

        /// <summary>
        /// إنشاء قسم النتائج
        /// Create results section
        /// </summary>
        private void CreateResultsSection()
        {
            // عنوان النتائج
            // Results title
            Label resultsLabel = new Label
            {
                Text = "نتائج البحث:",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(100, 25),
                Location = new Point(20, 360)
            };
            this.Controls.Add(resultsLabel);

            // عداد النتائج
            // Results counter
            lblResultsCount = new Label
            {
                Text = "عدد النتائج: 0",
                Font = new Font("Tahoma", 10F),
                ForeColor = Color.Gray,
                Size = new Size(150, 25),
                Location = new Point(130, 360)
            };
            this.Controls.Add(lblResultsCount);

            // جدول النتائج
            // Results grid
            dgvResults = new DataGridView
            {
                Size = new Size(1040, 320),
                Location = new Point(20, 390),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            dgvResults.DoubleClick += DgvResults_DoubleClick;
            this.Controls.Add(dgvResults);

            // إعداد أعمدة الجدول
            // Setup grid columns
            SetupResultsGrid();
        }

        /// <summary>
        /// إعداد جدول النتائج
        /// Setup results grid
        /// </summary>
        private void SetupResultsGrid()
        {
            dgvResults.Columns.Add("SerialNumber", "الرقم المسلسل");
            dgvResults.Columns.Add("Type", "النوع");
            dgvResults.Columns.Add("Subject", "الموضوع");
            dgvResults.Columns.Add("From", "من");
            dgvResults.Columns.Add("To", "إلى");
            dgvResults.Columns.Add("Department", "القسم");
            dgvResults.Columns.Add("DocumentDate", "تاريخ الوثيقة");
            dgvResults.Columns.Add("HasAttachments", "مرفقات");

            // تنسيق الأعمدة
            // Format columns
            dgvResults.Columns["SerialNumber"].Width = 150;
            dgvResults.Columns["Type"].Width = 80;
            dgvResults.Columns["Subject"].Width = 200;
            dgvResults.Columns["HasAttachments"].Width = 60;
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر عرض الوثيقة
            // View document button
            btnViewDocument = new Button
            {
                Text = "عرض الوثيقة",
                Size = new Size(120, 40),
                Location = new Point(300, 730),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F),
                Enabled = false
            };
            btnViewDocument.FlatAppearance.BorderSize = 0;
            btnViewDocument.Click += BtnViewDocument_Click;
            this.Controls.Add(btnViewDocument);

            // زر تصدير النتائج
            // Export results button
            btnExportResults = new Button
            {
                Text = "تصدير النتائج",
                Size = new Size(120, 40),
                Location = new Point(440, 730),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnExportResults.FlatAppearance.BorderSize = 0;
            btnExportResults.Click += BtnExportResults_Click;
            this.Controls.Add(btnExportResults);

            // زر الإغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(120, 40),
                Location = new Point(580, 730),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);
        }

        /// <summary>
        /// تحميل خيارات البحث
        /// Load search options
        /// </summary>
        private void LoadSearchOptions()
        {
            try
            {
                // تحميل أنواع الوثائق
                // Load document types
                cmbDocumentType.Items.AddRange(new string[] { "الكل", "وارد", "صادر" });
                cmbDocumentType.SelectedIndex = 0;

                // تحميل الأقسام
                // Load departments
                cmbDepartment.Items.Add("جميع الأقسام");
                var departments = DepartmentService.GetAllDepartments();
                foreach (var dept in departments)
                {
                    cmbDepartment.Items.Add(dept.Name);
                }
                cmbDepartment.SelectedIndex = 0;

                // تحميل الاضبارات
                // Load file boxes
                LoadFileBoxes();

                // تعيين التواريخ الافتراضية
                // Set default dates
                dtpFromDate.Value = DateTime.Now.AddMonths(-1);
                dtpToDate.Value = DateTime.Now;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل خيارات البحث", ex);
            }
        }

        /// <summary>
        /// تحميل الاضبارات
        /// Load file boxes
        /// </summary>
        private void LoadFileBoxes()
        {
            try
            {
                cmbFileBox.Items.Clear();
                cmbFileBox.Items.Add("جميع الاضبارات");

                if (cmbDepartment.SelectedIndex > 0)
                {
                    // تحميل اضبارات القسم المحدد
                    // Load selected department file boxes
                    string selectedDept = cmbDepartment.SelectedItem.ToString();
                    // هنا سيتم تحميل الاضبارات الفعلية من قاعدة البيانات
                    // Here actual file boxes will be loaded from database
                    
                    // إضافة اضبارات تجريبية
                    // Add sample file boxes
                    cmbFileBox.Items.AddRange(new string[] { "اضبارة عامة", "اضبارة المراسلات", "اضبارة التعاميم" });
                }

                cmbFileBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل الاضبارات", ex);
            }
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث تغيير القسم
        /// Department change event handler
        /// </summary>
        private void CmbDepartment_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadFileBoxes();
        }

        /// <summary>
        /// معالج حدث تفعيل الفترة الزمنية
        /// Date range activation event handler
        /// </summary>
        private void ChkDateRange_CheckedChanged(object sender, EventArgs e)
        {
            bool enabled = chkDateRange.Checked;
            dtpFromDate.Enabled = enabled;
            dtpToDate.Enabled = enabled;
        }

        /// <summary>
        /// معالج حدث البحث
        /// Search event handler
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                // تعطيل زر البحث أثناء المعالجة
                // Disable search button during processing
                btnSearch.Enabled = false;
                btnSearch.Text = "جاري البحث...";
                this.Cursor = Cursors.WaitCursor;

                // تنفيذ البحث
                // Execute search
                PerformAdvancedSearch();

                Logger.LogInfo("تم تنفيذ بحث متقدم");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في البحث المتقدم", ex);
                MessageBox.Show("حدث خطأ أثناء البحث", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSearch.Enabled = true;
                btnSearch.Text = "🔍 بحث متقدم";
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// تنفيذ البحث المتقدم
        /// Perform advanced search
        /// </summary>
        private void PerformAdvancedSearch()
        {
            // مسح النتائج السابقة
            // Clear previous results
            dgvResults.Rows.Clear();
            searchResults.Clear();

            // محاكاة البحث مع النتائج
            // Simulate search with results
            dgvResults.Rows.Add("وارد-1-2024-0001", "وارد", "طلب معلومات", "مواطن", "القسم العام", "القسم العام", "10/12/2024", "نعم");
            dgvResults.Rows.Add("صادر-1-2024-0001", "صادر", "رد على الاستفسار", "القسم العام", "مواطن", "قسم الشؤون الإدارية", "11/12/2024", "لا");
            dgvResults.Rows.Add("وارد-2-2024-0002", "وارد", "شكوى", "مواطن", "قسم المالية", "قسم المالية", "12/12/2024", "نعم");

            // تحديث عداد النتائج
            // Update results counter
            lblResultsCount.Text = string.Format("عدد النتائج: {0}", dgvResults.Rows.Count);
            
            // تمكين زر العرض إذا كانت هناك نتائج
            // Enable view button if there are results
            btnViewDocument.Enabled = dgvResults.Rows.Count > 0;

            if (dgvResults.Rows.Count > 0)
            {
                MessageBox.Show(string.Format("تم العثور على {0} نتيجة", dgvResults.Rows.Count), "نتائج البحث", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("لم يتم العثور على نتائج تطابق معايير البحث", "لا توجد نتائج", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// معالج حدث مسح الكل
        /// Clear all event handler
        /// </summary>
        private void BtnClear_Click(object sender, EventArgs e)
        {
            try
            {
                // مسح جميع حقول البحث
                // Clear all search fields
                txtSerialNumber.Clear();
                txtDocumentNumber.Clear();
                txtSubject.Clear();
                txtFrom.Clear();
                txtTo.Clear();
                cmbDocumentType.SelectedIndex = 0;
                cmbDepartment.SelectedIndex = 0;
                cmbFileBox.SelectedIndex = 0;
                chkHasAttachments.Checked = false;
                chkDateRange.Checked = false;
                dtpFromDate.Value = DateTime.Now.AddMonths(-1);
                dtpToDate.Value = DateTime.Now;

                // مسح النتائج
                // Clear results
                dgvResults.Rows.Clear();
                searchResults.Clear();
                lblResultsCount.Text = "عدد النتائج: 0";
                btnViewDocument.Enabled = false;

                Logger.LogInfo("تم مسح معايير البحث");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في مسح معايير البحث", ex);
            }
        }

        /// <summary>
        /// معالج حدث حفظ البحث
        /// Save search event handler
        /// </summary>
        private void BtnSaveSearch_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("وظيفة حفظ البحث قيد التطوير...\nسيتم إضافة إمكانية حفظ معايير البحث المفضلة لاحقاً", 
                    "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم طلب حفظ البحث");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حفظ البحث", ex);
            }
        }

        /// <summary>
        /// معالج حدث النقر المزدوج على النتائج
        /// Double click on results event handler
        /// </summary>
        private void DgvResults_DoubleClick(object sender, EventArgs e)
        {
            BtnViewDocument_Click(sender, e);
        }

        /// <summary>
        /// معالج حدث عرض الوثيقة
        /// View document event handler
        /// </summary>
        private void BtnViewDocument_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvResults.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvResults.SelectedRows[0];
                    string serialNumber = selectedRow.Cells["SerialNumber"].Value?.ToString() ?? "";
                    string subject = selectedRow.Cells["Subject"].Value?.ToString() ?? "";

                    // إنشاء وثيقة وهمية للعرض
                    // Create mock document for display
                    var document = new Document
                    {
                        DocumentId = 1,
                        SerialNumber = serialNumber,
                        Type = selectedRow.Cells["Type"].Value?.ToString() ?? "",
                        Subject = subject,
                        From = selectedRow.Cells["From"].Value?.ToString() ?? "",
                        To = selectedRow.Cells["To"].Value?.ToString() ?? "",
                        DocumentDate = DateTime.Parse(selectedRow.Cells["DocumentDate"].Value?.ToString() ?? DateTime.Now.ToString()),
                        EntryDate = DateTime.Now,
                        FileBox = new FileBox
                        {
                            Name = "اضبارة تجريبية",
                            Department = new Department
                            {
                                Name = selectedRow.Cells["Department"].Value?.ToString() ?? ""
                            }
                        }
                    };

                    // فتح نافذة عرض تفاصيل الوثيقة
                    // Open document viewer window
                    using (var viewerForm = new DocumentViewerForm(document))
                    {
                        viewerForm.ShowDialog();
                    }

                    Logger.LogInfo(string.Format("تم عرض تفاصيل الوثيقة من البحث المتقدم: {0}", serialNumber));
                }
                else
                {
                    MessageBox.Show("يرجى اختيار وثيقة لعرضها", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في عرض الوثيقة من البحث المتقدم", ex);
                MessageBox.Show("حدث خطأ أثناء عرض الوثيقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث تصدير النتائج
        /// Export results event handler
        /// </summary>
        private void BtnExportResults_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("وظيفة تصدير النتائج قيد التطوير...\nسيتم إضافة إمكانية تصدير نتائج البحث إلى Excel لاحقاً", 
                    "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم طلب تصدير نتائج البحث");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تصدير نتائج البحث", ex);
            }
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}

