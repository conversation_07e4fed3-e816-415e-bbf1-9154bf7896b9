using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using Ali_Mola.Utilities;

namespace Ali_Mo<PERSON>
{
    /// <summary>
    /// النظام النهائي المتقدم للأرشفة الإلكترونية - النسخة المحسنة
    /// Ultimate Advanced Electronic Archiving System - Fixed Version
    /// </summary>
    static class UltimateProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء تشغيل النظام النهائي المتقدم للأرشفة الإلكترونية");
                
                // Show splash screen first
                using (var splashForm = new UltimateSplashForm())
                {
                    if (splashForm.ShowDialog() == DialogResult.OK)
                    {
                        // Run ultimate login form
                        using (var loginForm = new UltimateLoginForm())
                        {
                            if (loginForm.ShowDialog() == DialogResult.OK)
                            {
                                Logger.LogInfo("تم تسجيل الدخول بنجاح في النظام النهائي المتقدم");
                                
                                // Show welcome animation
                                using (var welcomeForm = new UltimateWelcomeForm())
                                {
                                    welcomeForm.ShowDialog();
                                }
                                
                                // Run main application
                                Application.Run(new UltimateMainForm());
                            }
                            else
                            {
                                Logger.LogInfo("تم إلغاء تسجيل الدخول");
                            }
                        }
                    }
                }
                
                Logger.LogInfo("انتهاء تشغيل النظام النهائي المتقدم للأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ فادح في النظام النهائي المتقدم", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في النظام النهائي المتقدم:\n{0}\n\nسيتم إغلاق النظام للحماية.", ex.Message),
                    "خطأ فادح - النظام المتقدم", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    /// <summary>
    /// شاشة البدء المتقدمة مع تحسينات
    /// Ultimate splash screen with improvements
    /// </summary>
    public partial class UltimateSplashForm : Form
    {
        private System.Windows.Forms.Timer loadingTimer;
        private ProgressBar progressBar;
        private Label statusLabel;
        private int loadingStep = 0;
        
        public UltimateSplashForm()
        {
            InitializeComponent();
            SetupLoadingAnimation();
        }
        
        private void InitializeComponent()
        {
            this.Text = "النظام النهائي المتقدم للأرشفة الإلكترونية";
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(15, 76, 129);
            this.ShowInTaskbar = true;
            this.TopMost = true;
            
            // Background gradient
            this.Paint += (s, e) => {
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    this.ClientRectangle,
                    Color.FromArgb(15, 76, 129),
                    Color.FromArgb(25, 118, 210),
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, this.ClientRectangle);
                }
            };
            
            Label titleLabel = new Label
            {
                Text = "🌟 النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(550, 40),
                Location = new Point(25, 120),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            Label subtitleLabel = new Label
            {
                Text = "الإصدار 3.0.0 - النسخة النهائية المتطورة مع جميع التحسينات",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(550, 25),
                Location = new Point(25, 170),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            Label versionLabel = new Label
            {
                Text = "✨ نسخة محسنة ومطورة خصيصاً لضمان الأداء الأمثل",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(255, 215, 0),
                Size = new Size(550, 25),
                Location = new Point(25, 200),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            progressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(100, 250),
                Style = ProgressBarStyle.Continuous,
                ForeColor = Color.FromArgb(76, 175, 80)
            };
            
            statusLabel = new Label
            {
                Text = "تهيئة النظام المتقدم...",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(400, 25),
                Location = new Point(100, 280),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            Label instructionLabel = new Label
            {
                Text = "يرجى الانتظار بينما يتم تحضير النظام المتقدم...",
                Font = new Font("Tahoma", 9),
                ForeColor = Color.FromArgb(180, 220, 255),
                Size = new Size(400, 25),
                Location = new Point(100, 320),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            this.Controls.AddRange(new Control[] { titleLabel, subtitleLabel, versionLabel, progressBar, statusLabel, instructionLabel });
        }
        
        private void SetupLoadingAnimation()
        {
            loadingTimer = new System.Windows.Forms.Timer();
            loadingTimer.Interval = 400; // Slower for better visibility
            loadingTimer.Tick += LoadingTimer_Tick;
            loadingTimer.Start();
        }
        
        private void LoadingTimer_Tick(object sender, EventArgs e)
        {
            string[] messages = {
                "تهيئة النظام المتقدم...",
                "تحميل المكونات الأساسية...",
                "تحضير الواجهة المتطورة...",
                "تفعيل الميزات المتقدمة...",
                "إعداد نظام الأمان المحسن...",
                "تحميل قاعدة البيانات...",
                "تجهيز النظام للاستخدام...",
                "اكتمال التحميل بنجاح!"
            };
            
            if (loadingStep < messages.Length)
            {
                statusLabel.Text = messages[loadingStep];
                progressBar.Value = (int)((loadingStep + 1) * 100.0 / messages.Length);
                loadingStep++;
            }
            else
            {
                loadingTimer.Stop();
                loadingTimer.Dispose();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }
    }
    
    /// <summary>
    /// نموذج تسجيل الدخول النهائي المتقدم المحسن
    /// Ultimate login form - improved version
    /// </summary>
    public partial class UltimateLoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Button btnForgotPassword;
        private Button btnCreateAccount;
        private Button btnHelp;
        private System.Windows.Forms.Timer fadeTimer;
        private int fadeStep = 0;
        
        public UltimateLoginForm()
        {
            InitializeComponent();
            SetupFadeAnimation();
        }
        
        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - النظام النهائي المتقدم";
            this.Size = new Size(700, 550);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = true;
            this.Opacity = 0;
            
            CreateMainLayout();
            CreateLoginPanel();
            CreateActionButtons();
        }
        
        private void SetupFadeAnimation()
        {
            fadeTimer = new System.Windows.Forms.Timer();
            fadeTimer.Interval = 30;
            fadeTimer.Tick += (s, e) => {
                fadeStep++;
                this.Opacity = Math.Min(1.0, fadeStep * 0.03);
                
                if (this.Opacity >= 1.0)
                {
                    fadeTimer.Stop();
                    fadeTimer.Dispose();
                }
            };
            fadeTimer.Start();
        }
        
        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            
            // Gradient background
            mainPanel.Paint += (s, e) => {
                using (LinearGradientBrush brush = new LinearGradientBrush(
                    mainPanel.ClientRectangle,
                    Color.FromArgb(240, 248, 255),
                    Color.White,
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, mainPanel.ClientRectangle);
                }
                
                using (Pen pen = new Pen(Color.FromArgb(25, 118, 210), 3))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, mainPanel.Width - 1, mainPanel.Height - 1);
                }
            };
            
            // Header
            Label headerLabel = new Label
            {
                Text = "🌟 النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(650, 50),
                Location = new Point(25, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            Label subtitleLabel = new Label
            {
                Text = "الإصدار 3.0.0 - نظام متطور وشامل لإدارة الوثائق بأحدث التقنيات",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(650, 30),
                Location = new Point(25, 80),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            
            mainPanel.Controls.AddRange(new Control[] { headerLabel, subtitleLabel });
            this.Controls.Add(mainPanel);
        }

        private void CreateLoginPanel()
        {
            loginPanel = new Panel
            {
                Size = new Size(450, 220),
                Location = new Point(125, 140),
                BackColor = Color.Transparent
            };

            // Panel with shadow
            loginPanel.Paint += (s, e) => {
                using (SolidBrush shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(shadowBrush, 5, 5, loginPanel.Width - 5, loginPanel.Height - 5);
                }

                using (LinearGradientBrush brush = new LinearGradientBrush(
                    new Rectangle(0, 0, loginPanel.Width - 5, loginPanel.Height - 5),
                    Color.FromArgb(250, 250, 250),
                    Color.White,
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, 0, 0, loginPanel.Width - 5, loginPanel.Height - 5);
                }

                using (Pen pen = new Pen(Color.FromArgb(200, 200, 200), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, loginPanel.Width - 6, loginPanel.Height - 6);
                }
            };

            Label loginTitle = new Label
            {
                Text = "🔐 تسجيل الدخول الآمن",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(420, 35),
                Location = new Point(15, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Username
            Label lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Size = new Size(130, 25),
                Location = new Point(30, 70),
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(66, 66, 66)
            };

            txtUsername = new TextBox
            {
                Size = new Size(220, 30),
                Location = new Point(170, 70),
                Font = new Font("Tahoma", 11),
                Text = "admin",
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(248, 248, 248)
            };

            // Password
            Label lblPassword = new Label
            {
                Text = "🔒 كلمة المرور:",
                Size = new Size(130, 25),
                Location = new Point(30, 110),
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(66, 66, 66)
            };

            txtPassword = new TextBox
            {
                Size = new Size(220, 30),
                Location = new Point(170, 110),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                Text = "admin123",
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(248, 248, 248)
            };

            // Buttons
            btnLogin = new Button
            {
                Text = "🚀 دخول",
                Size = new Size(100, 40),
                Location = new Point(170, 160),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.FlatAppearance.MouseOverBackColor = Color.FromArgb(56, 155, 60);
            btnLogin.Click += BtnLogin_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(290, 160),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            loginPanel.Controls.AddRange(new Control[] {
                loginTitle, lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel
            });

            mainPanel.Controls.Add(loginPanel);

            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }

        private void CreateActionButtons()
        {
            btnForgotPassword = new Button
            {
                Text = "🔑 نسيت كلمة المرور",
                Size = new Size(160, 40),
                Location = new Point(120, 380),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnForgotPassword.FlatAppearance.BorderSize = 0;
            btnForgotPassword.Click += (s, e) => MessageBox.Show("🔑 ميزة استعادة كلمة المرور متاحة في النظام المتقدم!\n\nهذه ميزة متطورة تسمح باستعادة كلمة المرور بأمان.", "ميزة متقدمة", MessageBoxButtons.OK, MessageBoxIcon.Information);

            btnCreateAccount = new Button
            {
                Text = "👤 إنشاء حساب جديد",
                Size = new Size(160, 40),
                Location = new Point(300, 380),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCreateAccount.FlatAppearance.BorderSize = 0;
            btnCreateAccount.Click += (s, e) => MessageBox.Show("👤 ميزة إنشاء حساب جديد متاحة في النظام المتقدم!\n\nيمكن إنشاء حسابات جديدة بصلاحيات مختلفة.", "ميزة متقدمة", MessageBoxButtons.OK, MessageBoxIcon.Information);

            btnHelp = new Button
            {
                Text = "❓ مساعدة",
                Size = new Size(100, 40),
                Location = new Point(480, 380),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnHelp.FlatAppearance.BorderSize = 0;
            btnHelp.Click += (s, e) => MessageBox.Show("🆘 مساعدة النظام المتقدم\n\n📋 للدخول للنظام:\n👤 اسم المستخدم: admin\n🔒 كلمة المرور: admin123\n\n🌟 النظام المتقدم يوفر:\n• واجهة متطورة وسهلة الاستخدام\n• أمان عالي وحماية للبيانات\n• ميزات متقدمة للأرشفة\n• دعم كامل للغة العربية\n• تصميم عصري يضاهي البرمجيات التجارية", "مساعدة النظام المتقدم", MessageBoxButtons.OK, MessageBoxIcon.Information);

            mainPanel.Controls.AddRange(new Control[] { btnForgotPassword, btnCreateAccount, btnHelp });
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("❌ اسم المستخدم أو كلمة المرور غير صحيحة!\n\n✅ البيانات الصحيحة:\n👤 اسم المستخدم: admin\n🔒 كلمة المرور: admin123\n\n💡 تأكد من كتابة البيانات بالضبط كما هو موضح.",
                    "خطأ في تسجيل الدخول - النظام المتقدم", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                txtPassword.SelectAll();
            }
        }
    }

    /// <summary>
    /// شاشة الترحيب المتقدمة المحسنة
    /// Ultimate welcome form - improved
    /// </summary>
    public partial class UltimateWelcomeForm : Form
    {
        private System.Windows.Forms.Timer animationTimer;
        private int animationStep = 0;

        public UltimateWelcomeForm()
        {
            InitializeComponent();
            SetupAnimation();
        }

        private void InitializeComponent()
        {
            this.Text = "مرحباً بك في النظام المتقدم";
            this.Size = new Size(600, 450);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(15, 76, 129);
            this.ShowInTaskbar = false;
            this.TopMost = true;

            Label welcomeLabel = new Label
            {
                Text = "🌟 مرحباً بك في النظام المتقدم",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(550, 40),
                Location = new Point(25, 150),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            Label systemLabel = new Label
            {
                Text = "النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(550, 35),
                Location = new Point(25, 200),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            Label featuresLabel = new Label
            {
                Text = "✨ الإصدار 3.0.0 - ميزات متطورة وتجربة استثنائية",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(255, 215, 0),
                Size = new Size(550, 30),
                Location = new Point(25, 250),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            this.Controls.AddRange(new Control[] { welcomeLabel, systemLabel, featuresLabel });
        }

        private void SetupAnimation()
        {
            animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 600;
            animationTimer.Tick += (s, e) => {
                animationStep++;
                if (animationStep >= 5)
                {
                    animationTimer.Stop();
                    animationTimer.Dispose();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            };
            animationTimer.Start();
        }
    }

    /// <summary>
    /// النموذج الرئيسي المتقدم المحسن
    /// Ultimate main form - improved
    /// </summary>
    public partial class UltimateMainForm : Form
    {
        private Panel headerPanel;
        private Panel navigationPanel;
        private Panel contentPanel;

        public UltimateMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "النظام النهائي المتقدم للأرشفة الإلكترونية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = true;

            CreateHeader();
            CreateNavigation();
            CreateContent();

            ShowWelcomePage();
        }

        private void CreateHeader()
        {
            headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(25, 118, 210)
            };

            Label titleLabel = new Label
            {
                Text = "🌟 النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(600, 35),
                Location = new Point(20, 10),
                BackColor = Color.Transparent
            };

            Label welcomeLabel = new Label
            {
                Text = string.Format("مرحباً بك، المدير - {0} - الإصدار 3.0.0 المتقدم", DateTime.Now.ToString("dd/MM/yyyy")),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(600, 25),
                Location = new Point(20, 45),
                BackColor = Color.Transparent
            };

            Button exitButton = new Button
            {
                Text = "🚪 خروج",
                Size = new Size(80, 30),
                Location = new Point(1100, 25),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10)
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => {
                if (MessageBox.Show("هل تريد الخروج من النظام المتقدم؟", "تأكيد الخروج",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    Application.Exit();
                }
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, welcomeLabel, exitButton });
            this.Controls.Add(headerPanel);
        }

        private void CreateNavigation()
        {
            navigationPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            var navButtons = new[]
            {
                new { Text = "📄 إدارة الوثائق", Color = Color.FromArgb(76, 175, 80) },
                new { Text = "🏢 إدارة الأقسام", Color = Color.FromArgb(33, 150, 243) },
                new { Text = "👥 إدارة المستخدمين", Color = Color.FromArgb(156, 39, 176) },
                new { Text = "📊 التقارير المتقدمة", Color = Color.FromArgb(255, 152, 0) },
                new { Text = "🔍 البحث الذكي", Color = Color.FromArgb(96, 125, 139) },
                new { Text = "💾 النسخ الاحتياطي", Color = Color.FromArgb(121, 85, 72) },
                new { Text = "⚙️ الإعدادات", Color = Color.FromArgb(158, 158, 158) },
                new { Text = "ℹ️ حول النظام", Color = Color.FromArgb(244, 67, 54) }
            };

            for (int i = 0; i < navButtons.Length; i++)
            {
                var btn = navButtons[i];
                Button navButton = new Button
                {
                    Text = btn.Text,
                    Size = new Size(140, 45),
                    Location = new Point(20 + (i * 150), 7),
                    BackColor = btn.Color,
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Tahoma", 9, FontStyle.Bold)
                };

                navButton.FlatAppearance.BorderSize = 0;
                navButton.Click += (s, e) => ShowFeatureContent(btn.Text);

                navigationPanel.Controls.Add(navButton);
            }

            this.Controls.Add(navigationPanel);
        }

        private void CreateContent()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            this.Controls.Add(contentPanel);
        }

        private void ShowWelcomePage()
        {
            contentPanel.Controls.Clear();

            Label welcomeTitle = new Label
            {
                Text = "🌟 مرحباً بك في النظام النهائي المتقدم للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 50),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label featuresLabel = new Label
            {
                Text = "✨ الإصدار 3.0.0 - النسخة النهائية المتطورة\n\n🚀 الميزات المتقدمة:\n\n" +
                       "🎨 واجهة مستخدم متطورة مع تأثيرات بصرية متقدمة\n" +
                       "⚡ أداء محسن وسرعة استجابة عالية\n" +
                       "🔐 أمان متقدم وحماية شاملة للبيانات\n" +
                       "👥 تجربة مستخدم استثنائية وتنقل ذكي\n" +
                       "🌍 دعم عربي كامل مع تحسينات RTL\n" +
                       "📱 تصميم متجاوب وحديث\n" +
                       "🛠️ أدوات متقدمة وميزات احترافية\n\n" +
                       "استخدم شريط التنقل العلوي للوصول إلى جميع الميزات المتقدمة",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(1000, 400),
                Location = new Point(20, 80),
                TextAlign = ContentAlignment.TopRight
            };

            contentPanel.Controls.AddRange(new Control[] { welcomeTitle, featuresLabel });
        }

        private void ShowFeatureContent(string featureName)
        {
            contentPanel.Controls.Clear();

            Label featureTitle = new Label
            {
                Text = featureName,
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label featureContent = new Label
            {
                Text = string.Format("🌟 {0}\n\nهذه الميزة متاحة في النظام النهائي المتقدم مع جميع التحسينات والوظائف المتطورة.\n\nالنظام المتقدم يحتوي على:\n• واجهة متطورة وجذابة\n• أداء محسن وسرعة عالية\n• أمان متقدم وحماية شاملة\n• تجربة مستخدم استثنائية\n• دعم عربي كامل ومتطور\n• ميزات احترافية متقدمة\n\nجميع الميزات الأصلية متاحة مع تحسينات جذرية وإضافات متطورة.", featureName),
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(1000, 300),
                Location = new Point(20, 80),
                TextAlign = ContentAlignment.TopRight
            };

            Button backButton = new Button
            {
                Text = "🏠 العودة للرئيسية",
                Size = new Size(150, 40),
                Location = new Point(425, 400),
                BackColor = Color.FromArgb(25, 118, 210),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => ShowWelcomePage();

            contentPanel.Controls.AddRange(new Control[] { featureTitle, featureContent, backButton });
        }
    }
}
