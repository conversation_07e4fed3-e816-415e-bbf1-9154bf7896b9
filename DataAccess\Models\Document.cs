using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ali_Mola.DataAccess.Models
{
    /// <summary>
    /// نموذج الوثيقة - يمثل الوثائق في النظام
    /// Document Model - Represents documents in the system
    /// </summary>
    [Table("Documents")]
    public class Document
    {
        /// <summary>
        /// معرف الوثيقة الفريد
        /// Unique document identifier
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int DocumentId { get; set; }

        /// <summary>
        /// معرف الاضبارة التابع لها الوثيقة
        /// File box ID that owns this document
        /// </summary>
        [Required]
        [Display(Name = "الاضبارة")]
        public int FileBoxId { get; set; }

        /// <summary>
        /// نوع الوثيقة (صادر أو وارد)
        /// Document type (Outgoing or Incoming)
        /// </summary>
        [Required(ErrorMessage = "نوع الوثيقة مطلوب")]
        [StringLength(10)]
        [Display(Name = "نوع الوثيقة")]
        public string Type { get; set; } // "صادر" or "وارد"

        /// <summary>
        /// الرقم المسلسل للوثيقة (يتم توليده تلقائياً)
        /// Document serial number (auto-generated)
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "الرقم المسلسل")]
        public string SerialNumber { get; set; }

        /// <summary>
        /// رقم الوثيقة
        /// Document number
        /// </summary>
        [StringLength(50)]
        [Display(Name = "رقم الوثيقة")]
        public string DocumentNumber { get; set; }

        /// <summary>
        /// تاريخ الوثيقة
        /// Document date
        /// </summary>
        [Required(ErrorMessage = "تاريخ الوثيقة مطلوب")]
        [Display(Name = "تاريخ الوثيقة")]
        [DataType(DataType.Date)]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// تاريخ ووقت إدخال الوثيقة (يتم توليده تلقائياً)
        /// Document entry date and time (auto-generated)
        /// </summary>
        [Required]
        [Display(Name = "تاريخ الإدخال")]
        public DateTime EntryDate { get; set; }

        /// <summary>
        /// موضوع الوثيقة
        /// Document subject
        /// </summary>
        [Required(ErrorMessage = "موضوع الوثيقة مطلوب")]
        [StringLength(250, ErrorMessage = "موضوع الوثيقة يجب أن يكون أقل من 250 حرف")]
        [Display(Name = "الموضوع")]
        public string Subject { get; set; }

        /// <summary>
        /// من (المرسل)
        /// From (Sender)
        /// </summary>
        [StringLength(100)]
        [Display(Name = "من")]
        public string From { get; set; }

        /// <summary>
        /// إلى (المستقبل)
        /// To (Receiver)
        /// </summary>
        [StringLength(100)]
        [Display(Name = "إلى")]
        public string To { get; set; }

        /// <summary>
        /// الاضبارة التابع لها الوثيقة
        /// File box that owns this document
        /// </summary>
        [ForeignKey("FileBoxId")]
        public virtual FileBox FileBox { get; set; }

        /// <summary>
        /// مجموعة المرفقات التابعة لهذه الوثيقة
        /// Collection of attachments belonging to this document
        /// </summary>
        public virtual ICollection<Attachment> Attachments { get; set; }

        /// <summary>
        /// منشئ الوثيقة
        /// Document constructor
        /// </summary>
        public Document()
        {
            Attachments = new HashSet<Attachment>();
            EntryDate = DateTime.Now;
            DocumentDate = DateTime.Today;
        }
    }
}
