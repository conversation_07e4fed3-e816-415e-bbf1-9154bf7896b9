using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.BusinessLogic;
using <PERSON>_<PERSON><PERSON>.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج تغيير كلمة المرور
    /// Change password form
    /// </summary>
    public partial class ChangePasswordForm : Form
    {
        #region Controls
        private TextBox txtCurrentPassword;
        private TextBox txtNewPassword;
        private TextBox txtConfirmPassword;
        private Button btnSave;
        private Button btnCancel;
        private Label lblStrength;
        private ProgressBar progressStrength;
        #endregion

        /// <summary>
        /// منشئ نموذج تغيير كلمة المرور
        /// Change password form constructor
        /// </summary>
        public ChangePasswordForm()
        {
            InitializeComponent();
            SetupForm();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "تغيير كلمة المرور";
            this.Size = new Size(450, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(245, 245, 245);

            // إعداد الخط العربي
            // Setup Arabic font
            Font arabicFont = new Font("Tahoma", 10F, FontStyle.Regular);
            this.Font = arabicFont;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        /// <summary>
        /// إنشاء عناصر التحكم
        /// Create controls
        /// </summary>
        private void CreateControls()
        {
            // لوحة رئيسية
            // Main panel
            Panel mainPanel = new Panel
            {
                Size = new Size(410, 330),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(mainPanel);

            // عنوان النموذج
            // Form title
            Label titleLabel = new Label
            {
                Text = "تغيير كلمة المرور",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(200, 30),
                Location = new Point(20, 20)
            };
            mainPanel.Controls.Add(titleLabel);

            // أيقونة الأمان
            // Security icon
            Panel iconPanel = new Panel
            {
                Size = new Size(60, 60),
                Location = new Point(320, 15),
                BackColor = Color.FromArgb(255, 152, 0)
            };

            Label iconLabel = new Label
            {
                Text = "🔒",
                Font = new Font("Segoe UI Emoji", 20F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            mainPanel.Controls.Add(iconPanel);

            int yPos = 90;
            int spacing = 50;

            // كلمة المرور الحالية
            // Current password
            CreatePasswordField("كلمة المرور الحالية:", ref yPos, spacing, out txtCurrentPassword);

            // كلمة المرور الجديدة
            // New password
            CreatePasswordField("كلمة المرور الجديدة:", ref yPos, spacing, out txtNewPassword);
            txtNewPassword.TextChanged += TxtNewPassword_TextChanged;

            // تأكيد كلمة المرور
            // Confirm password
            CreatePasswordField("تأكيد كلمة المرور:", ref yPos, spacing, out txtConfirmPassword);
            txtConfirmPassword.TextChanged += TxtConfirmPassword_TextChanged;

            // مؤشر قوة كلمة المرور
            // Password strength indicator
            Label strengthLabel = new Label
            {
                Text = "قوة كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(20, yPos),
                TextAlign = ContentAlignment.MiddleRight
            };
            mainPanel.Controls.Add(strengthLabel);

            progressStrength = new ProgressBar
            {
                Size = new Size(200, 20),
                Location = new Point(150, yPos + 2),
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };
            mainPanel.Controls.Add(progressStrength);

            lblStrength = new Label
            {
                Text = "ضعيفة",
                Size = new Size(60, 20),
                Location = new Point(360, yPos + 2),
                ForeColor = Color.Red,
                Font = new Font("Tahoma", 9F)
            };
            mainPanel.Controls.Add(lblStrength);

            yPos += 40;

            // معلومات كلمة المرور
            // Password requirements
            Label requirementsLabel = new Label
            {
                Text = "متطلبات كلمة المرور:\n• 8 أحرف على الأقل\n• حروف كبيرة وصغيرة\n• أرقام ورموز",
                Size = new Size(350, 60),
                Location = new Point(20, yPos),
                ForeColor = Color.Gray,
                Font = new Font("Tahoma", 9F)
            };
            mainPanel.Controls.Add(requirementsLabel);

            yPos += 80;

            // زر الحفظ
            // Save button
            btnSave = new Button
            {
                Text = "حفظ التغييرات",
                Size = new Size(120, 35),
                Location = new Point(150, yPos),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Enabled = false
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;
            mainPanel.Controls.Add(btnSave);

            // زر الإلغاء
            // Cancel button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 35),
                Location = new Point(280, yPos),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            mainPanel.Controls.Add(btnCancel);

            // تعيين الأزرار الافتراضية
            // Set default buttons
            this.AcceptButton = btnSave;
            this.CancelButton = btnCancel;

            // تركيز على كلمة المرور الحالية
            // Focus on current password
            txtCurrentPassword.Focus();
        }

        /// <summary>
        /// إنشاء حقل كلمة مرور
        /// Create password field
        /// </summary>
        private void CreatePasswordField(string labelText, ref int yPos, int spacing, out TextBox textBox)
        {
            Label label = new Label
            {
                Text = labelText,
                Size = new Size(130, 25),
                Location = new Point(20, yPos),
                TextAlign = ContentAlignment.MiddleRight
            };

            textBox = new TextBox
            {
                Size = new Size(230, 25),
                Location = new Point(160, yPos),
                UseSystemPasswordChar = true,
                Font = new Font("Tahoma", 10F)
            };

            mainPanel.Controls.Add(label);
            mainPanel.Controls.Add(textBox);

            yPos += spacing;
        }

        /// <summary>
        /// تقييم قوة كلمة المرور
        /// Evaluate password strength
        /// </summary>
        private void EvaluatePasswordStrength(string password)
        {
            int score = 0;
            string strength = "ضعيفة";
            Color color = Color.Red;

            if (password.Length >= 8) score += 25;
            if (password.Any(char.IsUpper)) score += 25;
            if (password.Any(char.IsLower)) score += 25;
            if (password.Any(char.IsDigit)) score += 15;
            if (password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c))) score += 10;

            if (score >= 75)
            {
                strength = "قوية";
                color = Color.Green;
            }
            else if (score >= 50)
            {
                strength = "متوسطة";
                color = Color.Orange;
            }

            progressStrength.Value = Math.Min(score, 100);
            lblStrength.Text = strength;
            lblStrength.ForeColor = color;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        private bool ValidateData()
        {
            if (string.IsNullOrEmpty(txtCurrentPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور الحالية", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrentPassword.Focus();
                return false;
            }

            if (string.IsNullOrEmpty(txtNewPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور الجديدة", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }

            if (txtNewPassword.Text.Length < 8)
            {
                MessageBox.Show("كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }

            if (txtNewPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة وتأكيدها غير متطابقتين", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return false;
            }

            return true;
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث تغيير كلمة المرور الجديدة
        /// New password change event handler
        /// </summary>
        private void TxtNewPassword_TextChanged(object sender, EventArgs e)
        {
            EvaluatePasswordStrength(txtNewPassword.Text);
            CheckFormValidity();
        }

        /// <summary>
        /// معالج حدث تغيير تأكيد كلمة المرور
        /// Confirm password change event handler
        /// </summary>
        private void TxtConfirmPassword_TextChanged(object sender, EventArgs e)
        {
            CheckFormValidity();
        }

        /// <summary>
        /// فحص صحة النموذج
        /// Check form validity
        /// </summary>
        private void CheckFormValidity()
        {
            bool isValid = !string.IsNullOrEmpty(txtCurrentPassword.Text) &&
                          !string.IsNullOrEmpty(txtNewPassword.Text) &&
                          !string.IsNullOrEmpty(txtConfirmPassword.Text) &&
                          txtNewPassword.Text == txtConfirmPassword.Text &&
                          txtNewPassword.Text.Length >= 8;

            btnSave.Enabled = isValid;
        }

        /// <summary>
        /// معالج حدث الحفظ
        /// Save event handler
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateData())
                    return;

                // تعطيل الأزرار أثناء المعالجة
                // Disable buttons during processing
                btnSave.Enabled = false;
                btnCancel.Enabled = false;
                this.Cursor = Cursors.WaitCursor;

                // تغيير كلمة المرور
                // Change password
                bool success = AuthenticationService.ChangePassword(txtCurrentPassword.Text, txtNewPassword.Text);

                if (success)
                {
                    Logger.LogInfo("تم تغيير كلمة المرور بنجاح");
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("كلمة المرور الحالية غير صحيحة", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtCurrentPassword.Focus();
                    txtCurrentPassword.SelectAll();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تغيير كلمة المرور", ex);
                MessageBox.Show("حدث خطأ أثناء تغيير كلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // إعادة تمكين الأزرار
                // Re-enable buttons
                btnSave.Enabled = true;
                btnCancel.Enabled = true;
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// معالج حدث الإلغاء
        /// Cancel event handler
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion
    }
}
