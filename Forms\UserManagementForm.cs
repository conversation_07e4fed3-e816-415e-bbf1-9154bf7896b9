﻿using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;
using Ali_<PERSON><PERSON>.BusinessLogic;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using Ali_Mola.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج إدارة المستخدمين
    /// User management form
    /// </summary>
    public partial class UserManagementForm : Form
    {
        #region Controls
        private DataGridView dgvUsers;
        private Button btnAddUser;
        private Button btnEditUser;
        private Button btnDeleteUser;
        private Button btnResetPassword;
        private Button btnRefresh;
        private Button btnClose;
        private Label lblUsersCount;
        #endregion

        /// <summary>
        /// منشئ نموذج إدارة المستخدمين
        /// User management form constructor
        /// </summary>
        public UserManagementForm()
        {
            InitializeComponent();
            SetupForm();
            LoadUsers();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // التحقق من صلاحيات المدير
            // Check admin permissions
            if (!AuthenticationService.IsAdmin())
            {
                MessageBox.Show("هذه الصفحة مخصصة للمدير فقط", "غير مصرح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                this.Close();
                return;
            }

            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "إدارة المستخدمين";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateHeader();
            CreateUsersGrid();
            CreateActionButtons();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء رأس النموذج
        /// Create form header
        /// </summary>
        private void CreateHeader()
        {
            // عنوان النموذج
            // Form title
            Label titleLabel = new Label
            {
                Text = "إدارة المستخدمين",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(250, 30),
                Location = new Point(20, 20)
            };
            this.Controls.Add(titleLabel);

            // أيقونة المستخدمين
            // Users icon
            Panel iconPanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(780, 15),
                BackColor = Color.FromArgb(33, 150, 243)
            };

            Label iconLabel = new Label
            {
                Text = "👥",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            this.Controls.Add(iconPanel);

            // معلومات إضافية
            // Additional info
            Label infoLabel = new Label
            {
                Text = "يمكنك إضافة وتعديل وحذف المستخدمين وإعادة تعيين كلمات المرور",
                Size = new Size(500, 25),
                Location = new Point(20, 60),
                ForeColor = Color.Gray,
                Font = new Font("Tahoma", 9F)
            };
            this.Controls.Add(infoLabel);

            // عداد المستخدمين
            // Users counter
            lblUsersCount = new Label
            {
                Text = "عدد المستخدمين: 0",
                Size = new Size(150, 25),
                Location = new Point(20, 90),
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210)
            };
            this.Controls.Add(lblUsersCount);
        }

        /// <summary>
        /// إنشاء جدول المستخدمين
        /// Create users grid
        /// </summary>
        private void CreateUsersGrid()
        {
            dgvUsers = new DataGridView
            {
                Size = new Size(840, 350),
                Location = new Point(20, 130),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;
            this.Controls.Add(dgvUsers);

            // إعداد أعمدة الجدول
            // Setup grid columns
            SetupUsersGrid();
        }

        /// <summary>
        /// إعداد جدول المستخدمين
        /// Setup users grid
        /// </summary>
        private void SetupUsersGrid()
        {
            dgvUsers.Columns.Add("UserId", "المعرف");
            dgvUsers.Columns.Add("Username", "اسم المستخدم");
            dgvUsers.Columns.Add("FullName", "الاسم الكامل");
            dgvUsers.Columns.Add("Role", "الدور");
            dgvUsers.Columns.Add("IsActive", "نشط");
            dgvUsers.Columns.Add("LastLogin", "آخر دخول");
            dgvUsers.Columns.Add("CreatedDate", "تاريخ الإنشاء");

            // تنسيق الأعمدة
            // Format columns
            dgvUsers.Columns["UserId"].Width = 60;
            dgvUsers.Columns["Username"].Width = 120;
            dgvUsers.Columns["FullName"].Width = 150;
            dgvUsers.Columns["Role"].Width = 100;
            dgvUsers.Columns["IsActive"].Width = 60;
            dgvUsers.Columns["LastLogin"].Width = 120;
            dgvUsers.Columns["CreatedDate"].Width = 120;
        }

        /// <summary>
        /// إنشاء أزرار العمليات
        /// Create action buttons
        /// </summary>
        private void CreateActionButtons()
        {
            // زر إضافة مستخدم
            // Add user button
            btnAddUser = new Button
            {
                Text = "➕ إضافة مستخدم",
                Size = new Size(130, 40),
                Location = new Point(20, 500),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnAddUser.FlatAppearance.BorderSize = 0;
            btnAddUser.Click += BtnAddUser_Click;
            this.Controls.Add(btnAddUser);

            // زر تعديل مستخدم
            // Edit user button
            btnEditUser = new Button
            {
                Text = "✏️ تعديل",
                Size = new Size(100, 40),
                Location = new Point(170, 500),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F),
                Enabled = false
            };
            btnEditUser.FlatAppearance.BorderSize = 0;
            btnEditUser.Click += BtnEditUser_Click;
            this.Controls.Add(btnEditUser);

            // زر حذف مستخدم
            // Delete user button
            btnDeleteUser = new Button
            {
                Text = "🗑️ حذف",
                Size = new Size(100, 40),
                Location = new Point(290, 500),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F),
                Enabled = false
            };
            btnDeleteUser.FlatAppearance.BorderSize = 0;
            btnDeleteUser.Click += BtnDeleteUser_Click;
            this.Controls.Add(btnDeleteUser);

            // زر إعادة تعيين كلمة المرور
            // Reset password button
            btnResetPassword = new Button
            {
                Text = "🔑 إعادة تعيين كلمة المرور",
                Size = new Size(180, 40),
                Location = new Point(410, 500),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F),
                Enabled = false
            };
            btnResetPassword.FlatAppearance.BorderSize = 0;
            btnResetPassword.Click += BtnResetPassword_Click;
            this.Controls.Add(btnResetPassword);

            // زر تحديث
            // Refresh button
            btnRefresh = new Button
            {
                Text = "🔄 تحديث",
                Size = new Size(100, 40),
                Location = new Point(610, 500),
                BackColor = Color.FromArgb(96, 125, 139),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnRefresh.FlatAppearance.BorderSize = 0;
            btnRefresh.Click += BtnRefresh_Click;
            this.Controls.Add(btnRefresh);
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر الإغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(120, 40),
                Location = new Point(740, 500),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);
        }

        /// <summary>
        /// تحميل المستخدمين
        /// Load users
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                dgvUsers.Rows.Clear();

                // إضافة بيانات تجريبية للمستخدمين
                // Add sample user data
                dgvUsers.Rows.Add("1", "admin", "مدير النظام", "مدير", "نعم", DateTime.Now.AddHours(-2).ToString("dd/MM/yyyy HH:mm"), DateTime.Now.AddMonths(-6).ToString("dd/MM/yyyy"));
                dgvUsers.Rows.Add("2", "user1", "أحمد محمد", "مستخدم", "نعم", DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy HH:mm"), DateTime.Now.AddMonths(-3).ToString("dd/MM/yyyy"));
                dgvUsers.Rows.Add("3", "user2", "فاطمة علي", "مستخدم", "نعم", DateTime.Now.AddDays(-3).ToString("dd/MM/yyyy HH:mm"), DateTime.Now.AddMonths(-2).ToString("dd/MM/yyyy"));
                dgvUsers.Rows.Add("4", "user3", "محمد حسن", "مستخدم", "لا", DateTime.Now.AddDays(-14).ToString("dd/MM/yyyy HH:mm"), DateTime.Now.AddMonths(-1).ToString("dd/MM/yyyy"));

                // تحديث عداد المستخدمين
                // Update users counter
                lblUsersCount.Text = string.Format("عدد المستخدمين: {0}", dgvUsers.Rows.Count);

                Logger.LogInfo("تم تحميل قائمة المستخدمين");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل المستخدمين", ex);
                MessageBox.Show("حدث خطأ أثناء تحميل المستخدمين", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث تغيير اختيار المستخدم
        /// User selection change event handler
        /// </summary>
        private void DgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = dgvUsers.SelectedRows.Count > 0;
            btnEditUser.Enabled = hasSelection;
            btnDeleteUser.Enabled = hasSelection;
            btnResetPassword.Enabled = hasSelection;

            // منع حذف المدير الحالي
            // Prevent deleting current admin
            if (hasSelection)
            {
                var selectedRow = dgvUsers.SelectedRows[0];
                string username = selectedRow.Cells["Username"].Value?.ToString();
                if (username == AuthenticationService.CurrentUser?.Username)
                {
                    btnDeleteUser.Enabled = false;
                }
            }
        }

        /// <summary>
        /// معالج حدث إضافة مستخدم
        /// Add user event handler
        /// </summary>
        private void BtnAddUser_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("نموذج إضافة مستخدم جديد قيد التطوير...\nسيتم إضافة إمكانية إنشاء مستخدمين جدد لاحقاً", 
                    "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم طلب إضافة مستخدم جديد");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة مستخدم", ex);
            }
        }

        /// <summary>
        /// معالج حدث تعديل مستخدم
        /// Edit user event handler
        /// </summary>
        private void BtnEditUser_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvUsers.SelectedRows[0];
                    string username = selectedRow.Cells["Username"].Value?.ToString();
                    
                    MessageBox.Show(string.Format("نموذج تعديل المستخدم '{0}' قيد التطوير...\nسيتم إضافة إمكانية تعديل بيانات المستخدمين لاحقاً", username), 
                        "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    Logger.LogInfo(string.Format("تم طلب تعديل المستخدم: {0}", username));
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تعديل المستخدم", ex);
            }
        }

        /// <summary>
        /// معالج حدث حذف مستخدم
        /// Delete user event handler
        /// </summary>
        private void BtnDeleteUser_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvUsers.SelectedRows[0];
                    string username = selectedRow.Cells["Username"].Value?.ToString();
                    
                    if (MessageBox.Show(string.Format("هل أنت متأكد من حذف المستخدم '{0}'؟\nهذا الإجراء لا يمكن التراجع عنه!", username), 
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                    {
                        // محاكاة حذف المستخدم
                        // Simulate user deletion
                        dgvUsers.Rows.Remove(selectedRow);
                        lblUsersCount.Text = string.Format("عدد المستخدمين: {0}", dgvUsers.Rows.Count);
                        
                        MessageBox.Show("تم حذف المستخدم بنجاح!", "نجح الحذف", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        Logger.LogInfo(string.Format("تم حذف المستخدم: {0}", username));
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حذف المستخدم", ex);
                MessageBox.Show("حدث خطأ أثناء حذف المستخدم", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث إعادة تعيين كلمة المرور
        /// Reset password event handler
        /// </summary>
        private void BtnResetPassword_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvUsers.SelectedRows[0];
                    string username = selectedRow.Cells["Username"].Value?.ToString();
                    
                    if (MessageBox.Show(string.Format("هل تريد إعادة تعيين كلمة مرور المستخدم '{0}'؟\nسيتم تعيين كلمة مرور افتراضية جديدة.", username), 
                        "تأكيد إعادة التعيين", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        // محاكاة إعادة تعيين كلمة المرور
                        // Simulate password reset
                        string newPassword = "123456"; // كلمة مرور افتراضية
                        
                        MessageBox.Show(string.Format("تم إعادة تعيين كلمة المرور بنجاح!\nكلمة المرور الجديدة: {0}\nيُنصح بتغييرها عند أول تسجيل دخول.", newPassword), 
                            "نجحت العملية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        Logger.LogInfo(string.Format("تم إعادة تعيين كلمة مرور المستخدم: {0}", username));
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إعادة تعيين كلمة المرور", ex);
                MessageBox.Show("حدث خطأ أثناء إعادة تعيين كلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث التحديث
        /// Refresh event handler
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadUsers();
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}

