using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Linq;
using Ali_Mola.Utilities;

namespace Ali_Mola
{
    /// <summary>
    /// نظام إدارة المستخدمين المتقدم
    /// Advanced User Management System
    /// </summary>
    public static class UserManager
    {
        private static readonly string UsersFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "users.dat");
        private static Dictionary<string, UserAccount> users = new Dictionary<string, UserAccount>();
        
        static UserManager()
        {
            LoadUsers();
        }
        
        public static bool CreateUser(string username, string password, string email, string fullName, string securityQuestion, string securityAnswer)
        {
            if (users.ContainsKey(username.ToLower()))
                return false;
                
            var user = new UserAccount
            {
                Username = username,
                PasswordHash = HashPassword(password),
                Email = email,
                FullName = fullName,
                SecurityQuestion = securityQuestion,
                SecurityAnswerHash = HashPassword(securityAnswer),
                CreatedDate = DateTime.Now,
                IsActive = true
            };
            
            users[username.ToLower()] = user;
            SaveUsers();
            return true;
        }
        
        public static bool ValidateUser(string username, string password)
        {
            if (username == "admin" && password == "admin123")
                return true;
                
            var key = username.ToLower();
            if (users.ContainsKey(key))
            {
                var user = users[key];
                return user.IsActive && VerifyPassword(password, user.PasswordHash);
            }
            return false;
        }
        
        public static bool ChangePassword(string username, string oldPassword, string newPassword)
        {
            if (username == "admin")
                return false; // Cannot change admin password
                
            var key = username.ToLower();
            if (users.ContainsKey(key))
            {
                var user = users[key];
                if (VerifyPassword(oldPassword, user.PasswordHash))
                {
                    user.PasswordHash = HashPassword(newPassword);
                    SaveUsers();
                    return true;
                }
            }
            return false;
        }
        
        public static bool ResetPassword(string username, string email, string securityAnswer, string newPassword)
        {
            var key = username.ToLower();
            if (users.ContainsKey(key))
            {
                var user = users[key];
                if (user.Email.ToLower() == email.ToLower() && VerifyPassword(securityAnswer, user.SecurityAnswerHash))
                {
                    user.PasswordHash = HashPassword(newPassword);
                    SaveUsers();
                    return true;
                }
            }
            return false;
        }
        
        public static UserAccount GetUser(string username)
        {
            var key = username.ToLower();
            return users.ContainsKey(key) ? users[key] : null;
        }
        
        private static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SALT_KEY_2025"));
                return Convert.ToBase64String(hashedBytes);
            }
        }
        
        private static bool VerifyPassword(string password, string hash)
        {
            return HashPassword(password) == hash;
        }
        
        private static void LoadUsers()
        {
            try
            {
                if (File.Exists(UsersFilePath))
                {
                    var lines = File.ReadAllLines(UsersFilePath, Encoding.UTF8);
                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line)) continue;
                        
                        var parts = line.Split('|');
                        if (parts.Length >= 7)
                        {
                            var user = new UserAccount
                            {
                                Username = parts[0],
                                PasswordHash = parts[1],
                                Email = parts[2],
                                FullName = parts[3],
                                SecurityQuestion = parts[4],
                                SecurityAnswerHash = parts[5],
                                CreatedDate = DateTime.Parse(parts[6]),
                                IsActive = parts.Length > 7 ? bool.Parse(parts[7]) : true
                            };
                            users[user.Username.ToLower()] = user;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل بيانات المستخدمين", ex);
            }
        }
        
        private static void SaveUsers()
        {
            try
            {
                var lines = users.Values.Select(u => 
                    string.Format("{0}|{1}|{2}|{3}|{4}|{5}|{6}|{7}", 
                        u.Username, u.PasswordHash, u.Email, u.FullName, u.SecurityQuestion, u.SecurityAnswerHash, 
                        u.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"), u.IsActive));
                File.WriteAllLines(UsersFilePath, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حفظ بيانات المستخدمين", ex);
            }
        }
    }
    
    /// <summary>
    /// فئة حساب المستخدم
    /// User Account Class
    /// </summary>
    public class UserAccount
    {
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string Email { get; set; }
        public string FullName { get; set; }
        public string SecurityQuestion { get; set; }
        public string SecurityAnswerHash { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// البرنامج الرئيسي للنظام المحسن
    /// Enhanced System Main Program
    /// </summary>
    static class EnhancedProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء تشغيل النظام المحسن للأرشفة الإلكترونية");
                
                // Show welcome animation
                using (var welcomeForm = new EnhancedWelcomeForm())
                {
                    if (welcomeForm.ShowDialog() == DialogResult.OK)
                    {
                        // Show login form
                        using (var loginForm = new EnhancedLoginForm())
                        {
                            if (loginForm.ShowDialog() == DialogResult.OK)
                            {
                                Logger.LogInfo("تم تسجيل الدخول بنجاح في النظام المحسن");
                                
                                // Run main application
                                Application.Run(new EnhancedMainForm());
                            }
                            else
                            {
                                Logger.LogInfo("تم إلغاء تسجيل الدخول");
                            }
                        }
                    }
                }
                
                Logger.LogInfo("انتهاء تشغيل النظام المحسن للأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ فادح في النظام المحسن", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في النظام المحسن:\n{0}\n\nسيتم إغلاق النظام للحماية.", ex.Message),
                    "خطأ فادح - النظام المحسن", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// نموذج الترحيب المتحرك
    /// Enhanced Welcome Form
    /// </summary>
    public partial class EnhancedWelcomeForm : Form
    {
        private System.Windows.Forms.Timer animationTimer;
        private int animationStep = 0;

        public EnhancedWelcomeForm()
        {
            InitializeComponent();
            SetupAnimation();
        }

        private void InitializeComponent()
        {
            this.Text = "مرحباً بك في النظام المحسن";
            this.Size = new Size(600, 450);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(15, 76, 129);
            this.ShowInTaskbar = true;
            this.TopMost = true;

            Label welcomeLabel = new Label
            {
                Text = "🌟 مرحباً بك في النظام المحسن",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(550, 40),
                Location = new Point(25, 150),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            Label systemLabel = new Label
            {
                Text = "النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(550, 35),
                Location = new Point(25, 200),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            Label featuresLabel = new Label
            {
                Text = "✨ الإصدار 2.0.0 - ميزات متطورة وتجربة استثنائية",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(255, 215, 0),
                Size = new Size(550, 30),
                Location = new Point(25, 250),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            this.Controls.AddRange(new Control[] { welcomeLabel, systemLabel, featuresLabel });
        }

        private void SetupAnimation()
        {
            animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 600;
            animationTimer.Tick += (s, e) => {
                animationStep++;
                if (animationStep >= 5)
                {
                    animationTimer.Stop();
                    animationTimer.Dispose();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            };
            animationTimer.Start();
        }
    }

    /// <summary>
    /// نموذج تسجيل الدخول المحسن
    /// Enhanced Login Form
    /// </summary>
    public partial class EnhancedLoginForm : Form
    {
        private Panel mainPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Button btnForgotPassword;
        private Button btnCreateAccount;

        public EnhancedLoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - النظام المحسن";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = true;
            this.TopMost = true;

            CreateMainLayout();
        }

        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            Label headerLabel = new Label
            {
                Text = "🔐 تسجيل الدخول",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(440, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Username
            Label lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Size = new Size(120, 25),
                Location = new Point(50, 80),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtUsername = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(180, 80),
                Font = new Font("Tahoma", 11),
                Text = "admin",
                BorderStyle = BorderStyle.FixedSingle
            };

            // Password
            Label lblPassword = new Label
            {
                Text = "🔒 كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(50, 120),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(180, 120),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                Text = "admin123",
                BorderStyle = BorderStyle.FixedSingle
            };

            // Buttons
            btnLogin = new Button
            {
                Text = "🚀 دخول",
                Size = new Size(100, 40),
                Location = new Point(180, 170),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(300, 170),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            btnForgotPassword = new Button
            {
                Text = "🔑 نسيت كلمة المرور",
                Size = new Size(150, 35),
                Location = new Point(120, 230),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            btnForgotPassword.FlatAppearance.BorderSize = 0;
            btnForgotPassword.Click += BtnForgotPassword_Click;

            btnCreateAccount = new Button
            {
                Text = "👤 إنشاء حساب جديد",
                Size = new Size(150, 35),
                Location = new Point(290, 230),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            btnCreateAccount.FlatAppearance.BorderSize = 0;
            btnCreateAccount.Click += BtnCreateAccount_Click;

            mainPanel.Controls.AddRange(new Control[] {
                headerLabel, lblUsername, txtUsername, lblPassword, txtPassword,
                btnLogin, btnCancel, btnForgotPassword, btnCreateAccount
            });

            this.Controls.Add(mainPanel);
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم وكلمة المرور!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (UserManager.ValidateUser(txtUsername.Text, txtPassword.Text))
            {
                Logger.LogInfo(string.Format("تم تسجيل الدخول بنجاح للمستخدم: {0}", txtUsername.Text));
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("❌ اسم المستخدم أو كلمة المرور غير صحيحة!\n\n✅ للحساب الافتراضي:\n👤 اسم المستخدم: admin\n🔒 كلمة المرور: admin123\n\n💡 أو استخدم حساب مسجل مسبقاً",
                    "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                txtPassword.SelectAll();
            }
        }

        private void BtnForgotPassword_Click(object sender, EventArgs e)
        {
            try
            {
                using (var forgotForm = new ForgotPasswordForm())
                {
                    forgotForm.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج استعادة كلمة المرور", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج استعادة كلمة المرور", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCreateAccount_Click(object sender, EventArgs e)
        {
            try
            {
                using (var createForm = new CreateAccountForm())
                {
                    if (createForm.ShowDialog(this) == DialogResult.OK)
                    {
                        MessageBox.Show("✅ تم إنشاء الحساب بنجاح!\n\nيمكنك الآن تسجيل الدخول باستخدام الحساب الجديد.",
                            "نجح إنشاء الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج إنشاء حساب جديد", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج إنشاء الحساب", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
