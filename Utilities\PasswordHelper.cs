using System;
using System.Security.Cryptography;
using System.Text;

namespace Ali_Mola.Utilities
{
    /// <summary>
    /// مساعد كلمات المرور - يوفر وظائف تشفير والتحقق من كلمات المرور
    /// Password Helper - Provides password hashing and verification functions
    /// </summary>
    public static class PasswordHelper
    {
        /// <summary>
        /// تشفير كلمة المرور باستخدام SHA256
        /// Hash password using SHA256
        /// </summary>
        /// <param name="password">كلمة المرور الخام</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("كلمة المرور لا يمكن أن تكون فارغة", nameof(password));

            using (var sha256 = SHA256.Create())
            {
                byte[] hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// Verify password
        /// </summary>
        /// <param name="password">كلمة المرور الخام</param>
        /// <param name="hashedPassword">كلمة المرور المشفرة</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hashedPassword))
                return false;

            string hashOfInput = HashPassword(password);
            return hashOfInput.Equals(hashedPassword, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// توليد كلمة مرور عشوائية
        /// Generate random password
        /// </summary>
        /// <param name="length">طول كلمة المرور</param>
        /// <returns>كلمة مرور عشوائية</returns>
        public static string GenerateRandomPassword(int length = 8)
        {
            const string validChars = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*?_-";
            var random = new Random();
            var chars = new char[length];

            for (int i = 0; i < length; i++)
            {
                chars[i] = validChars[random.Next(validChars.Length)];
            }

            return new string(chars);
        }
    }
}
