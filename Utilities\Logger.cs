using System;
using System.IO;

namespace Ali_Mola.Utilities
{
    /// <summary>
    /// مسجل الأحداث - يوفر وظائف تسجيل الأحداث والأخطاء
    /// Logger - Provides event and error logging functionality
    /// </summary>
    public static class Logger
    {
        private static readonly string LogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
        private static readonly object LockObject = new object();

        /// <summary>
        /// تهيئة مجلد السجلات
        /// Initialize logs directory
        /// </summary>
        static Logger()
        {
            try
            {
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل إنشاء مجلد السجلات، استخدم مجلد مؤقت
                // If creating logs directory fails, use temp directory
                Console.WriteLine($"فشل في إنشاء مجلد السجلات: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل معلومات
        /// Log information
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void LogInfo(string message)
        {
            Log("INFO", message);
        }

        /// <summary>
        /// تسجيل خطأ
        /// Log error
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="exception">الاستثناء</param>
        public static void LogError(string message, Exception exception = null)
        {
            string fullMessage = message;
            if (exception != null)
            {
                fullMessage += $"\nتفاصيل الخطأ: {exception.Message}\nStack Trace: {exception.StackTrace}";
            }
            Log("ERROR", fullMessage);
        }

        /// <summary>
        /// تسجيل تحذير
        /// Log warning
        /// </summary>
        /// <param name="message">رسالة التحذير</param>
        public static void LogWarning(string message)
        {
            Log("WARNING", message);
        }

        /// <summary>
        /// تسجيل رسالة عامة
        /// Log general message
        /// </summary>
        /// <param name="level">مستوى السجل</param>
        /// <param name="message">الرسالة</param>
        private static void Log(string level, string message)
        {
            try
            {
                lock (LockObject)
                {
                    string fileName = $"Log_{DateTime.Now:yyyyMMdd}.txt";
                    string filePath = Path.Combine(LogDirectory, fileName);
                    
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] {message}{Environment.NewLine}";
                    
                    File.AppendAllText(filePath, logEntry, System.Text.Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل الكتابة في الملف، اكتب في وحدة التحكم
                // If file writing fails, write to console
                Console.WriteLine($"فشل في كتابة السجل: {ex.Message}");
                Console.WriteLine($"السجل الأصلي: [{level}] {message}");
            }
        }

        /// <summary>
        /// حذف السجلات القديمة (أكثر من 30 يوم)
        /// Delete old logs (older than 30 days)
        /// </summary>
        public static void CleanupOldLogs()
        {
            try
            {
                if (!Directory.Exists(LogDirectory))
                    return;

                var files = Directory.GetFiles(LogDirectory, "Log_*.txt");
                var cutoffDate = DateTime.Now.AddDays(-30);

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        LogInfo($"تم حذف ملف السجل القديم: {fileInfo.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("فشل في تنظيف السجلات القديمة", ex);
            }
        }
    }
}
