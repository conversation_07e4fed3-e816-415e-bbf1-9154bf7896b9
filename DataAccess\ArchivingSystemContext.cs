using System;
using System.Linq;
using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using Ali_Mola.DataAccess.Models;

namespace Ali_Mola.DataAccess
{
    /// <summary>
    /// سياق قاعدة البيانات لنظام الأرشفة الإلكترونية
    /// Database context for Electronic Archiving System
    /// </summary>
    public class ArchivingSystemContext : DbContext
    {
        /// <summary>
        /// منشئ السياق
        /// Context constructor
        /// </summary>
        public ArchivingSystemContext() : base("ArchivingSystemContext")
        {
            // تمكين إنشاء قاعدة البيانات تلقائياً إذا لم تكن موجودة
            // Enable automatic database creation if it doesn't exist
            Database.SetInitializer(new CreateDatabaseIfNotExists<ArchivingSystemContext>());
        }

        /// <summary>
        /// جدول الأقسام
        /// Departments table
        /// </summary>
        public DbSet<Department> Departments { get; set; }

        /// <summary>
        /// جدول الاضبارات
        /// FileBoxes table
        /// </summary>
        public DbSet<FileBox> FileBoxes { get; set; }

        /// <summary>
        /// جدول الوثائق
        /// Documents table
        /// </summary>
        public DbSet<Document> Documents { get; set; }

        /// <summary>
        /// جدول المرفقات
        /// Attachments table
        /// </summary>
        public DbSet<Attachment> Attachments { get; set; }

        /// <summary>
        /// جدول المستخدمين
        /// Users table
        /// </summary>
        public DbSet<User> Users { get; set; }

        /// <summary>
        /// تكوين النموذج
        /// Model configuration
        /// </summary>
        /// <param name="modelBuilder">منشئ النموذج</param>
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            // إزالة اتفاقية تعدد الأسماء
            // Remove pluralizing table name convention
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();

            // تكوين العلاقات
            // Configure relationships

            // علاقة القسم مع الاضبارات (واحد إلى متعدد)
            // Department to FileBoxes relationship (One to Many)
            modelBuilder.Entity<Department>()
                .HasMany(d => d.FileBoxes)
                .WithRequired(f => f.Department)
                .HasForeignKey(f => f.DepartmentId)
                .WillCascadeOnDelete(false);

            // علاقة الاضبارة مع الوثائق (واحد إلى متعدد)
            // FileBox to Documents relationship (One to Many)
            modelBuilder.Entity<FileBox>()
                .HasMany(f => f.Documents)
                .WithRequired(d => d.FileBox)
                .HasForeignKey(d => d.FileBoxId)
                .WillCascadeOnDelete(false);

            // علاقة الوثيقة مع المرفقات (واحد إلى متعدد)
            // Document to Attachments relationship (One to Many)
            modelBuilder.Entity<Document>()
                .HasMany(d => d.Attachments)
                .WithRequired(a => a.Document)
                .HasForeignKey(a => a.DocumentId)
                .WillCascadeOnDelete(true);

            // تكوين فهارس فريدة
            // Configure unique indexes
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<Document>()
                .HasIndex(d => d.SerialNumber)
                .IsUnique();

            base.OnModelCreating(modelBuilder);
        }

        /// <summary>
        /// إنشاء بيانات أولية
        /// Seed initial data
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public static void SeedData(ArchivingSystemContext context)
        {
            // إنشاء مستخدم إداري افتراضي
            // Create default admin user
            if (!context.Users.Any(u => u.Username == "admin"))
            {
                var adminUser = new User
                {
                    Username = "admin",
                    Password = HashPassword("admin123"), // يجب تشفير كلمة المرور
                    FullName = "مدير النظام",
                    Role = "Admin",
                    IsActive = true
                };
                context.Users.Add(adminUser);
            }

            // إنشاء قسم افتراضي
            // Create default department
            if (!context.Departments.Any())
            {
                var defaultDepartment = new Department
                {
                    Name = "القسم العام",
                    CreatedDate = DateTime.Now
                };
                context.Departments.Add(defaultDepartment);
            }

            context.SaveChanges();
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// Hash password
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        private static string HashPassword(string password)
        {
            // استخدام SHA256 لتشفير كلمة المرور
            // Use SHA256 to hash the password
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
