using System;
using System.Linq;
using <PERSON><PERSON><PERSON><PERSON>.DataAccess;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using <PERSON>_Mo<PERSON>.Utilities;

namespace Ali_Mola.BusinessLogic
{
    /// <summary>
    /// خدمة المصادقة - تدير عمليات تسجيل الدخول والخروج
    /// Authentication Service - Manages login and logout operations
    /// </summary>
    public class AuthenticationService
    {
        /// <summary>
        /// المستخدم الحالي المسجل دخوله
        /// Currently logged in user
        /// </summary>
        public static User CurrentUser { get; private set; }

        /// <summary>
        /// تسجيل دخول المستخدم
        /// User login
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>true إذا نجح تسجيل الدخول</returns>
        public static bool Login(string username, string password)
        {
            try
            {
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    Logger.LogWarning("محاولة تسجيل دخول بمعلومات فارغة");
                    return false;
                }

                using (var context = new SimpleDataContext())
                {
                    // البحث عن المستخدم والتحقق من كلمة المرور
                    // Find user and verify password
                    var user = context.ValidateUser(username, password);

                    if (user == null)
                    {
                        Logger.LogWarning($"محاولة تسجيل دخول فاشلة للمستخدم: {username}");
                        return false;
                    }

                    // تحديث تاريخ آخر تسجيل دخول
                    // Update last login date
                    context.UpdateLastLoginDate(user.UserId);

                    // تعيين المستخدم الحالي
                    // Set current user
                    CurrentUser = user;

                    Logger.LogInfo($"تم تسجيل دخول المستخدم: {username}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تسجيل الدخول", ex);
                return false;
            }
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// User logout
        /// </summary>
        public static void Logout()
        {
            try
            {
                if (CurrentUser != null)
                {
                    Logger.LogInfo($"تم تسجيل خروج المستخدم: {CurrentUser.Username}");
                    CurrentUser = null;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تسجيل الخروج", ex);
            }
        }

        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// Check user permission
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان المستخدم لديه الصلاحية</returns>
        public static bool HasPermission(string requiredRole)
        {
            if (CurrentUser == null)
                return false;

            if (CurrentUser.Role == "Admin")
                return true; // المدير لديه جميع الصلاحيات

            return CurrentUser.Role == requiredRole;
        }

        /// <summary>
        /// التحقق من تسجيل دخول المستخدم
        /// Check if user is logged in
        /// </summary>
        /// <returns>true إذا كان المستخدم مسجل دخوله</returns>
        public static bool IsLoggedIn()
        {
            return CurrentUser != null;
        }

        /// <summary>
        /// التحقق من كون المستخدم مدير
        /// Check if user is admin
        /// </summary>
        /// <returns>true إذا كان المستخدم مدير</returns>
        public static bool IsAdmin()
        {
            return CurrentUser != null && CurrentUser.Role == "Admin";
        }

        /// <summary>
        /// تغيير كلمة مرور المستخدم الحالي
        /// Change current user password
        /// </summary>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم تغيير كلمة المرور بنجاح</returns>
        public static bool ChangePassword(string oldPassword, string newPassword)
        {
            try
            {
                if (CurrentUser == null)
                    return false;

                if (string.IsNullOrEmpty(oldPassword) || string.IsNullOrEmpty(newPassword))
                    return false;

                using (var context = new ArchivingSystemContext())
                {
                    var user = context.Users.Find(CurrentUser.UserId);
                    if (user == null)
                        return false;

                    // التحقق من كلمة المرور القديمة
                    // Verify old password
                    if (!PasswordHelper.VerifyPassword(oldPassword, user.Password))
                        return false;

                    // تحديث كلمة المرور
                    // Update password
                    user.Password = PasswordHelper.HashPassword(newPassword);
                    context.SaveChanges();

                    // تحديث المستخدم الحالي
                    // Update current user
                    CurrentUser.Password = user.Password;

                    Logger.LogInfo($"تم تغيير كلمة مرور المستخدم: {CurrentUser.Username}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تغيير كلمة المرور", ex);
                return false;
            }
        }
    }
}
