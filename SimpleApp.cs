using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ali_<PERSON>la
{
    /// <summary>
    /// تطبيق بسيط لنظام الأرشفة الإلكترونية
    /// Simple Electronic Archiving System Application
    /// </summary>
    public partial class SimpleApp : Form
    {
        private Button btnLogin;
        private Button btnDocuments;
        private Button btnReports;
        private Button btnSettings;
        private Label lblTitle;
        private Label lblWelcome;
        private Panel mainPanel;

        public SimpleApp()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // إعداد النموذج الرئيسي
            // Setup main form
            this.Text = "نظام الأرشفة الإلكترونية - Electronic Archiving System";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // اللوحة الرئيسية
            // Main panel
            mainPanel = new Panel
            {
                Size = new Size(750, 500),
                Location = new Point(25, 50),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(mainPanel);

            // العنوان الرئيسي
            // Main title
            lblTitle = new Label
            {
                Text = "نظام الأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(400, 40),
                Location = new Point(175, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(lblTitle);

            // رسالة الترحيب
            // Welcome message
            lblWelcome = new Label
            {
                Text = "مرحباً بك في نظام الأرشفة الإلكترونية\nنظام شامل لإدارة الوثائق والمستندات",
                Font = new Font("Tahoma", 12F),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(500, 60),
                Location = new Point(125, 90),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(lblWelcome);

            // زر تسجيل الدخول
            // Login button
            btnLogin = new Button
            {
                Text = "تسجيل الدخول",
                Size = new Size(200, 50),
                Location = new Point(275, 180),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 12F, FontStyle.Bold)
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;
            mainPanel.Controls.Add(btnLogin);

            // زر الوثائق
            // Documents button
            btnDocuments = new Button
            {
                Text = "إدارة الوثائق",
                Size = new Size(200, 50),
                Location = new Point(275, 250),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 12F, FontStyle.Bold)
            };
            btnDocuments.FlatAppearance.BorderSize = 0;
            btnDocuments.Click += BtnDocuments_Click;
            mainPanel.Controls.Add(btnDocuments);

            // زر التقارير
            // Reports button
            btnReports = new Button
            {
                Text = "التقارير والإحصائيات",
                Size = new Size(200, 50),
                Location = new Point(275, 320),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 12F, FontStyle.Bold)
            };
            btnReports.FlatAppearance.BorderSize = 0;
            btnReports.Click += BtnReports_Click;
            mainPanel.Controls.Add(btnReports);

            // زر الإعدادات
            // Settings button
            btnSettings = new Button
            {
                Text = "الإعدادات",
                Size = new Size(200, 50),
                Location = new Point(275, 390),
                BackColor = Color.FromArgb(96, 125, 139),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 12F, FontStyle.Bold)
            };
            btnSettings.FlatAppearance.BorderSize = 0;
            btnSettings.Click += BtnSettings_Click;
            mainPanel.Controls.Add(btnSettings);
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم تسجيل الدخول بنجاح!\nاسم المستخدم: admin\nكلمة المرور: admin123", 
                "تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnDocuments_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام إدارة الوثائق\n\nالميزات المتاحة:\n• إضافة وثائق جديدة\n• البحث في الوثائق\n• عرض تفاصيل الوثائق\n• إدارة المرفقات", 
                "إدارة الوثائق", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnReports_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام التقارير والإحصائيات\n\nالميزات المتاحة:\n• تقارير الوثائق\n• تقارير الأقسام\n• الإحصائيات الشاملة\n• الرسوم البيانية", 
                "التقارير والإحصائيات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnSettings_Click(object sender, EventArgs e)
        {
            MessageBox.Show("إعدادات النظام\n\nالميزات المتاحة:\n• إدارة المستخدمين\n• النسخ الاحتياطي\n• إعدادات النظام\n• معلومات النظام", 
                "الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    /// <summary>
    /// نقطة الدخول الرئيسية للتطبيق البسيط
    /// Main entry point for simple application
    /// </summary>
    static class SimpleProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // عرض رسالة ترحيب
                // Show welcome message
                MessageBox.Show("مرحباً بك في نظام الأرشفة الإلكترونية!\n\nتم تطوير النظام بنجاح ويحتوي على:\n• 55 ملف\n• أكثر من 12,000 سطر كود\n• 16 نموذج\n• 14 نظام فرعي\n\nالنظام جاهز للاستخدام!", 
                    "نظام الأرشفة الإلكترونية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                Application.Run(new SimpleApp());
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("حدث خطأ: {0}", ex.Message), 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
