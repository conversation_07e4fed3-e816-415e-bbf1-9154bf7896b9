﻿using System;
using System.Collections.Generic;
using System.Linq;
using <PERSON>_<PERSON><PERSON>.DataAccess;
using Ali_Mo<PERSON>.DataAccess.Models;
using Ali_Mola.Utilities;

namespace Ali_Mola.BusinessLogic
{
    /// <summary>
    /// خدمة الأقسام - تدير عمليات الأقسام والاضبارات
    /// Department Service - Manages department and file box operations
    /// </summary>
    public static class DepartmentService
    {
        /// <summary>
        /// إضافة قسم جديد
        /// Add new department
        /// </summary>
        /// <param name="department">القسم</param>
        /// <returns>معرف القسم الجديد أو -1 في حالة الفشل</returns>
        public static int AddDepartment(Department department)
        {
            try
            {
                if (department == null)
                    throw new ArgumentNullException("department");

                // التحقق من عدم وجود قسم بنفس الاسم
                // Check if department with same name doesn't exist
                if (DepartmentExists(department.Name))
                {
                    Logger.LogWarning(string.Format("محاولة إضافة قسم موجود بالفعل: {0}", department.Name));
                    return -1;
                }

                using (var context = new SimpleDataContext())
                {
                    // تعيين تاريخ الإنشاء
                    // Set creation date
                    department.CreatedDate = DateTime.Now;

                    // هنا سيتم إضافة القسم إلى قاعدة البيانات
                    // Here the department will be added to database
                    // في النسخة المبسطة، نعيد معرف وهمي
                    // In simplified version, return mock ID
                    int newId = GetNextDepartmentId();
                    department.DepartmentId = newId;

                    Logger.LogInfo(string.Format("تم إضافة قسم جديد: {0} برقم: {newId}", department.Name));
                    return newId;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة القسم", ex);
                return -1;
            }
        }

        /// <summary>
        /// تحديث قسم
        /// Update department
        /// </summary>
        /// <param name="department">القسم</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateDepartment(Department department)
        {
            try
            {
                if (department == null)
                    throw new ArgumentNullException("department");

                using (var context = new SimpleDataContext())
                {
                    // هنا سيتم تحديث القسم في قاعدة البيانات
                    // Here the department will be updated in database
                    
                    Logger.LogInfo(string.Format("تم تحديث القسم: {0}", department.Name));
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحديث القسم", ex);
                return false;
            }
        }

        /// <summary>
        /// حذف قسم
        /// Delete department
        /// </summary>
        /// <param name="departmentId">معرف القسم</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteDepartment(int departmentId)
        {
            try
            {
                using (var context = new SimpleDataContext())
                {
                    // التحقق من وجود اضبارات أو وثائق في القسم
                    // Check if department has file boxes or documents
                    if (HasFileBoxes(departmentId))
                    {
                        Logger.LogWarning(string.Format("محاولة حذف قسم يحتوي على اضبارات: {0}", departmentId));
                        return false;
                    }

                    // هنا سيتم حذف القسم من قاعدة البيانات
                    // Here the department will be deleted from database
                    
                    Logger.LogInfo(string.Format("تم حذف القسم: {0}", departmentId));
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حذف القسم", ex);
                return false;
            }
        }

        /// <summary>
        /// الحصول على قسم بالمعرف
        /// Get department by ID
        /// </summary>
        /// <param name="departmentId">معرف القسم</param>
        /// <returns>القسم أو null</returns>
        public static Department GetDepartment(int departmentId)
        {
            try
            {
                using (var context = new SimpleDataContext())
                {
                    // هنا سيتم البحث عن القسم في قاعدة البيانات
                    // Here the department will be searched in database
                    
                    // إرجاع بيانات وهمية للعرض
                    // Return mock data for display
                    return new Department
                    {
                        DepartmentId = departmentId,
                        Name = string.Format("قسم رقم {0}", departmentId),
                        CreatedDate = DateTime.Now.AddMonths(-departmentId)
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على القسم", ex);
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع الأقسام
        /// Get all departments
        /// </summary>
        /// <returns>قائمة الأقسام</returns>
        public static List<Department> GetAllDepartments()
        {
            try
            {
                using (var context = new SimpleDataContext())
                {
                    // هنا سيتم الحصول على جميع الأقسام من قاعدة البيانات
                    // Here all departments will be retrieved from database
                    
                    // إرجاع بيانات وهمية للعرض
                    // Return mock data for display
                    return new List<Department>
                    {
                        new Department { DepartmentId = 1, Name = "القسم العام", CreatedDate = DateTime.Now.AddMonths(-6) },
                        new Department { DepartmentId = 2, Name = "قسم الشؤون الإدارية", CreatedDate = DateTime.Now.AddMonths(-4) },
                        new Department { DepartmentId = 3, Name = "قسم المالية", CreatedDate = DateTime.Now.AddMonths(-3) }
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على الأقسام", ex);
                return new List<Department>();
            }
        }

        /// <summary>
        /// البحث في الأقسام
        /// Search departments
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الأقسام المطابقة</returns>
        public static List<Department> SearchDepartments(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return GetAllDepartments();

                var allDepartments = GetAllDepartments();
                return allDepartments.Where(d => d.Name.Contains(searchTerm.Trim())).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في البحث في الأقسام", ex);
                return new List<Department>();
            }
        }

        /// <summary>
        /// إضافة اضبارة جديدة
        /// Add new file box
        /// </summary>
        /// <param name="fileBox">الاضبارة</param>
        /// <returns>معرف الاضبارة الجديدة أو -1 في حالة الفشل</returns>
        public static int AddFileBox(FileBox fileBox)
        {
            try
            {
                if (fileBox == null)
                    throw new ArgumentNullException("fileBox");

                using (var context = new SimpleDataContext())
                {
                    // التحقق من وجود القسم
                    // Check if department exists
                    var department = GetDepartment(fileBox.DepartmentId);
                    if (department == null)
                    {
                        Logger.LogWarning(string.Format("محاولة إضافة اضبارة لقسم غير موجود: {0}", fileBox.DepartmentId));
                        return -1;
                    }

                    // تعيين تاريخ الإنشاء
                    // Set creation date
                    fileBox.CreatedDate = DateTime.Now;

                    // هنا سيتم إضافة الاضبارة إلى قاعدة البيانات
                    // Here the file box will be added to database
                    int newId = GetNextFileBoxId();
                    fileBox.FileBoxId = newId;

                    Logger.LogInfo(string.Format("تم إضافة اضبارة جديدة: {0} برقم: {newId}", fileBox.Name));
                    return newId;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة الاضبارة", ex);
                return -1;
            }
        }

        /// <summary>
        /// الحصول على اضبارات قسم
        /// Get department file boxes
        /// </summary>
        /// <param name="departmentId">معرف القسم</param>
        /// <returns>قائمة الاضبارات</returns>
        public static List<FileBox> GetDepartmentFileBoxes(int departmentId)
        {
            try
            {
                using (var context = new SimpleDataContext())
                {
                    // هنا سيتم الحصول على اضبارات القسم من قاعدة البيانات
                    // Here department file boxes will be retrieved from database
                    
                    // إرجاع بيانات وهمية للعرض
                    // Return mock data for display
                    return new List<FileBox>
                    {
                        new FileBox { FileBoxId = 1, DepartmentId = departmentId, Name = "اضبارة عامة", CreatedDate = DateTime.Now.AddMonths(-2) },
                        new FileBox { FileBoxId = 2, DepartmentId = departmentId, Name = "اضبارة المراسلات", CreatedDate = DateTime.Now.AddMonths(-1) }
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على اضبارات القسم", ex);
                return new List<FileBox>();
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الأقسام
        /// Get departments statistics
        /// </summary>
        /// <returns>إحصائيات الأقسام</returns>
        public static DepartmentStatistics GetDepartmentStatistics()
        {
            try
            {
                using (var context = new SimpleDataContext())
                {
                    var stats = context.GetStatistics();
                    
                    return new DepartmentStatistics
                    {
                        TotalDepartments = stats.DepartmentCount,
                        TotalFileBoxes = stats.DepartmentCount * 3, // متوسط 3 اضبارات لكل قسم
                        TotalDocuments = stats.TotalDocuments,
                        AverageFileBoxesPerDepartment = 3
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على إحصائيات الأقسام", ex);
                return new DepartmentStatistics();
            }
        }

        #region Helper Methods

        /// <summary>
        /// التحقق من وجود قسم بنفس الاسم
        /// Check if department with same name exists
        /// </summary>
        private static bool DepartmentExists(string name)
        {
            try
            {
                var departments = GetAllDepartments();
                return departments.Any(d => d.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود اضبارات في القسم
        /// Check if department has file boxes
        /// </summary>
        private static bool HasFileBoxes(int departmentId)
        {
            try
            {
                var fileBoxes = GetDepartmentFileBoxes(departmentId);
                return fileBoxes.Count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على معرف القسم التالي
        /// Get next department ID
        /// </summary>
        private static int GetNextDepartmentId()
        {
            try
            {
                var departments = GetAllDepartments();
                return departments.Count > 0 ? departments.Max(d => d.DepartmentId) + 1 : 1;
            }
            catch
            {
                return 1;
            }
        }

        /// <summary>
        /// الحصول على معرف الاضبارة التالي
        /// Get next file box ID
        /// </summary>
        private static int GetNextFileBoxId()
        {
            // في التطبيق الحقيقي، سيتم الحصول على هذا من قاعدة البيانات
            // In real application, this will be retrieved from database
            return new Random().Next(1000, 9999);
        }

        #endregion
    }

    /// <summary>
    /// إحصائيات الأقسام
    /// Department statistics
    /// </summary>
    public class DepartmentStatistics
    {
        public int TotalDepartments { get; set; }
        public int TotalFileBoxes { get; set; }
        public int TotalDocuments { get; set; }
        public double AverageFileBoxesPerDepartment { get; set; }
    }
}


