using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ali_Mola.DataAccess.Models
{
    /// <summary>
    /// نموذج القسم - يمثل الأقسام في النظام
    /// Department Model - Represents departments in the system
    /// </summary>
    [Table("Departments")]
    public class Department
    {
        /// <summary>
        /// معرف القسم الفريد
        /// Unique department identifier
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int DepartmentId { get; set; }

        /// <summary>
        /// اسم القسم
        /// Department name
        /// </summary>
        [Required(ErrorMessage = "اسم القسم مطلوب")]
        [StringLength(100, ErrorMessage = "اسم القسم يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم القسم")]
        public string Name { get; set; }

        /// <summary>
        /// تاريخ إنشاء القسم
        /// Department creation date
        /// </summary>
        [Required]
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// مجموعة الاضبارات التابعة لهذا القسم
        /// Collection of file boxes belonging to this department
        /// </summary>
        public virtual ICollection<FileBox> FileBoxes { get; set; }

        /// <summary>
        /// منشئ القسم
        /// Department constructor
        /// </summary>
        public Department()
        {
            FileBoxes = new HashSet<FileBox>();
            CreatedDate = DateTime.Now;
        }
    }
}
