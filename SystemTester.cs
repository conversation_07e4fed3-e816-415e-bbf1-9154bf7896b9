using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading;
using Ali_Mola.Utilities;

namespace <PERSON>_Mo<PERSON>
{
    /// <summary>
    /// نظام اختبار شامل للنظام المحسن
    /// Comprehensive System Tester
    /// </summary>
    public static class SystemTester
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء اختبار النظام المحسن الشامل");
                
                // Test 1: Welcome Form
                TestWelcomeForm();
                
                // Test 2: Login Form
                TestLoginForm();
                
                // Test 3: Create Account Form
                TestCreateAccountForm();
                
                // Test 4: Forgot Password Form
                TestForgotPasswordForm();
                
                // Test 5: Change Password Form
                TestChangePasswordForm();
                
                // Test 6: Main Form
                TestMainForm();
                
                Logger.LogInfo("انتهاء اختبار النظام المحسن بنجاح");
                
                MessageBox.Show("✅ تم اختبار جميع النماذج بنجاح!\n\nجميع النوافذ تعمل بشكل صحيح ومتوافق مع المتطلبات.",
                    "نجح الاختبار الشامل", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في اختبار النظام", ex);
                MessageBox.Show(string.Format("حدث خطأ في الاختبار:\n{0}", ex.Message),
                    "خطأ في الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private static void TestWelcomeForm()
        {
            Logger.LogInfo("اختبار نموذج الترحيب");
            
            using (var welcomeForm = new EnhancedWelcomeForm())
            {
                // Test form properties
                if (welcomeForm.Size.Width != 600 || welcomeForm.Size.Height != 450)
                {
                    throw new Exception("خطأ في حجم نموذج الترحيب");
                }
                
                if (welcomeForm.FormBorderStyle != FormBorderStyle.None)
                {
                    throw new Exception("خطأ في نمط حدود نموذج الترحيب");
                }
                
                if (welcomeForm.StartPosition != FormStartPosition.CenterScreen)
                {
                    throw new Exception("خطأ في موضع نموذج الترحيب");
                }
                
                Logger.LogInfo("✅ نموذج الترحيب: اجتاز جميع الاختبارات");
            }
        }
        
        private static void TestLoginForm()
        {
            Logger.LogInfo("اختبار نموذج تسجيل الدخول");
            
            using (var loginForm = new EnhancedLoginForm())
            {
                // Test form properties
                if (loginForm.Size.Width != 500 || loginForm.Size.Height != 400)
                {
                    throw new Exception("خطأ في حجم نموذج تسجيل الدخول");
                }
                
                if (loginForm.RightToLeft != RightToLeft.Yes)
                {
                    throw new Exception("خطأ في دعم RTL لنموذج تسجيل الدخول");
                }
                
                if (loginForm.FormBorderStyle != FormBorderStyle.FixedDialog)
                {
                    throw new Exception("خطأ في نمط حدود نموذج تسجيل الدخول");
                }
                
                Logger.LogInfo("✅ نموذج تسجيل الدخول: اجتاز جميع الاختبارات");
            }
        }
        
        private static void TestCreateAccountForm()
        {
            Logger.LogInfo("اختبار نموذج إنشاء حساب جديد");
            
            using (var createForm = new CreateAccountForm())
            {
                // Test form properties
                if (createForm.Size.Width != 500 || createForm.Size.Height != 600)
                {
                    throw new Exception("خطأ في حجم نموذج إنشاء الحساب");
                }
                
                if (createForm.RightToLeft != RightToLeft.Yes)
                {
                    throw new Exception("خطأ في دعم RTL لنموذج إنشاء الحساب");
                }
                
                if (createForm.ShowInTaskbar != false)
                {
                    throw new Exception("خطأ في إعدادات شريط المهام لنموذج إنشاء الحساب");
                }
                
                Logger.LogInfo("✅ نموذج إنشاء الحساب: اجتاز جميع الاختبارات");
            }
        }
        
        private static void TestForgotPasswordForm()
        {
            Logger.LogInfo("اختبار نموذج استعادة كلمة المرور");
            
            using (var forgotForm = new ForgotPasswordForm())
            {
                // Test form properties
                if (forgotForm.Size.Width != 500 || forgotForm.Size.Height != 450)
                {
                    throw new Exception("خطأ في حجم نموذج استعادة كلمة المرور");
                }
                
                if (forgotForm.RightToLeft != RightToLeft.Yes)
                {
                    throw new Exception("خطأ في دعم RTL لنموذج استعادة كلمة المرور");
                }
                
                if (forgotForm.FormBorderStyle != FormBorderStyle.FixedDialog)
                {
                    throw new Exception("خطأ في نمط حدود نموذج استعادة كلمة المرور");
                }
                
                Logger.LogInfo("✅ نموذج استعادة كلمة المرور: اجتاز جميع الاختبارات");
            }
        }
        
        private static void TestChangePasswordForm()
        {
            Logger.LogInfo("اختبار نموذج تغيير كلمة المرور");
            
            using (var changeForm = new ChangePasswordForm())
            {
                // Test form properties
                if (changeForm.Size.Width != 450 || changeForm.Size.Height != 350)
                {
                    throw new Exception("خطأ في حجم نموذج تغيير كلمة المرور");
                }
                
                if (changeForm.RightToLeft != RightToLeft.Yes)
                {
                    throw new Exception("خطأ في دعم RTL لنموذج تغيير كلمة المرور");
                }
                
                if (changeForm.ShowInTaskbar != false)
                {
                    throw new Exception("خطأ في إعدادات شريط المهام لنموذج تغيير كلمة المرور");
                }
                
                Logger.LogInfo("✅ نموذج تغيير كلمة المرور: اجتاز جميع الاختبارات");
            }
        }
        
        private static void TestMainForm()
        {
            Logger.LogInfo("اختبار النموذج الرئيسي");

            using (var mainForm = new EnhancedMainForm())
            {
                // Test form properties before maximization
                mainForm.WindowState = FormWindowState.Normal;

                if (mainForm.RightToLeft != RightToLeft.Yes)
                {
                    throw new Exception("خطأ في دعم RTL للنموذج الرئيسي");
                }

                if (mainForm.StartPosition != FormStartPosition.CenterScreen)
                {
                    throw new Exception("خطأ في موضع النموذج الرئيسي");
                }

                // Test that form can be maximized
                mainForm.WindowState = FormWindowState.Maximized;

                if (mainForm.WindowState != FormWindowState.Maximized)
                {
                    throw new Exception("خطأ في تكبير النموذج الرئيسي");
                }

                Logger.LogInfo("✅ النموذج الرئيسي: اجتاز جميع الاختبارات");
            }
        }
    }
}
