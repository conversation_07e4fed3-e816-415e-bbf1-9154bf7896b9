using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ali_Mola.DataAccess.Models
{
    /// <summary>
    /// نموذج المرفق - يمثل المرفقات في النظام
    /// Attachment Model - Represents attachments in the system
    /// </summary>
    [Table("Attachments")]
    public class Attachment
    {
        /// <summary>
        /// معرف المرفق الفريد
        /// Unique attachment identifier
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int AttachmentId { get; set; }

        /// <summary>
        /// معرف الوثيقة التابع لها المرفق
        /// Document ID that owns this attachment
        /// </summary>
        [Required]
        [Display(Name = "الوثيقة")]
        public int DocumentId { get; set; }

        /// <summary>
        /// مسار الملف
        /// File path
        /// </summary>
        [Required(ErrorMessage = "مسار الملف مطلوب")]
        [StringLength(500, ErrorMessage = "مسار الملف يجب أن يكون أقل من 500 حرف")]
        [Display(Name = "مسار الملف")]
        public string FilePath { get; set; }

        /// <summary>
        /// اسم الملف الأصلي
        /// Original file name
        /// </summary>
        [StringLength(255)]
        [Display(Name = "اسم الملف")]
        public string FileName { get; set; }

        /// <summary>
        /// نوع الملف
        /// File type
        /// </summary>
        [Required]
        [StringLength(10)]
        [Display(Name = "نوع الملف")]
        public string FileType { get; set; } // PDF, PNG, JPG, DOCX, etc.

        /// <summary>
        /// حجم الملف بالبايت
        /// File size in bytes
        /// </summary>
        [Display(Name = "حجم الملف")]
        public long FileSize { get; set; }

        /// <summary>
        /// الوثيقة التابع لها المرفق
        /// Document that owns this attachment
        /// </summary>
        [ForeignKey("DocumentId")]
        public virtual Document Document { get; set; }
    }
}
