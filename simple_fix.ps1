# Simple PowerShell Script to Fix String Interpolation
Write-Host "إصلاح مشاكل String Interpolation..." -ForegroundColor Green

$files = Get-ChildItem -Path "." -Filter "*.cs" -Recurse
$totalReplacements = 0

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # Simple pattern replacement
    $content = $content -replace '\$"([^"]*?)\{([^}]+?)\}([^"]*?)"', 'string.Format("$1{0}$3", $2)'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "تم إصلاح الملف: $($file.Name)" -ForegroundColor Yellow
        $totalReplacements++
    }
}

Write-Host "تم إصلاح $totalReplacements ملف" -ForegroundColor Green
