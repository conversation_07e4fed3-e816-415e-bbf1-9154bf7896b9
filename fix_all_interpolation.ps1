# PowerShell script to fix all String Interpolation issues
# Convert $"text {variable}" to string.Format("text {0}", variable)

Write-Host "Starting comprehensive String Interpolation fix..." -ForegroundColor Green

# Get all C# files
$files = Get-ChildItem -Path . -Recurse -Filter "*.cs" | Where-Object { $_.Name -ne "SimpleApp.cs" }

$totalFiles = $files.Count
$processedFiles = 0
$totalReplacements = 0

foreach ($file in $files) {
    $processedFiles++
    Write-Host "Processing file $processedFiles/$totalFiles : $($file.Name)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # Pattern 1: Simple interpolation $"text {variable}"
    $pattern1 = '\$"([^"]*?)\{([^}]+)\}([^"]*?)"'
    $replacement1 = 'string.Format("$1{0}$3", $2)'
    $content = $content -replace $pattern1, $replacement1
    
    # Pattern 2: Multiple variables $"text {var1} more {var2}"
    # This is more complex, let's handle common cases
    $pattern2 = '\$"([^"]*?)\{([^}]+)\}([^"]*?)\{([^}]+)\}([^"]*?)"'
    $replacement2 = 'string.Format("$1{0}$3{1}$5", $2, $4)'
    $content = $content -replace $pattern2, $replacement2
    
    # Pattern 3: Three variables
    $pattern3 = '\$"([^"]*?)\{([^}]+)\}([^"]*?)\{([^}]+)\}([^"]*?)\{([^}]+)\}([^"]*?)"'
    $replacement3 = 'string.Format("$1{0}$3{1}$5{2}$7", $2, $4, $6)'
    $content = $content -replace $pattern3, $replacement3
    
    # Count replacements made
    if ($content -ne $originalContent) {
        $replacements = ($originalContent.Length - $content.Length) / 2  # Rough estimate
        $totalReplacements += $replacements
        
        # Write the fixed content back
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Fixed interpolations in $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  No interpolations found in $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "`nString Interpolation fix completed!" -ForegroundColor Green
Write-Host "Files processed: $processedFiles" -ForegroundColor Cyan
Write-Host "Total replacements: $totalReplacements" -ForegroundColor Cyan
Write-Host "`nReady to build the complete project!" -ForegroundColor Yellow
