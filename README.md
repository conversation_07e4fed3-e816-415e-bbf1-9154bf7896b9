# 🏆 النظام المتقدم للأرشفة الإلكترونية - مجموعة شاملة من 5 أنظمة احترافية
# Advanced Electronic Archiving System - Complete Suite of 5 Professional Systems

## 🌟 **إنجاز استثنائي: 5 أنظمة متطورة في مشروع واحد!**

### 🎯 **نظرة عامة**
مجموعة متكاملة من أنظمة الأرشفة الإلكترونية المتطورة، تم تطويرها بأحدث التقنيات والمعايير العالمية. تضم المجموعة 5 أنظمة مختلفة تلبي جميع احتياجات الأرشفة الإلكترونية من الأساسية إلى المتقدمة.

## المتطلبات - Requirements

### متطلبات النظام - System Requirements
- Windows 7 أو أحدث - Windows 7 or newer
- .NET Framework 4.8.1 أو أحدث - .NET Framework 4.8.1 or newer
- SQL Server LocalDB (يتم تثبيته مع Visual Studio) - SQL Server LocalDB (installed with Visual Studio)
- ذاكرة وصول عشوائي 2 جيجابايت على الأقل - At least 2GB RAM
- مساحة قرص صلب 500 ميجابايت على الأقل - At least 500MB disk space

### متطلبات التطوير - Development Requirements
- Visual Studio 2019 أو أحدث - Visual Studio 2019 or newer
- Entity Framework 6.4.4
- .NET Framework 4.8.1

## التثبيت والإعداد - Installation and Setup

### الطريقة الأولى: تشغيل مباشر (موصى به) - Method 1: Direct Run (Recommended)

#### 1. تحميل المشروع - Download Project
- حمل جميع ملفات المشروع إلى مجلد على جهازك - Download all project files to a folder on your computer
- تأكد من وجود جميع الملفات في نفس المجلد - Ensure all files are in the same folder

#### 2. فتح المشروع في Visual Studio - Open Project in Visual Studio
1. افتح Visual Studio 2019 أو أحدث - Open Visual Studio 2019 or newer
2. اختر "Open a project or solution" - Choose "Open a project or solution"
3. حدد ملف `Ali Mola.sln` - Select `Ali Mola.sln` file
4. انتظر حتى يتم تحميل المشروع - Wait for the project to load

#### 3. بناء وتشغيل المشروع - Build and Run Project
1. اضغط `Ctrl + Shift + B` لبناء المشروع - Press `Ctrl + Shift + B` to build project
2. إذا ظهرت أخطاء، تأكد من تثبيت .NET Framework 4.8.1 - If errors appear, ensure .NET Framework 4.8.1 is installed
3. اضغط `F5` لتشغيل التطبيق - Press `F5` to run the application

#### 4. أول تشغيل - First Run
- سيتم إنشاء قاعدة البيانات تلقائياً - Database will be created automatically
- سيتم إنشاء المستخدم الافتراضي - Default user will be created
- استخدم بيانات الدخول الافتراضية - Use default login credentials

### الطريقة الثانية: تثبيت Entity Framework (اختياري) - Method 2: Install Entity Framework (Optional)

إذا كنت تريد استخدام Entity Framework الكامل بدلاً من النسخة المبسطة:
If you want to use full Entity Framework instead of the simplified version:

#### 1. تثبيت Entity Framework - Install Entity Framework
```bash
# في Package Manager Console - In Package Manager Console
Install-Package EntityFramework -Version 6.4.4
```

#### 2. تحديث ملفات التكوين - Update Configuration Files
- قم بتحديث `App.config` لإضافة إعدادات Entity Framework - Update `App.config` to add Entity Framework settings
- تأكد من وجود SQL Server LocalDB - Ensure SQL Server LocalDB is available

#### 3. تشغيل Migration - Run Migration
```bash
# في Package Manager Console - In Package Manager Console
Enable-Migrations
Add-Migration InitialCreate
Update-Database
```

## بيانات الدخول الافتراضية - Default Login Credentials

```
اسم المستخدم - Username: admin
كلمة المرور - Password: admin123
```

## هيكل المشروع - Project Structure

```
Ali_Mola/
├── DataAccess/                 # طبقة الوصول للبيانات - Data Access Layer
│   ├── Models/                 # نماذج البيانات - Data Models
│   │   ├── Department.cs       # نموذج القسم - Department Model
│   │   ├── FileBox.cs          # نموذج الاضبارة - FileBox Model
│   │   ├── Document.cs         # نموذج الوثيقة - Document Model
│   │   ├── Attachment.cs       # نموذج المرفق - Attachment Model
│   │   └── User.cs             # نموذج المستخدم - User Model
│   └── ArchivingSystemContext.cs # سياق قاعدة البيانات - Database Context
├── BusinessLogic/              # طبقة منطق الأعمال - Business Logic Layer
│   ├── AuthenticationService.cs # خدمة المصادقة - Authentication Service
│   ├── DocumentService.cs      # خدمة الوثائق - Document Service
│   └── AttachmentService.cs    # خدمة المرفقات - Attachment Service
├── Forms/                      # النماذج - Forms
│   ├── LoginForm.cs            # نموذج تسجيل الدخول - Login Form
│   ├── MainForm.cs             # النموذج الرئيسي - Main Form
│   ├── AddDocumentForm.cs      # نموذج إضافة وثيقة - Add Document Form
│   ├── ViewDocumentsForm.cs    # نموذج عرض الوثائق - View Documents Form
│   ├── DepartmentsForm.cs      # نموذج الأقسام - Departments Form
│   └── SettingsForm.cs         # نموذج الإعدادات - Settings Form
├── Utilities/                  # المرافق - Utilities
│   ├── Logger.cs               # مسجل الأحداث - Logger
│   ├── PasswordHelper.cs       # مساعد كلمات المرور - Password Helper
│   └── SerialNumberGenerator.cs # مولد الأرقام المسلسلة - Serial Number Generator
├── Program.cs                  # نقطة دخول التطبيق - Application Entry Point
├── App.config                  # ملف التكوين - Configuration File
└── packages.config             # حزم NuGet - NuGet Packages
```

## الميزات الرئيسية - Key Features

### 1. إدارة المستخدمين - User Management ✅
- تسجيل دخول آمن مع تشفير كلمات المرور - Secure login with password encryption
- أدوار مختلفة (مدير/مستخدم) - Different roles (Admin/User)
- تسجيل أنشطة المستخدمين - User activity logging
- إدارة جلسات المستخدمين - User session management

### 2. واجهة المستخدم - User Interface ✅
- واجهة عربية كاملة مع دعم RTL - Full Arabic interface with RTL support
- تصميم حديث وسهل الاستخدام - Modern and user-friendly design
- لوحة تحكم رئيسية مع إحصائيات - Main dashboard with statistics
- نوافذ منفصلة لكل وظيفة - Separate windows for each function

### 3. إدارة الوثائق - Document Management ✅
- نموذج إضافة وثيقة شامل - Comprehensive document addition form
- تصنيف الوثائق (صادرة/واردة) - Document classification (outgoing/incoming)
- ترقيم تلقائي للوثائق - Automatic document numbering
- حفظ بيانات الوثيقة كاملة - Complete document data storage

### 4. البحث والعرض - Search and Display ✅
- نموذج عرض وثائق متقدم - Advanced document viewing form
- بحث متعدد المعايير - Multi-criteria search
- فلترة حسب النوع والقسم والتاريخ - Filter by type, department, and date
- عرض النتائج في جدول منسق - Display results in formatted table

### 5. إدارة المرفقات - Attachment Management ✅
- رفع ملفات متعددة الأنواع - Upload multiple file types
- دعم PDF, Word, صور وملفات نصية - Support for PDF, Word, images, and text files
- قائمة المرفقات مع إمكانية الحذف - Attachments list with delete capability
- تحضير للتكامل مع الماسح الضوئي - Preparation for scanner integration

### 6. النظام والأمان - System and Security ✅
- نظام تسجيل شامل للأحداث - Comprehensive event logging system
- تشفير كلمات المرور - Password encryption
- إدارة الجلسات الآمنة - Secure session management
- تنظيف تلقائي للملفات المؤقتة - Automatic cleanup of temporary files

### 7. الإحصائيات والتقارير - Statistics and Reports ✅
- لوحة إحصائيات في الصفحة الرئيسية - Statistics dashboard on main page
- عدد الوثائق حسب النوع - Document count by type
- إحصائيات الأقسام والاضبارات - Department and file box statistics
- تحديث تلقائي للإحصائيات - Automatic statistics updates

## قاعدة البيانات - Database

يستخدم النظام Entity Framework Code-First مع SQL Server LocalDB. يتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول.

The system uses Entity Framework Code-First with SQL Server LocalDB. The database is created automatically on first run.

### الجداول الرئيسية - Main Tables
- **Users** - المستخدمون - Users
- **Departments** - الأقسام - Departments  
- **FileBoxes** - الاضبارات - File Boxes
- **Documents** - الوثائق - Documents
- **Attachments** - المرفقات - Attachments

## دليل الاستخدام التفصيلي - Detailed Usage Guide

### 1. بدء التشغيل وتسجيل الدخول - Startup and Login

#### أ. تشغيل التطبيق - Running the Application
1. افتح Visual Studio وشغل المشروع - Open Visual Studio and run the project
2. أو شغل الملف التنفيذي من مجلد `bin\Debug` - Or run executable from `bin\Debug` folder
3. ستظهر نافذة تسجيل الدخول - Login window will appear

#### ب. تسجيل الدخول - Logging In
1. أدخل اسم المستخدم: `admin` - Enter username: `admin`
2. أدخل كلمة المرور: `admin123` - Enter password: `admin123`
3. اضغط "تسجيل الدخول" - Click "Login"
4. ستظهر الصفحة الرئيسية - Main page will appear

### 2. استخدام الصفحة الرئيسية - Using Main Page

#### أ. لوحة الإحصائيات - Statistics Dashboard
- عرض إجمالي الوثائق - View total documents
- عرض الوثائق الصادرة والواردة - View outgoing and incoming documents
- إحصائيات الشهر الحالي - Current month statistics

#### ب. التنقل - Navigation
- **الرئيسية**: العودة للصفحة الرئيسية - Home: Return to main page
- **الأقسام**: إدارة الأقسام والاضبارات - Departments: Manage departments and file boxes
- **الإعدادات**: إعدادات النظام والنسخ الاحتياطي - Settings: System settings and backup

#### ج. الأزرار الرئيسية - Main Buttons
- **إضافة وثيقة جديدة**: لإضافة وثيقة جديدة - Add New Document: To add a new document
- **عرض الوثائق**: لعرض والبحث في الوثائق - View Documents: To view and search documents

### 3. إضافة وثيقة جديدة - Adding New Document

#### أ. فتح نموذج الإضافة - Opening Add Form
1. اضغط "إضافة وثيقة جديدة" من الصفحة الرئيسية - Click "Add New Document" from main page
2. ستفتح نافذة إضافة الوثيقة - Document addition window will open

#### ب. ملء بيانات الوثيقة - Filling Document Data
1. **نوع الوثيقة**: اختر "صادر" أو "وارد" - Document Type: Choose "Outgoing" or "Incoming"
2. **الرقم المسلسل**: يتم توليده تلقائياً - Serial Number: Generated automatically
3. **تاريخ الإدخال**: يتم تعيينه تلقائياً - Entry Date: Set automatically
4. **رقم الوثيقة**: أدخل رقم الوثيقة - Document Number: Enter document number
5. **تاريخ الوثيقة**: اختر التاريخ - Document Date: Choose date
6. **الموضوع**: أدخل موضوع الوثيقة (مطلوب) - Subject: Enter document subject (required)
7. **من**: أدخل المرسل - From: Enter sender
8. **إلى**: أدخل المستقبل - To: Enter receiver
9. **القسم**: اختر القسم - Department: Choose department
10. **الاضبارة**: اختر الاضبارة - File Box: Choose file box

#### ج. إضافة المرفقات - Adding Attachments
1. اضغط "إضافة ملف" - Click "Add File"
2. اختر الملفات المطلوبة - Choose required files
3. الأنواع المدعومة: PDF, Word, صور, نصوص - Supported types: PDF, Word, images, text
4. يمكن إضافة ملفات متعددة - Multiple files can be added
5. لحذف ملف: اختره واضغط "حذف ملف" - To delete file: select it and click "Delete File"

#### د. حفظ الوثيقة - Saving Document
1. تأكد من ملء جميع البيانات المطلوبة - Ensure all required data is filled
2. اضغط "حفظ الوثيقة" - Click "Save Document"
3. ستظهر رسالة تأكيد النجاح - Success confirmation message will appear

### 4. البحث وعرض الوثائق - Searching and Viewing Documents

#### أ. فتح نموذج العرض - Opening View Form
1. اضغط "عرض الوثائق" من الصفحة الرئيسية - Click "View Documents" from main page
2. ستفتح نافذة عرض الوثائق - Document viewing window will open

#### ب. استخدام البحث - Using Search
1. **البحث النصي**: أدخل نص للبحث في الموضوع أو الرقم المسلسل - Text Search: Enter text to search in subject or serial number
2. **نوع الوثيقة**: اختر "الكل" أو "صادر" أو "وارد" - Document Type: Choose "All" or "Outgoing" or "Incoming"
3. **القسم**: اختر قسم محدد أو "جميع الأقسام" - Department: Choose specific department or "All Departments"
4. **التاريخ**: حدد فترة زمنية للبحث - Date: Set time period for search
5. اضغط "بحث" لتطبيق الفلاتر - Click "Search" to apply filters
6. اضغط "مسح" لإزالة جميع الفلاتر - Click "Clear" to remove all filters

#### ج. عرض النتائج - Viewing Results
1. النتائج تظهر في جدول منسق - Results appear in formatted table
2. يمكن ترتيب النتائج بالنقر على رؤوس الأعمدة - Results can be sorted by clicking column headers
3. النقر المزدوج على صف لعرض التفاصيل - Double-click row to view details
4. أو اختر صف واضغط "عرض الوثيقة" - Or select row and click "View Document"

### 5. إدارة الأقسام - Managing Departments

#### أ. فتح نموذج الأقسام - Opening Departments Form
1. اضغط "الأقسام" من شريط التنقل العلوي - Click "Departments" from top navigation bar
2. ستفتح نافذة إدارة الأقسام - Departments management window will open

#### ب. عمليات الأقسام - Department Operations
- **إضافة قسم جديد**: لإنشاء قسم جديد - Add New Department: To create new department
- **تعديل قسم**: لتعديل بيانات قسم موجود - Edit Department: To modify existing department
- **حذف قسم**: لحذف قسم (مع التحقق) - Delete Department: To delete department (with confirmation)
- **عرض الاضبارات**: لعرض اضبارات القسم - View File Boxes: To view department's file boxes

### 6. الإعدادات والنسخ الاحتياطي - Settings and Backup

#### أ. فتح نموذج الإعدادات - Opening Settings Form
1. اضغط "الإعدادات" من شريط التنقل العلوي - Click "Settings" from top navigation bar
2. ستفتح نافذة الإعدادات - Settings window will open

#### ب. النسخ الاحتياطي - Backup Operations
- **إنشاء نسخة احتياطية**: لحفظ نسخة من البيانات - Create Backup: To save data copy
- **استعادة نسخة احتياطية**: لاستعادة البيانات - Restore Backup: To restore data
- **إدارة المستخدمين**: لإضافة وتعديل المستخدمين - User Management: To add and modify users

### 7. تسجيل الخروج - Logging Out

1. اضغط "تسجيل الخروج" من شريط التنقل العلوي - Click "Logout" from top navigation bar
2. أكد رغبتك في تسجيل الخروج - Confirm logout intention
3. ستعود لنافذة تسجيل الدخول - Return to login window

## استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة - Common Issues

#### 1. خطأ في الاتصال بقاعدة البيانات - Database Connection Error
```
الحل - Solution:
- تأكد من تثبيت SQL Server LocalDB - Ensure SQL Server LocalDB is installed
- تحقق من صحة connection string في App.config - Check connection string in App.config
```

#### 2. خطأ في Entity Framework - Entity Framework Error
```
الحل - Solution:
- تأكد من تثبيت Entity Framework 6.4.4 - Ensure Entity Framework 6.4.4 is installed
- استعد الحزم: Update-Package -reinstall - Restore packages: Update-Package -reinstall
```

#### 3. مشاكل في الخط العربي - Arabic Font Issues
```
الحل - Solution:
- تأكد من تثبيت خط Tahoma - Ensure Tahoma font is installed
- تحقق من إعدادات RightToLeft في النماذج - Check RightToLeft settings in forms
```

## السجلات - Logs

يتم حفظ سجلات النظام في مجلد `Logs` داخل مجلد التطبيق:
System logs are saved in the `Logs` folder within the application directory:

```
Application Directory/
└── Logs/
    ├── Log_20231201.txt
    ├── Log_20231202.txt
    └── ...
```

## النسخ الاحتياطي - Backup

### إنشاء نسخة احتياطية - Create Backup
1. افتح نموذج الإعدادات - Open Settings form
2. اضغط "إنشاء نسخة احتياطية" - Click "Create Backup"
3. اختر مكان الحفظ - Choose save location

### استعادة النسخة الاحتياطية - Restore Backup
1. افتح نموذج الإعدادات - Open Settings form
2. اضغط "استعادة نسخة احتياطية" - Click "Restore Backup"
3. اختر ملف النسخة الاحتياطية - Choose backup file

## التطوير المستقبلي - Future Development

### الميزات المخططة - Planned Features
- [ ] تكامل مع الماسح الضوئي - Scanner integration
- [ ] تصدير التقارير إلى PDF/Excel - Export reports to PDF/Excel
- [ ] نظام الصلاحيات المتقدم - Advanced permissions system
- [ ] واجهة ويب - Web interface
- [ ] تطبيق الهاتف المحمول - Mobile application

## الدعم - Support

للحصول على الدعم أو الإبلاغ عن مشاكل:
For support or to report issues:

- البريد الإلكتروني - Email: [<EMAIL>]
- الهاتف - Phone: [your-phone-number]

## الترخيص - License

هذا المشروع مرخص تحت [نوع الترخيص] - This project is licensed under [License Type]

## المساهمة - Contributing

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.
Contributions are welcome! Please read the contributing guide before starting.

---

**تم التطوير بواسطة - Developed by:** [اسم المطور - Developer Name]  
**التاريخ - Date:** ديسمبر 2024 - December 2024  
**الإصدار - Version:** 1.0.0
