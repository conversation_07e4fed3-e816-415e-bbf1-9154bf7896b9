# نظام الأرشفة الإلكترونية - Electronic Archiving System

## نظرة عامة - Overview

نظام الأرشفة الإلكترونية هو تطبيق سطح مكتب مطور بلغة C# باستخدام Windows Forms، مصمم لإدارة وأرشفة الوثائق الإلكترونية بطريقة منظمة وفعالة. يدعم النظام اللغة العربية بالكامل ويوفر واجهة مستخدم سهلة الاستخدام.

The Electronic Archiving System is a desktop application developed in C# using Windows Forms, designed to manage and archive electronic documents in an organized and efficient manner. The system fully supports Arabic language and provides an easy-to-use interface.

## المتطلبات - Requirements

### متطلبات النظام - System Requirements
- Windows 7 أو أحدث - Windows 7 or newer
- .NET Framework 4.8.1 أو أحدث - .NET Framework 4.8.1 or newer
- SQL Server LocalDB (يتم تثبيته مع Visual Studio) - SQL Server LocalDB (installed with Visual Studio)
- ذاكرة وصول عشوائي 2 جيجابايت على الأقل - At least 2GB RAM
- مساحة قرص صلب 500 ميجابايت على الأقل - At least 500MB disk space

### متطلبات التطوير - Development Requirements
- Visual Studio 2019 أو أحدث - Visual Studio 2019 or newer
- Entity Framework 6.4.4
- .NET Framework 4.8.1

## التثبيت والإعداد - Installation and Setup

### 1. تحميل المشروع - Download Project
```bash
git clone [repository-url]
cd Ali_Mola
```

### 2. فتح المشروع في Visual Studio - Open Project in Visual Studio
1. افتح Visual Studio - Open Visual Studio
2. اختر "Open a project or solution" - Choose "Open a project or solution"
3. حدد ملف `Ali Mola.sln` - Select `Ali Mola.sln` file

### 3. استعادة الحزم - Restore Packages
```bash
# في Package Manager Console - In Package Manager Console
Update-Package -reinstall
```

### 4. بناء المشروع - Build Project
1. اضغط `Ctrl + Shift + B` أو - Press `Ctrl + Shift + B` or
2. من القائمة: Build → Build Solution - From menu: Build → Build Solution

### 5. تشغيل التطبيق - Run Application
1. اضغط `F5` أو - Press `F5` or
2. من القائمة: Debug → Start Debugging - From menu: Debug → Start Debugging

## بيانات الدخول الافتراضية - Default Login Credentials

```
اسم المستخدم - Username: admin
كلمة المرور - Password: admin123
```

## هيكل المشروع - Project Structure

```
Ali_Mola/
├── DataAccess/                 # طبقة الوصول للبيانات - Data Access Layer
│   ├── Models/                 # نماذج البيانات - Data Models
│   │   ├── Department.cs       # نموذج القسم - Department Model
│   │   ├── FileBox.cs          # نموذج الاضبارة - FileBox Model
│   │   ├── Document.cs         # نموذج الوثيقة - Document Model
│   │   ├── Attachment.cs       # نموذج المرفق - Attachment Model
│   │   └── User.cs             # نموذج المستخدم - User Model
│   └── ArchivingSystemContext.cs # سياق قاعدة البيانات - Database Context
├── BusinessLogic/              # طبقة منطق الأعمال - Business Logic Layer
│   ├── AuthenticationService.cs # خدمة المصادقة - Authentication Service
│   ├── DocumentService.cs      # خدمة الوثائق - Document Service
│   └── AttachmentService.cs    # خدمة المرفقات - Attachment Service
├── Forms/                      # النماذج - Forms
│   ├── LoginForm.cs            # نموذج تسجيل الدخول - Login Form
│   ├── MainForm.cs             # النموذج الرئيسي - Main Form
│   ├── AddDocumentForm.cs      # نموذج إضافة وثيقة - Add Document Form
│   ├── ViewDocumentsForm.cs    # نموذج عرض الوثائق - View Documents Form
│   ├── DepartmentsForm.cs      # نموذج الأقسام - Departments Form
│   └── SettingsForm.cs         # نموذج الإعدادات - Settings Form
├── Utilities/                  # المرافق - Utilities
│   ├── Logger.cs               # مسجل الأحداث - Logger
│   ├── PasswordHelper.cs       # مساعد كلمات المرور - Password Helper
│   └── SerialNumberGenerator.cs # مولد الأرقام المسلسلة - Serial Number Generator
├── Program.cs                  # نقطة دخول التطبيق - Application Entry Point
├── App.config                  # ملف التكوين - Configuration File
└── packages.config             # حزم NuGet - NuGet Packages
```

## الميزات الرئيسية - Key Features

### 1. إدارة المستخدمين - User Management
- تسجيل دخول آمن - Secure login
- أدوار مختلفة (مدير/مستخدم) - Different roles (Admin/User)
- تشفير كلمات المرور - Password encryption

### 2. إدارة الأقسام والاضبارات - Departments and FileBoxes Management
- إنشاء وتعديل الأقسام - Create and edit departments
- إدارة الاضبارات داخل الأقسام - Manage file boxes within departments
- هيكل تنظيمي هرمي - Hierarchical organizational structure

### 3. إدارة الوثائق - Document Management
- إضافة وثائق جديدة (صادرة/واردة) - Add new documents (outgoing/incoming)
- ترقيم تلقائي للوثائق - Automatic document numbering
- بحث متقدم في الوثائق - Advanced document search
- عرض تفاصيل الوثائق - View document details

### 4. إدارة المرفقات - Attachment Management
- رفع ملفات متعددة الأنواع - Upload multiple file types
- معاينة المرفقات - Preview attachments
- تحميل وطباعة المرفقات - Download and print attachments

### 5. الإحصائيات والتقارير - Statistics and Reports
- إحصائيات شاملة للنظام - Comprehensive system statistics
- عدد الوثائق حسب النوع - Document count by type
- إحصائيات شهرية وسنوية - Monthly and yearly statistics

## قاعدة البيانات - Database

يستخدم النظام Entity Framework Code-First مع SQL Server LocalDB. يتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول.

The system uses Entity Framework Code-First with SQL Server LocalDB. The database is created automatically on first run.

### الجداول الرئيسية - Main Tables
- **Users** - المستخدمون - Users
- **Departments** - الأقسام - Departments  
- **FileBoxes** - الاضبارات - File Boxes
- **Documents** - الوثائق - Documents
- **Attachments** - المرفقات - Attachments

## الاستخدام - Usage

### 1. تسجيل الدخول - Login
1. شغل التطبيق - Run the application
2. أدخل بيانات الدخول الافتراضية - Enter default credentials
3. اضغط "تسجيل الدخول" - Click "Login"

### 2. إضافة وثيقة جديدة - Add New Document
1. من الصفحة الرئيسية، اضغط "إضافة وثيقة جديدة" - From main page, click "Add New Document"
2. املأ بيانات الوثيقة - Fill document details
3. أرفق الملفات المطلوبة - Attach required files
4. احفظ الوثيقة - Save document

### 3. البحث في الوثائق - Search Documents
1. اضغط "عرض الوثائق" - Click "View Documents"
2. استخدم فلاتر البحث - Use search filters
3. اضغط على الوثيقة لعرض التفاصيل - Click document to view details

## استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة - Common Issues

#### 1. خطأ في الاتصال بقاعدة البيانات - Database Connection Error
```
الحل - Solution:
- تأكد من تثبيت SQL Server LocalDB - Ensure SQL Server LocalDB is installed
- تحقق من صحة connection string في App.config - Check connection string in App.config
```

#### 2. خطأ في Entity Framework - Entity Framework Error
```
الحل - Solution:
- تأكد من تثبيت Entity Framework 6.4.4 - Ensure Entity Framework 6.4.4 is installed
- استعد الحزم: Update-Package -reinstall - Restore packages: Update-Package -reinstall
```

#### 3. مشاكل في الخط العربي - Arabic Font Issues
```
الحل - Solution:
- تأكد من تثبيت خط Tahoma - Ensure Tahoma font is installed
- تحقق من إعدادات RightToLeft في النماذج - Check RightToLeft settings in forms
```

## السجلات - Logs

يتم حفظ سجلات النظام في مجلد `Logs` داخل مجلد التطبيق:
System logs are saved in the `Logs` folder within the application directory:

```
Application Directory/
└── Logs/
    ├── Log_20231201.txt
    ├── Log_20231202.txt
    └── ...
```

## النسخ الاحتياطي - Backup

### إنشاء نسخة احتياطية - Create Backup
1. افتح نموذج الإعدادات - Open Settings form
2. اضغط "إنشاء نسخة احتياطية" - Click "Create Backup"
3. اختر مكان الحفظ - Choose save location

### استعادة النسخة الاحتياطية - Restore Backup
1. افتح نموذج الإعدادات - Open Settings form
2. اضغط "استعادة نسخة احتياطية" - Click "Restore Backup"
3. اختر ملف النسخة الاحتياطية - Choose backup file

## التطوير المستقبلي - Future Development

### الميزات المخططة - Planned Features
- [ ] تكامل مع الماسح الضوئي - Scanner integration
- [ ] تصدير التقارير إلى PDF/Excel - Export reports to PDF/Excel
- [ ] نظام الصلاحيات المتقدم - Advanced permissions system
- [ ] واجهة ويب - Web interface
- [ ] تطبيق الهاتف المحمول - Mobile application

## الدعم - Support

للحصول على الدعم أو الإبلاغ عن مشاكل:
For support or to report issues:

- البريد الإلكتروني - Email: [<EMAIL>]
- الهاتف - Phone: [your-phone-number]

## الترخيص - License

هذا المشروع مرخص تحت [نوع الترخيص] - This project is licensed under [License Type]

## المساهمة - Contributing

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.
Contributions are welcome! Please read the contributing guide before starting.

---

**تم التطوير بواسطة - Developed by:** [اسم المطور - Developer Name]  
**التاريخ - Date:** ديسمبر 2024 - December 2024  
**الإصدار - Version:** 1.0.0
