# PowerShell Script to Fix String Interpolation Issues
# This script converts C# 6.0 string interpolation to string.Format() for .NET Framework 4.0 compatibility

Write-Host "بدء إصلاح مشاكل String Interpolation..." -ForegroundColor Green
Write-Host "Starting String Interpolation fixes..." -ForegroundColor Green

# Get all C# files
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

$totalFiles = $csFiles.Count
$processedFiles = 0
$totalReplacements = 0

foreach ($file in $csFiles) {
    $processedFiles++
    Write-Progress -Activity "معالجة الملفات - Processing Files" -Status "ملف $processedFiles من $totalFiles - File $processedFiles of $totalFiles" -PercentComplete (($processedFiles / $totalFiles) * 100)
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileReplacements = 0
    
    # Pattern 1: Simple interpolation like $"text {variable}"
    $pattern1 = '\$"([^"]*?)\{([^}]+?)\}([^"]*?)"'
    while ($content -match $pattern1) {
        $content = $content -replace $pattern1, 'string.Format("$1{0}$3", $2)'
        $fileReplacements++
    }
    
    # Pattern 2: Multiple variables like $"text {var1} more {var2}"
    $pattern2 = '\$"([^"]*?)\{([^}]+?)\}([^"]*?)\{([^}]+?)\}([^"]*?)"'
    while ($content -match $pattern2) {
        $content = $content -replace $pattern2, 'string.Format("$1{0}$3{1}$5", $2, $4)'
        $fileReplacements++
    }
    
    # Pattern 3: Three variables
    $pattern3 = '\$"([^"]*?)\{([^}]+?)\}([^"]*?)\{([^}]+?)\}([^"]*?)\{([^}]+?)\}([^"]*?)"'
    while ($content -match $pattern3) {
        $content = $content -replace $pattern3, 'string.Format("$1{0}$3{1}$5{2}$7", $2, $4, $6)'
        $fileReplacements++
    }
    
    # Pattern 4: Format specifiers like {DateTime.Now:yyyy-MM-dd}
    $pattern4 = '\$"([^"]*?)\{([^}:]+?):([^}]+?)\}([^"]*?)"'
    while ($content -match $pattern4) {
        $content = $content -replace $pattern4, 'string.Format("$1{0:$3}$4", $2)'
        $fileReplacements++
    }
    
    # Save if changes were made
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "تم إصلاح $fileReplacements استبدال في الملف: $($file.Name)" -ForegroundColor Yellow
        Write-Host "Fixed $fileReplacements replacements in file: $($file.Name)" -ForegroundColor Yellow
        $totalReplacements += $fileReplacements
    }
}

Write-Host ""
Write-Host "تم الانتهاء من الإصلاح!" -ForegroundColor Green
Write-Host "Fix completed!" -ForegroundColor Green
Write-Host "إجمالي الملفات المعالجة: $processedFiles" -ForegroundColor Cyan
Write-Host "Total files processed: $processedFiles" -ForegroundColor Cyan
Write-Host "إجمالي الاستبدالات: $totalReplacements" -ForegroundColor Cyan
Write-Host "Total replacements: $totalReplacements" -ForegroundColor Cyan

# Try to build the project
Write-Host ""
Write-Host "محاولة بناء المشروع..." -ForegroundColor Green
Write-Host "Attempting to build project..." -ForegroundColor Green

try {
    $buildResult = & "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe" "Ali Mola.csproj" /p:Configuration=Release /p:Platform=AnyCPU /verbosity:minimal 2>&1

    if ($LASTEXITCODE -eq 0) {
        Write-Host "نجح البناء!" -ForegroundColor Green
        Write-Host "Build successful!" -ForegroundColor Green
    } else {
        Write-Host "فشل البناء. راجع الأخطاء أعلاه." -ForegroundColor Red
        Write-Host "Build failed. Check errors above." -ForegroundColor Red
        Write-Host $buildResult
    }
} catch {
    Write-Host "خطأ في تشغيل MSBuild: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error running MSBuild: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "انتهى السكريبت." -ForegroundColor Green
Write-Host "Script completed." -ForegroundColor Green
