﻿using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;
using Ali_Mo<PERSON>.BusinessLogic;
using Ali_Mo<PERSON>.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج التقارير
    /// Reports form
    /// </summary>
    public partial class ReportsForm : Form
    {
        #region Controls
        private TabControl tabControl;
        private TabPage tabDocumentReports;
        private TabPage tabDepartmentReports;
        private TabPage tabStatisticalReports;
        private ComboBox cmbReportType;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private ComboBox cmbDepartment;
        private Button btnGenerateReport;
        private Button btnExportReport;
        private Button btnPrintReport;
        private DataGridView dgvReportData;
        private RichTextBox rtbReportSummary;
        private Button btnClose;
        #endregion

        /// <summary>
        /// منشئ نموذج التقارير
        /// Reports form constructor
        /// </summary>
        public ReportsForm()
        {
            InitializeComponent();
            SetupForm();
            LoadReportTypes();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "التقارير والإحصائيات";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateTabControl();
            CreateDocumentReportsTab();
            CreateDepartmentReportsTab();
            CreateStatisticalReportsTab();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء التحكم في التبويبات
        /// Create tab control
        /// </summary>
        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Size = new Size(960, 600),
                Location = new Point(20, 20),
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(tabControl);

            // تبويب تقارير الوثائق
            // Document reports tab
            tabDocumentReports = new TabPage
            {
                Text = "تقارير الوثائق",
                BackColor = Color.White
            };
            tabControl.TabPages.Add(tabDocumentReports);

            // تبويب تقارير الأقسام
            // Department reports tab
            tabDepartmentReports = new TabPage
            {
                Text = "تقارير الأقسام",
                BackColor = Color.White
            };
            tabControl.TabPages.Add(tabDepartmentReports);

            // تبويب التقارير الإحصائية
            // Statistical reports tab
            tabStatisticalReports = new TabPage
            {
                Text = "التقارير الإحصائية",
                BackColor = Color.White
            };
            tabControl.TabPages.Add(tabStatisticalReports);
        }

        /// <summary>
        /// إنشاء تبويب تقارير الوثائق
        /// Create document reports tab
        /// </summary>
        private void CreateDocumentReportsTab()
        {
            // عنوان التبويب
            // Tab title
            Label titleLabel = new Label
            {
                Text = "تقارير الوثائق",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(200, 30),
                Location = new Point(20, 20)
            };
            tabDocumentReports.Controls.Add(titleLabel);

            // أيقونة التقارير
            // Reports icon
            Panel iconPanel = new Panel
            {
                Size = new Size(60, 60),
                Location = new Point(850, 15),
                BackColor = Color.FromArgb(33, 150, 243)
            };

            Label iconLabel = new Label
            {
                Text = "📊",
                Font = new Font("Segoe UI Emoji", 20F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            tabDocumentReports.Controls.Add(iconPanel);

            // لوحة الفلاتر
            // Filters panel
            GroupBox filtersGroup = new GroupBox
            {
                Text = "فلاتر التقرير",
                Size = new Size(900, 120),
                Location = new Point(20, 80),
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            tabDocumentReports.Controls.Add(filtersGroup);

            // نوع التقرير
            // Report type
            Label lblReportType = new Label
            {
                Text = "نوع التقرير:",
                Size = new Size(100, 25),
                Location = new Point(20, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            filtersGroup.Controls.Add(lblReportType);

            cmbReportType = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(130, 30),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            filtersGroup.Controls.Add(cmbReportType);

            // من تاريخ
            // From date
            Label lblFromDate = new Label
            {
                Text = "من تاريخ:",
                Size = new Size(80, 25),
                Location = new Point(350, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            filtersGroup.Controls.Add(lblFromDate);

            dtpFromDate = new DateTimePicker
            {
                Size = new Size(150, 25),
                Location = new Point(440, 30),
                Format = DateTimePickerFormat.Short
            };
            filtersGroup.Controls.Add(dtpFromDate);

            // إلى تاريخ
            // To date
            Label lblToDate = new Label
            {
                Text = "إلى تاريخ:",
                Size = new Size(80, 25),
                Location = new Point(610, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            filtersGroup.Controls.Add(lblToDate);

            DateTimePicker dtpToDate = new DateTimePicker
            {
                Size = new Size(150, 25),
                Location = new Point(700, 30),
                Format = DateTimePickerFormat.Short
            };
            filtersGroup.Controls.Add(dtpToDate);

            // القسم
            // Department
            Label lblDepartment = new Label
            {
                Text = "القسم:",
                Size = new Size(80, 25),
                Location = new Point(20, 70),
                TextAlign = ContentAlignment.MiddleRight
            };
            filtersGroup.Controls.Add(lblDepartment);

            cmbDepartment = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(110, 70),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            filtersGroup.Controls.Add(cmbDepartment);

            // زر إنشاء التقرير
            // Generate report button
            btnGenerateReport = new Button
            {
                Text = "إنشاء التقرير",
                Size = new Size(120, 35),
                Location = new Point(350, 65),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnGenerateReport.FlatAppearance.BorderSize = 0;
            btnGenerateReport.Click += BtnGenerateReport_Click;
            filtersGroup.Controls.Add(btnGenerateReport);

            // أزرار التصدير والطباعة
            // Export and print buttons
            btnExportReport = new Button
            {
                Text = "تصدير Excel",
                Size = new Size(100, 35),
                Location = new Point(490, 65),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F)
            };
            btnExportReport.FlatAppearance.BorderSize = 0;
            btnExportReport.Click += BtnExportReport_Click;
            filtersGroup.Controls.Add(btnExportReport);

            btnPrintReport = new Button
            {
                Text = "طباعة",
                Size = new Size(80, 35),
                Location = new Point(610, 65),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F)
            };
            btnPrintReport.FlatAppearance.BorderSize = 0;
            btnPrintReport.Click += BtnPrintReport_Click;
            filtersGroup.Controls.Add(btnPrintReport);

            // جدول بيانات التقرير
            // Report data grid
            dgvReportData = new DataGridView
            {
                Size = new Size(900, 300),
                Location = new Point(20, 220),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            tabDocumentReports.Controls.Add(dgvReportData);

            // ملخص التقرير
            // Report summary
            Label lblSummary = new Label
            {
                Text = "ملخص التقرير:",
                Size = new Size(100, 25),
                Location = new Point(20, 540),
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            tabDocumentReports.Controls.Add(lblSummary);

            rtbReportSummary = new RichTextBox
            {
                Size = new Size(900, 60),
                Location = new Point(20, 565),
                BackColor = Color.FromArgb(248, 248, 248),
                ReadOnly = true,
                Font = new Font("Tahoma", 9F)
            };
            tabDocumentReports.Controls.Add(rtbReportSummary);
        }

        /// <summary>
        /// إنشاء تبويب تقارير الأقسام
        /// Create department reports tab
        /// </summary>
        private void CreateDepartmentReportsTab()
        {
            // عنوان التبويب
            // Tab title
            Label titleLabel = new Label
            {
                Text = "تقارير الأقسام والاضبارات",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };
            tabDepartmentReports.Controls.Add(titleLabel);

            // رسم بياني للأقسام (محاكاة)
            // Department chart (simulation)
            Panel chartPanel = new Panel
            {
                Size = new Size(900, 400),
                Location = new Point(20, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            tabDepartmentReports.Controls.Add(chartPanel);

            Label chartLabel = new Label
            {
                Text = "📈 الرسم البياني لإحصائيات الأقسام\n\n(سيتم تطوير الرسوم البيانية التفاعلية لاحقاً)",
                Font = new Font("Tahoma", 12F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            chartPanel.Controls.Add(chartLabel);

            // إحصائيات سريعة
            // Quick statistics
            CreateQuickStats(tabDepartmentReports, 20, 500);
        }

        /// <summary>
        /// إنشاء تبويب التقارير الإحصائية
        /// Create statistical reports tab
        /// </summary>
        private void CreateStatisticalReportsTab()
        {
            // عنوان التبويب
            // Tab title
            Label titleLabel = new Label
            {
                Text = "التقارير الإحصائية الشاملة",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };
            tabStatisticalReports.Controls.Add(titleLabel);

            // إحصائيات متقدمة
            // Advanced statistics
            CreateAdvancedStatistics(tabStatisticalReports);
        }

        /// <summary>
        /// إنشاء إحصائيات سريعة
        /// Create quick statistics
        /// </summary>
        private void CreateQuickStats(Control parent, int x, int y)
        {
            var stats = DocumentService.GetDocumentStatistics();
            var deptStats = DepartmentService.GetDepartmentStatistics();

            string statsText = string.Format("إجمالي الوثائق: {0}\n", stats.TotalDocuments) +
                              string.Format("إجمالي الأقسام: {0}\n", deptStats.TotalDepartments) +
                              string.Format("إجمالي الاضبارات: {0}\n", deptStats.TotalFileBoxes) +
                              string.Format("متوسط الوثائق لكل قسم: {0}", (stats.TotalDocuments / Math.Max(deptStats.TotalDepartments, 1)):F1);

            RichTextBox rtbStats = new RichTextBox
            {
                Size = new Size(400, 100),
                Location = new Point(x, y),
                Text = statsText,
                BackColor = Color.FromArgb(248, 248, 248),
                ReadOnly = true,
                Font = new Font("Tahoma", 10F)
            };
            parent.Controls.Add(rtbStats);
        }

        /// <summary>
        /// إنشاء إحصائيات متقدمة
        /// Create advanced statistics
        /// </summary>
        private void CreateAdvancedStatistics(Control parent)
        {
            try
            {
                var stats = DocumentService.GetDocumentStatistics();
                var deptStats = DepartmentService.GetDepartmentStatistics();

                // لوحة الإحصائيات
                // Statistics panel
                Panel statsPanel = new Panel
                {
                    Size = new Size(900, 500),
                    Location = new Point(20, 80),
                    BackColor = Color.White,
                    BorderStyle = BorderStyle.FixedSingle
                };
                parent.Controls.Add(statsPanel);

                // إحصائيات مفصلة
                // Detailed statistics
                string detailedStats = "📊 تقرير إحصائي شامل\n" +
                                      "==================================================\n\n" +
                                      "📄 إحصائيات الوثائق:\n" +
                                      string.Format("   • إجمالي الوثائق: {0}\n", stats.TotalDocuments) +
                                      string.Format("   • الوثائق الصادرة: {0}\n", stats.OutgoingDocuments) +
                                      string.Format("   • الوثائق الواردة: {0}\n", stats.IncomingDocuments) +
                                      string.Format("   • وثائق هذا الشهر: {0}\n\n", stats.DocumentsThisMonth) +
                                      "🏢 إحصائيات الأقسام:\n" +
                                      string.Format("   • إجمالي الأقسام: {0}\n", deptStats.TotalDepartments) +
                                      string.Format("   • إجمالي الاضبارات: {0}\n", deptStats.TotalFileBoxes) +
                                      string.Format("   • متوسط الاضبارات لكل قسم: {0}\n\n", deptStats.AverageFileBoxesPerDepartment:F1) +
                                      "📈 مؤشرات الأداء:\n" +
                                      string.Format("   • متوسط الوثائق لكل قسم: {0}\n", (stats.TotalDocuments / Math.Max(deptStats.TotalDepartments, 1)):F1) +
                                      string.Format("   • نسبة الوثائق الصادرة: {0}%\n", (stats.OutgoingDocuments * 100.0 / Math.Max(stats.TotalDocuments, 1)):F1) +
                                      string.Format("   • نسبة الوثائق الواردة: {0}%\n\n", (stats.IncomingDocuments * 100.0 / Math.Max(stats.TotalDocuments, 1)):F1) +
                                      string.Format("📅 تاريخ التقرير: {0}\n", DateTime.Now.ToString("dd/MM/yyyy HH:mm) +
                                      string.Format("👤 المستخدم: {0")}", AuthenticationService.CurrentUser?.Username);

                RichTextBox rtbDetailedStats = new RichTextBox
                {
                    Size = new Size(860, 460),
                    Location = new Point(20, 20),
                    Text = detailedStats,
                    BackColor = Color.FromArgb(248, 248, 248),
                    ReadOnly = true,
                    Font = new Font("Tahoma", 10F)
                };
                statsPanel.Controls.Add(rtbDetailedStats);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء الإحصائيات المتقدمة", ex);
            }
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر الإغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(120, 40),
                Location = new Point(450, 640),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);
        }

        /// <summary>
        /// تحميل أنواع التقارير
        /// Load report types
        /// </summary>
        private void LoadReportTypes()
        {
            try
            {
                cmbReportType.Items.AddRange(new string[]
                {
                    "جميع الوثائق",
                    "الوثائق الصادرة",
                    "الوثائق الواردة",
                    "وثائق حسب القسم",
                    "وثائق حسب التاريخ",
                    "الوثائق المرفقة"
                });
                cmbReportType.SelectedIndex = 0;

                // تحميل الأقسام
                // Load departments
                cmbDepartment.Items.Add("جميع الأقسام");
                var departments = DepartmentService.GetAllDepartments();
                foreach (var dept in departments)
                {
                    cmbDepartment.Items.Add(dept.Name);
                }
                cmbDepartment.SelectedIndex = 0;

                // تعيين التواريخ الافتراضية
                // Set default dates
                dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل أنواع التقارير", ex);
            }
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث إنشاء التقرير
        /// Generate report event handler
        /// </summary>
        private void BtnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                // تعطيل الزر أثناء المعالجة
                // Disable button during processing
                btnGenerateReport.Enabled = false;
                btnGenerateReport.Text = "جاري الإنشاء...";
                this.Cursor = Cursors.WaitCursor;

                // محاكاة إنشاء التقرير
                // Simulate report generation
                GenerateReportData();

                MessageBox.Show("تم إنشاء التقرير بنجاح!", "نجح الإنشاء", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                Logger.LogInfo(string.Format("تم إنشاء تقرير: {0}", cmbReportType.SelectedItem));
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء التقرير", ex);
                MessageBox.Show("حدث خطأ أثناء إنشاء التقرير", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerateReport.Enabled = true;
                btnGenerateReport.Text = "إنشاء التقرير";
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// إنشاء بيانات التقرير
        /// Generate report data
        /// </summary>
        private void GenerateReportData()
        {
            // إعداد أعمدة الجدول
            // Setup table columns
            dgvReportData.Columns.Clear();
            dgvReportData.Columns.Add("SerialNumber", "الرقم المسلسل");
            dgvReportData.Columns.Add("Type", "النوع");
            dgvReportData.Columns.Add("Subject", "الموضوع");
            dgvReportData.Columns.Add("Department", "القسم");
            dgvReportData.Columns.Add("Date", "التاريخ");

            // إضافة بيانات تجريبية
            // Add sample data
            dgvReportData.Rows.Add("وارد-1-2024-0001", "وارد", "طلب معلومات", "القسم العام", "10/12/2024");
            dgvReportData.Rows.Add("صادر-1-2024-0001", "صادر", "رد على الاستفسار", "قسم الشؤون الإدارية", "11/12/2024");
            dgvReportData.Rows.Add("وارد-2-2024-0002", "وارد", "شكوى", "قسم المالية", "12/12/2024");

            // إنشاء ملخص التقرير
            // Create report summary
            string summary = string.Format("تم إنشاء التقرير بتاريخ: {0}\n", DateTime.Now.ToString("dd/MM/yyyy HH:mm) +
                           string.Format("نوع التقرير: {0")}\n", cmbReportType.SelectedItem) +
                           string.Format("الفترة: من {0} إلى {1}\n", dtpFromDate.Value.ToString("dd/MM/yyyy"), DateTime.Now.ToString("dd/MM/yyyy")) +
                           string.Format("عدد السجلات: {0}", dgvReportData.Rows.Count);

            rtbReportSummary.Text = summary;
        }

        /// <summary>
        /// معالج حدث تصدير التقرير
        /// Export report event handler
        /// </summary>
        private void BtnExportReport_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("وظيفة التصدير إلى Excel قيد التطوير...\nسيتم إضافة تكامل مع Microsoft Excel لاحقاً", 
                    "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم طلب تصدير التقرير");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تصدير التقرير", ex);
            }
        }

        /// <summary>
        /// معالج حدث طباعة التقرير
        /// Print report event handler
        /// </summary>
        private void BtnPrintReport_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("وظيفة الطباعة قيد التطوير...\nسيتم إضافة إمكانية طباعة التقارير لاحقاً", 
                    "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم طلب طباعة التقرير");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في طباعة التقرير", ex);
            }
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}


