﻿using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Ali_Mo<PERSON>.DataAccess;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using Ali_Mola.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج إدارة الأقسام
    /// Departments management form
    /// </summary>
    public partial class DepartmentsForm : Form
    {
        #region Controls
        private Panel topPanel;
        private Panel mainPanel;
        private FlowLayoutPanel departmentsPanel;
        private Button btnAddDepartment;
        private Button btnRefresh;
        private Button btnClose;
        private TextBox txtSearch;
        private Label lblTitle;
        #endregion

        public DepartmentsForm()
        {
            InitializeComponent();
            SetupForm();
            LoadDepartments();
        }

        private void SetupForm()
        {
            this.Text = "إدارة الأقسام";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateTopPanel();
            CreateMainPanel();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء اللوحة العلوية
        /// Create top panel
        /// </summary>
        private void CreateTopPanel()
        {
            topPanel = new Panel
            {
                Size = new Size(960, 80),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(topPanel);

            // عنوان الصفحة
            // Page title
            lblTitle = new Label
            {
                Text = "إدارة الأقسام والاضبارات",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(20, 15)
            };
            topPanel.Controls.Add(lblTitle);

            // مربع البحث
            // Search box
            Label searchLabel = new Label
            {
                Text = "البحث:",
                Size = new Size(60, 25),
                Location = new Point(20, 50),
                TextAlign = ContentAlignment.MiddleRight
            };
            topPanel.Controls.Add(searchLabel);

            txtSearch = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(90, 50),
                // PlaceholderText = "ابحث في أسماء الأقسام..." // غير مدعوم
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            topPanel.Controls.Add(txtSearch);

            // زر إضافة قسم جديد
            // Add new department button
            btnAddDepartment = new Button
            {
                Text = "إضافة قسم جديد",
                Size = new Size(130, 35),
                Location = new Point(700, 45),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnAddDepartment.FlatAppearance.BorderSize = 0;
            btnAddDepartment.Click += BtnAddDepartment_Click;
            topPanel.Controls.Add(btnAddDepartment);

            // زر التحديث
            // Refresh button
            btnRefresh = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 35),
                Location = new Point(850, 45),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnRefresh.FlatAppearance.BorderSize = 0;
            btnRefresh.Click += BtnRefresh_Click;
            topPanel.Controls.Add(btnRefresh);
        }

        /// <summary>
        /// إنشاء اللوحة الرئيسية
        /// Create main panel
        /// </summary>
        private void CreateMainPanel()
        {
            mainPanel = new Panel
            {
                Size = new Size(960, 520),
                Location = new Point(20, 110),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };
            this.Controls.Add(mainPanel);

            // لوحة الأقسام
            // Departments panel
            departmentsPanel = new FlowLayoutPanel
            {
                Size = new Size(940, 500),
                Location = new Point(10, 10),
                FlowDirection = FlowDirection.TopDown,
                WrapContents = false,
                AutoScroll = true,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(departmentsPanel);
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر الإغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(120, 40),
                Location = new Point(450, 650),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);
        }

        /// <summary>
        /// تحميل الأقسام
        /// Load departments
        /// </summary>
        private void LoadDepartments()
        {
            try
            {
                departmentsPanel.Controls.Clear();

                // تحميل بيانات تجريبية
                // Load sample data
                var sampleDepartments = new[]
                {
                    new { Id = 1, Name = "القسم العام", FileBoxCount = 5, DocumentCount = 25, CreatedDate = DateTime.Now.AddMonths(-6) },
                    new { Id = 2, Name = "قسم الشؤون الإدارية", FileBoxCount = 8, DocumentCount = 42, CreatedDate = DateTime.Now.AddMonths(-4) },
                    new { Id = 3, Name = "قسم المالية", FileBoxCount = 6, DocumentCount = 38, CreatedDate = DateTime.Now.AddMonths(-3) },
                    new { Id = 4, Name = "قسم الموارد البشرية", FileBoxCount = 4, DocumentCount = 18, CreatedDate = DateTime.Now.AddMonths(-2) },
                    new { Id = 5, Name = "قسم تقنية المعلومات", FileBoxCount = 3, DocumentCount = 15, CreatedDate = DateTime.Now.AddMonths(-1) }
                };

                foreach (var dept in sampleDepartments)
                {
                    var deptCard = CreateDepartmentCard(dept.Id, dept.Name, dept.FileBoxCount, dept.DocumentCount, dept.CreatedDate);
                    departmentsPanel.Controls.Add(deptCard);
                }

                Logger.LogInfo("تم تحميل الأقسام بنجاح");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل الأقسام", ex);
                MessageBox.Show("حدث خطأ أثناء تحميل الأقسام", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء بطاقة قسم
        /// Create department card
        /// </summary>
        private Panel CreateDepartmentCard(int id, string name, int fileBoxCount, int documentCount, DateTime createdDate)
        {
            Panel card = new Panel
            {
                Size = new Size(900, 120),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(10)
            };

            // أيقونة القسم
            // Department icon
            Panel iconPanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(800, 20),
                BackColor = Color.FromArgb(25, 118, 210)
            };

            Label iconLabel = new Label
            {
                Text = "📁",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            card.Controls.Add(iconPanel);

            // اسم القسم
            // Department name
            Label nameLabel = new Label
            {
                Text = name,
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 25),
                Location = new Point(450, 20)
            };
            card.Controls.Add(nameLabel);

            // معلومات القسم
            // Department info
            Label infoLabel = new Label
            {
                Text = string.Format("عدد الاضبارات: {0} | عدد الوثائق: {documentCount}", fileBoxCount),
                Font = new Font("Tahoma", 10F),
                ForeColor = Color.Gray,
                Size = new Size(300, 20),
                Location = new Point(450, 50)
            };
            card.Controls.Add(infoLabel);

            Label dateLabel = new Label
            {
                Text = string.Format("تاريخ الإنشاء: {0}", createdDate:dd/MM/yyyy),
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.Gray,
                Size = new Size(200, 20),
                Location = new Point(450, 75)
            };
            card.Controls.Add(dateLabel);

            // أزرار العمليات
            // Action buttons
            Button btnView = new Button
            {
                Text = "عرض الاضبارات",
                Size = new Size(100, 30),
                Location = new Point(320, 25),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F),
                Tag = id
            };
            btnView.FlatAppearance.BorderSize = 0;
            btnView.Click += BtnViewFileBoxes_Click;
            card.Controls.Add(btnView);

            Button btnEdit = new Button
            {
                Text = "تعديل",
                Size = new Size(70, 30),
                Location = new Point(240, 25),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F),
                Tag = id
            };
            btnEdit.FlatAppearance.BorderSize = 0;
            btnEdit.Click += BtnEditDepartment_Click;
            card.Controls.Add(btnEdit);

            Button btnDelete = new Button
            {
                Text = "حذف",
                Size = new Size(70, 30),
                Location = new Point(160, 25),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F),
                Tag = id
            };
            btnDelete.FlatAppearance.BorderSize = 0;
            btnDelete.Click += BtnDeleteDepartment_Click;
            card.Controls.Add(btnDelete);

            Button btnAddFileBox = new Button
            {
                Text = "إضافة اضبارة",
                Size = new Size(100, 30),
                Location = new Point(320, 65),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F),
                Tag = id
            };
            btnAddFileBox.FlatAppearance.BorderSize = 0;
            btnAddFileBox.Click += BtnAddFileBox_Click;
            card.Controls.Add(btnAddFileBox);

            return card;
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث البحث
        /// Search event handler
        /// </summary>
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                string searchText = txtSearch.Text.Trim().ToLower();

                foreach (Panel card in departmentsPanel.Controls)
                {
                    if (card.Controls.Count > 0)
                    {
                        var nameLabel = card.Controls.OfType<Label>().FirstOrDefault(l => l.Font.Bold);
                        if (nameLabel != null)
                        {
                            bool visible = string.IsNullOrEmpty(searchText) ||
                                         nameLabel.Text.ToLower().Contains(searchText);
                            card.Visible = visible;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في البحث", ex);
            }
        }

        /// <summary>
        /// معالج حدث إضافة قسم جديد
        /// Add new department event handler
        /// </summary>
        private void BtnAddDepartment_Click(object sender, EventArgs e)
        {
            try
            {
                using (var addForm = new AddDepartmentForm())
                {
                    if (addForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadDepartments();
                        Logger.LogInfo("تم إضافة قسم جديد");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة قسم جديد", ex);
                MessageBox.Show("حدث خطأ أثناء إضافة القسم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث التحديث
        /// Refresh event handler
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDepartments();
        }

        /// <summary>
        /// معالج حدث عرض الاضبارات
        /// View file boxes event handler
        /// </summary>
        private void BtnViewFileBoxes_Click(object sender, EventArgs e)
        {
            try
            {
                Button btn = sender as Button;
                int departmentId = (int)btn.Tag;

                MessageBox.Show(string.Format("عرض اضبارات القسم رقم: {0}\n\n(سيتم تطوير نافذة عرض الاضبارات لاحقاً)", departmentId),
                    "عرض الاضبارات", MessageBoxButtons.OK, MessageBoxIcon.Information);

                Logger.LogInfo(string.Format("تم طلب عرض اضبارات القسم: {0}", departmentId));
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في عرض الاضبارات", ex);
            }
        }

        /// <summary>
        /// معالج حدث تعديل قسم
        /// Edit department event handler
        /// </summary>
        private void BtnEditDepartment_Click(object sender, EventArgs e)
        {
            try
            {
                Button btn = sender as Button;
                int departmentId = (int)btn.Tag;

                MessageBox.Show(string.Format("تعديل القسم رقم: {0}\n\n(سيتم تطوير نافذة التعديل لاحقاً)", departmentId),
                    "تعديل القسم", MessageBoxButtons.OK, MessageBoxIcon.Information);

                Logger.LogInfo(string.Format("تم طلب تعديل القسم: {0}", departmentId));
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تعديل القسم", ex);
            }
        }

        /// <summary>
        /// معالج حدث حذف قسم
        /// Delete department event handler
        /// </summary>
        private void BtnDeleteDepartment_Click(object sender, EventArgs e)
        {
            try
            {
                Button btn = sender as Button;
                int departmentId = (int)btn.Tag;

                if (MessageBox.Show("هل أنت متأكد من حذف هذا القسم؟\nسيتم حذف جميع الاضبارات والوثائق التابعة له!",
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    // هنا سيتم تنفيذ عملية الحذف الفعلية
                    // Here the actual deletion will be implemented
                    MessageBox.Show("تم حذف القسم بنجاح!", "نجح الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadDepartments();
                    Logger.LogInfo(string.Format("تم حذف القسم: {0}", departmentId));
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حذف القسم", ex);
                MessageBox.Show("حدث خطأ أثناء حذف القسم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث إضافة اضبارة
        /// Add file box event handler
        /// </summary>
        private void BtnAddFileBox_Click(object sender, EventArgs e)
        {
            try
            {
                Button btn = sender as Button;
                int departmentId = (int)btn.Tag;

                MessageBox.Show(string.Format("إضافة اضبارة جديدة للقسم رقم: {0}\n\n(سيتم تطوير نافذة إضافة الاضبارة لاحقاً)", departmentId),
                    "إضافة اضبارة", MessageBoxButtons.OK, MessageBoxIcon.Information);

                Logger.LogInfo(string.Format("تم طلب إضافة اضبارة للقسم: {0}", departmentId));
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة اضبارة", ex);
            }
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}

