using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج إدارة الأقسام
    /// Departments management form
    /// </summary>
    public partial class DepartmentsForm : Form
    {
        public DepartmentsForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            this.Text = "إدارة الأقسام";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label tempLabel = new Label
            {
                Text = "نموذج إدارة الأقسام قيد التطوير...",
                Font = new Font("Tahoma", 14F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(tempLabel);
        }
    }
}
