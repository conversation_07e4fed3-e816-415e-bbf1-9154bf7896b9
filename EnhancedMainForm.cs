using System;
using System.Drawing;
using System.Windows.Forms;
using Ali_Mo<PERSON>.Utilities;

namespace <PERSON>_<PERSON>
{
    /// <summary>
    /// النموذج الرئيسي المحسن
    /// Enhanced Main Form
    /// </summary>
    public partial class EnhancedMainForm : Form
    {
        private Panel headerPanel;
        private Panel navigationPanel;
        private Panel contentPanel;
        
        public EnhancedMainForm()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Text = "النظام المحسن للأرشفة الإلكترونية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = true;
            this.TopMost = false;
            this.BringToFront();
            this.Focus();
            
            CreateHeader();
            CreateNavigation();
            CreateContent();
            
            ShowWelcomePage();
            
            // Ensure form is visible
            this.Visible = true;
            this.Activate();
        }
        
        private void CreateHeader()
        {
            headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(25, 118, 210)
            };
            
            Label titleLabel = new Label
            {
                Text = "🌟 النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(600, 35),
                Location = new Point(20, 10),
                BackColor = Color.Transparent
            };
            
            Label welcomeLabel = new Label
            {
                Text = string.Format("مرحباً بك، المدير - {0} - الإصدار 2.0.0 المحسن", DateTime.Now.ToString("dd/MM/yyyy")),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(600, 25),
                Location = new Point(20, 45),
                BackColor = Color.Transparent
            };
            
            Button exitButton = new Button
            {
                Text = "🚪 خروج",
                Size = new Size(80, 30),
                Location = new Point(1100, 25),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10)
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => {
                if (MessageBox.Show("هل تريد الخروج من النظام المحسن؟", "تأكيد الخروج", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    Application.Exit();
                }
            };
            
            headerPanel.Controls.AddRange(new Control[] { titleLabel, welcomeLabel, exitButton });
            this.Controls.Add(headerPanel);
        }
        
        private void CreateNavigation()
        {
            navigationPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240)
            };
            
            var navButtons = new[]
            {
                new { Text = "📄 إدارة الوثائق", Color = Color.FromArgb(76, 175, 80) },
                new { Text = "🏢 إدارة الأقسام", Color = Color.FromArgb(33, 150, 243) },
                new { Text = "👥 إدارة المستخدمين", Color = Color.FromArgb(156, 39, 176) },
                new { Text = "📊 التقارير المتقدمة", Color = Color.FromArgb(255, 152, 0) },
                new { Text = "🔍 البحث الذكي", Color = Color.FromArgb(96, 125, 139) },
                new { Text = "💾 النسخ الاحتياطي", Color = Color.FromArgb(121, 85, 72) },
                new { Text = "⚙️ الإعدادات", Color = Color.FromArgb(158, 158, 158) },
                new { Text = "ℹ️ حول النظام", Color = Color.FromArgb(244, 67, 54) }
            };
            
            for (int i = 0; i < navButtons.Length; i++)
            {
                var btn = navButtons[i];
                Button navButton = new Button
                {
                    Text = btn.Text,
                    Size = new Size(140, 45),
                    Location = new Point(20 + (i * 150), 7),
                    BackColor = btn.Color,
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Tahoma", 9, FontStyle.Bold)
                };
                
                navButton.FlatAppearance.BorderSize = 0;
                navButton.Click += (s, e) => ShowFeatureContent(btn.Text);
                
                navigationPanel.Controls.Add(navButton);
            }
            
            this.Controls.Add(navigationPanel);
        }
        
        private void CreateContent()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            
            this.Controls.Add(contentPanel);
        }
        
        private void ShowWelcomePage()
        {
            contentPanel.Controls.Clear();
            
            Label welcomeTitle = new Label
            {
                Text = "🌟 مرحباً بك في النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 50),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            Label featuresLabel = new Label
            {
                Text = "✨ الإصدار 2.0.0 - النسخة المحسنة المتطورة\n\n🚀 الميزات المتقدمة الجديدة:\n\n" +
                       "🎨 واجهة مستخدم متطورة مع تأثيرات بصرية متقدمة\n" +
                       "⚡ أداء محسن وسرعة استجابة عالية\n" +
                       "🔐 أمان متقدم وحماية شاملة للبيانات\n" +
                       "👥 تجربة مستخدم استثنائية وتنقل ذكي\n" +
                       "🌍 دعم عربي كامل مع تحسينات RTL\n" +
                       "📱 تصميم متجاوب وحديث\n" +
                       "🛠️ أدوات متقدمة وميزات احترافية\n" +
                       "🔑 نظام إدارة المستخدمين المتكامل\n" +
                       "💾 حفظ واستعادة كلمات المرور\n" +
                       "🆕 إنشاء حسابات جديدة\n\n" +
                       "استخدم شريط التنقل العلوي للوصول إلى جميع الميزات المتقدمة",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(1000, 400),
                Location = new Point(20, 80),
                TextAlign = ContentAlignment.TopRight
            };
            
            contentPanel.Controls.AddRange(new Control[] { welcomeTitle, featuresLabel });
        }
        
        private void ShowFeatureContent(string featureName)
        {
            contentPanel.Controls.Clear();
            
            Label featureTitle = new Label
            {
                Text = featureName,
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            Label featureContent = new Label
            {
                Text = string.Format("🌟 {0}\n\nهذه الميزة متاحة في النظام المحسن مع جميع التحسينات والوظائف المتطورة.\n\nالنظام المحسن يحتوي على:\n• واجهة متطورة وجذابة\n• أداء محسن وسرعة عالية\n• أمان متقدم وحماية شاملة\n• تجربة مستخدم استثنائية\n• دعم عربي كامل ومتطور\n• ميزات احترافية متقدمة\n• نظام إدارة المستخدمين الكامل\n\nجميع الميزات الأصلية متاحة مع تحسينات جذرية وإضافات متطورة.", featureName),
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(1000, 300),
                Location = new Point(20, 80),
                TextAlign = ContentAlignment.TopRight
            };
            
            // Add special functionality for user management
            if (featureName.Contains("المستخدمين"))
            {
                Button changePasswordButton = new Button
                {
                    Text = "🔑 تغيير كلمة المرور",
                    Size = new Size(150, 40),
                    Location = new Point(200, 400),
                    BackColor = Color.FromArgb(255, 152, 0),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Tahoma", 11, FontStyle.Bold)
                };
                changePasswordButton.FlatAppearance.BorderSize = 0;
                changePasswordButton.Click += (s, e) => ShowChangePasswordForm();
                
                contentPanel.Controls.Add(changePasswordButton);
            }
            
            Button backButton = new Button
            {
                Text = "🏠 العودة للرئيسية",
                Size = new Size(150, 40),
                Location = new Point(425, 400),
                BackColor = Color.FromArgb(25, 118, 210),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => ShowWelcomePage();
            
            contentPanel.Controls.AddRange(new Control[] { featureTitle, featureContent, backButton });
        }
        
        private void ShowChangePasswordForm()
        {
            try
            {
                using (var changePasswordForm = new ChangePasswordForm())
                {
                    if (changePasswordForm.ShowDialog(this) == DialogResult.OK)
                    {
                        MessageBox.Show("✅ تم تغيير كلمة المرور بنجاح!", "نجحت العملية", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج تغيير كلمة المرور", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج تغيير كلمة المرور", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
