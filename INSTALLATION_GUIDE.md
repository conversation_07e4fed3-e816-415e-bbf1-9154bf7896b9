# دليل التثبيت والتشغيل - Installation and Running Guide

## نظام الأرشفة الإلكترونية - Electronic Archiving System

---

## 🚀 التشغيل السريع - Quick Start

### الخطوات الأساسية - Basic Steps

1. **افتح Visual Studio** - Open Visual Studio
2. **افتح المشروع**: `Ali Mola.sln` - Open Project: `Ali Mo<PERSON>.sln`
3. **اضغط F5 للتشغيل** - Press F5 to Run
4. **استخدم بيانات الدخول**: - Use Login Credentials:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

---

## 📋 المتطلبات التفصيلية - Detailed Requirements

### متطلبات النظام - System Requirements
- **نظام التشغيل**: Windows 7 أو أحدث - Windows 7 or newer
- **الذاكرة**: 2 جيجابايت RAM على الأقل - At least 2GB RAM
- **المساحة**: 500 ميجابايت مساحة فارغة - 500MB free space
- **الدقة**: 1024x768 أو أعلى - 1024x768 or higher

### متطلبات التطوير - Development Requirements
- **Visual Studio**: 2019 أو أحدث - 2019 or newer
- **.NET Framework**: 4.8.1 أو أحدث - 4.8.1 or newer
- **SQL Server LocalDB**: (اختياري للنسخة الكاملة) - (Optional for full version)

---

## 🔧 خطوات التثبيت التفصيلية - Detailed Installation Steps

### الخطوة 1: تحضير البيئة - Step 1: Environment Setup

#### أ. تثبيت Visual Studio - Install Visual Studio
1. حمل Visual Studio Community (مجاني) - Download Visual Studio Community (Free)
2. تأكد من تثبيت .NET Framework development workload
3. تأكد من تثبيت .NET Framework 4.8.1

#### ب. تحميل المشروع - Download Project
1. حمل جميع ملفات المشروع - Download all project files
2. ضعها في مجلد منفصل - Place them in a separate folder
3. تأكد من وجود جميع الملفات - Ensure all files are present

### الخطوة 2: فتح المشروع - Step 2: Open Project

1. **افتح Visual Studio** - Open Visual Studio
2. **اختر "Open a project or solution"** - Choose "Open a project or solution"
3. **حدد ملف `Ali Mola.sln`** - Select `Ali Mola.sln` file
4. **انتظر تحميل المشروع** - Wait for project to load

### الخطوة 3: بناء المشروع - Step 3: Build Project

1. **من القائمة**: Build → Build Solution - From menu: Build → Build Solution
2. **أو اضغط**: `Ctrl + Shift + B` - Or press: `Ctrl + Shift + B`
3. **تأكد من عدم وجود أخطاء** - Ensure no errors exist
4. **إذا ظهرت أخطاء**: راجع قسم استكشاف الأخطاء - If errors appear: check troubleshooting section

### الخطوة 4: تشغيل التطبيق - Step 4: Run Application

1. **اضغط F5** أو من القائمة: Debug → Start Debugging - Press F5 or from menu: Debug → Start Debugging
2. **ستظهر نافذة تسجيل الدخول** - Login window will appear
3. **أدخل بيانات الدخول الافتراضية** - Enter default login credentials
4. **استمتع بالنظام!** - Enjoy the system!

---

## 🔐 بيانات الدخول الافتراضية - Default Login Credentials

```
اسم المستخدم - Username: admin
كلمة المرور - Password: admin123
الدور - Role: مدير النظام (Admin)
```

**ملاحظة مهمة**: يُنصح بتغيير كلمة المرور بعد أول تسجيل دخول من الإعدادات.
**Important Note**: It's recommended to change the password after first login from Settings.

---

## 📁 هيكل الملفات - File Structure

```
Ali_Mola/
├── 📄 Ali Mola.sln                    # ملف الحل - Solution file
├── 📄 Ali Mola.csproj                 # ملف المشروع - Project file
├── 📄 Program.cs                      # نقطة الدخول - Entry point
├── 📄 App.config                      # ملف التكوين - Configuration
├── 📁 DataAccess/                     # طبقة البيانات - Data layer
│   ├── 📁 Models/                     # نماذج البيانات - Data models
│   ├── 📄 ArchivingSystemContext.cs   # سياق Entity Framework
│   └── 📄 SimpleDataContext.cs        # سياق مبسط - Simplified context
├── 📁 BusinessLogic/                  # منطق الأعمال - Business logic
│   ├── 📄 AuthenticationService.cs    # خدمة المصادقة
│   ├── 📄 DocumentService.cs          # خدمة الوثائق
│   ├── 📄 AttachmentService.cs        # خدمة المرفقات
│   ├── 📄 DepartmentService.cs        # خدمة الأقسام
│   └── 📄 BackupService.cs            # خدمة النسخ الاحتياطي
├── 📁 Forms/                          # النماذج - Forms
│   ├── 📄 LoginForm.cs                # نموذج تسجيل الدخول
│   ├── 📄 MainForm.cs                 # النموذج الرئيسي
│   ├── 📄 AddDocumentForm.cs          # إضافة وثيقة
│   ├── 📄 ViewDocumentsForm.cs        # عرض الوثائق
│   ├── 📄 DocumentViewerForm.cs       # عارض تفاصيل الوثيقة
│   ├── 📄 DepartmentsForm.cs          # إدارة الأقسام
│   ├── 📄 AddDepartmentForm.cs        # إضافة قسم
│   ├── 📄 SettingsForm.cs             # الإعدادات
│   └── 📄 ChangePasswordForm.cs       # تغيير كلمة المرور
├── 📁 Utilities/                      # المرافق - Utilities
│   ├── 📄 Logger.cs                   # نظام التسجيل
│   ├── 📄 PasswordHelper.cs           # مساعد كلمات المرور
│   └── 📄 SerialNumberGenerator.cs    # مولد الأرقام المسلسلة
└── 📁 Documentation/                  # التوثيق - Documentation
    ├── 📄 README.md                   # دليل شامل
    ├── 📄 PROJECT_SUMMARY.md          # ملخص المشروع
    └── 📄 INSTALLATION_GUIDE.md       # دليل التثبيت
```

---

## ⚡ الميزات المتاحة - Available Features

### ✅ الميزات المكتملة - Completed Features

#### 1. نظام المصادقة - Authentication System
- تسجيل دخول آمن - Secure login
- تشفير كلمات المرور - Password encryption
- إدارة الجلسات - Session management
- تغيير كلمة المرور - Password change

#### 2. إدارة الوثائق - Document Management
- إضافة وثائق جديدة - Add new documents
- تصنيف (صادر/وارد) - Classification (outgoing/incoming)
- ترقيم تلقائي - Automatic numbering
- عرض تفاصيل شامل - Comprehensive details view

#### 3. البحث والعرض - Search and Display
- بحث متعدد المعايير - Multi-criteria search
- فلترة متقدمة - Advanced filtering
- عرض في جداول منسقة - Formatted table display
- ترتيب وتصفية النتائج - Sort and filter results

#### 4. إدارة المرفقات - Attachment Management
- رفع ملفات متعددة - Upload multiple files
- أنواع ملفات مختلفة - Various file types
- عرض وتحميل المرفقات - View and download attachments
- إدارة الملفات المؤقتة - Temporary files management

#### 5. إدارة الأقسام - Department Management
- إضافة وتعديل الأقسام - Add and edit departments
- إدارة الاضبارات - File boxes management
- بحث في الأقسام - Search departments
- إحصائيات شاملة - Comprehensive statistics

#### 6. النسخ الاحتياطي - Backup System
- إنشاء نسخ احتياطية - Create backups
- استعادة البيانات - Restore data
- ضغط الملفات - File compression
- معلومات النسخة الاحتياطية - Backup information

#### 7. الإعدادات - Settings
- إعدادات النظام - System settings
- إدارة المستخدمين - User management
- تنظيف الملفات - File cleanup
- معلومات النظام - System information

---

## 🎯 كيفية الاستخدام - How to Use

### 1. البدء - Getting Started
1. شغل التطبيق وسجل دخولك - Run app and login
2. ستظهر الصفحة الرئيسية مع الإحصائيات - Main page with statistics will appear
3. استخدم شريط التنقل العلوي للانتقال - Use top navigation bar to navigate

### 2. إضافة وثيقة جديدة - Add New Document
1. اضغط "إضافة وثيقة جديدة" - Click "Add New Document"
2. املأ جميع البيانات المطلوبة - Fill all required data
3. أضف المرفقات إذا لزم الأمر - Add attachments if needed
4. احفظ الوثيقة - Save document

### 3. البحث في الوثائق - Search Documents
1. اضغط "عرض الوثائق" - Click "View Documents"
2. استخدم فلاتر البحث المتاحة - Use available search filters
3. اضغط "بحث" لتطبيق الفلاتر - Click "Search" to apply filters
4. انقر نقراً مزدوجاً على وثيقة لعرض تفاصيلها - Double-click document to view details

### 4. إدارة الأقسام - Manage Departments
1. اضغط "الأقسام" من شريط التنقل - Click "Departments" from navigation
2. اضغط "إضافة قسم جديد" لإضافة قسم - Click "Add New Department" to add
3. استخدم أزرار العمليات لكل قسم - Use action buttons for each department

### 5. النسخ الاحتياطي - Backup
1. اذهب إلى "الإعدادات" → تبويب "النسخ الاحتياطي" - Go to "Settings" → "Backup" tab
2. اضغط "إنشاء نسخة احتياطية" - Click "Create Backup"
3. اختر مكان الحفظ - Choose save location

---

## 🔧 استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة - Common Issues

#### 1. خطأ في بناء المشروع - Build Error
**المشكلة**: فشل في بناء المشروع - Build failed
**الحل**: 
- تأكد من تثبيت .NET Framework 4.8.1
- تأكد من وجود جميع الملفات
- أعد تشغيل Visual Studio

#### 2. خطأ في تسجيل الدخول - Login Error
**المشكلة**: لا يمكن تسجيل الدخول - Cannot login
**الحل**:
- تأكد من استخدام البيانات الصحيحة: `admin` / `admin123`
- تأكد من تشغيل التطبيق بصلاحيات كافية

#### 3. خطأ في الملفات - File Error
**المشكلة**: خطأ في الوصول للملفات - File access error
**الحل**:
- تأكد من صلاحيات الكتابة في مجلد التطبيق
- شغل Visual Studio كمدير إذا لزم الأمر

#### 4. مشاكل الخط العربي - Arabic Font Issues
**المشكلة**: عرض خاطئ للنصوص العربية - Incorrect Arabic text display
**الحل**:
- تأكد من تثبيت خط Tahoma
- تأكد من إعدادات اللغة في Windows

---

## 📞 الدعم والمساعدة - Support and Help

### للحصول على المساعدة - For Help
- راجع ملف README.md للتوثيق الشامل - Check README.md for comprehensive documentation
- راجع ملف PROJECT_SUMMARY.md لملخص المشروع - Check PROJECT_SUMMARY.md for project summary
- تحقق من ملفات السجلات في مجلد Logs - Check log files in Logs folder

### الإبلاغ عن المشاكل - Report Issues
- سجل المشكلة مع تفاصيل كاملة - Log issue with full details
- أرفق ملفات السجلات إذا أمكن - Attach log files if possible
- اذكر خطوات إعادة إنتاج المشكلة - Mention steps to reproduce

---

## 🎉 تهانينا! - Congratulations!

إذا وصلت إلى هنا، فقد نجحت في تثبيت وتشغيل نظام الأرشفة الإلكترونية!
If you've reached here, you've successfully installed and run the Electronic Archiving System!

**استمتع بالنظام واستكشف جميع ميزاته المتقدمة!**
**Enjoy the system and explore all its advanced features!**

---

**تاريخ آخر تحديث - Last Updated:** ديسمبر 2024 - December 2024  
**الإصدار - Version:** 1.0.0  
**الحالة - Status:** جاهز للاستخدام - Ready for Use
