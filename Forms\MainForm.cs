using System;
using System.Drawing;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.BusinessLogic;
using <PERSON>_<PERSON><PERSON>.Utilities;

namespace <PERSON>_<PERSON>.Forms
{
    /// <summary>
    /// النموذج الرئيسي للنظام
    /// Main system form
    /// </summary>
    public partial class MainForm : Form
    {
        private Timer welcomeTimer;
        private Label welcomeLabel;
        private Panel statisticsPanel;
        private Panel navigationPanel;

        /// <summary>
        /// منشئ النموذج الرئيسي
        /// Main form constructor
        /// </summary>
        public MainForm()
        {
            InitializeComponent();
            SetupForm();
            LoadStatistics();
            SetupWelcomeTimer();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "نظام الأرشفة الإلكترونية - الصفحة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.IsMdiContainer = true;

            // إعداد الخط العربي
            // Setup Arabic font
            Font arabicFont = new Font("Tahoma", 10F, FontStyle.Regular);
            this.Font = arabicFont;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateNavigationPanel();
            CreateWelcomeSection();
            CreateStatisticsPanel();
            CreateMainButtons();
        }

        /// <summary>
        /// إنشاء شريط التنقل العلوي
        /// Create top navigation panel
        /// </summary>
        private void CreateNavigationPanel()
        {
            navigationPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(25, 118, 210)
            };

            // زر الصفحة الرئيسية
            // Home button
            Button homeBtn = CreateNavButton("الرئيسية", 20);
            homeBtn.Click += (s, e) => { /* Already on home */ };
            navigationPanel.Controls.Add(homeBtn);

            // زر الأقسام
            // Departments button
            Button departmentsBtn = CreateNavButton("الأقسام", 140);
            departmentsBtn.Click += DepartmentsBtn_Click;
            navigationPanel.Controls.Add(departmentsBtn);

            // زر الإعدادات
            // Settings button
            Button settingsBtn = CreateNavButton("الإعدادات", 260);
            settingsBtn.Click += SettingsBtn_Click;
            navigationPanel.Controls.Add(settingsBtn);

            // معلومات المستخدم
            // User info
            Label userInfoLabel = new Label
            {
                Text = $"مرحباً، {AuthenticationService.CurrentUser?.FullName ?? AuthenticationService.CurrentUser?.Username}",
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10F),
                AutoSize = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Left
            };
            userInfoLabel.Location = new Point(this.Width - userInfoLabel.Width - 20, 20);
            navigationPanel.Controls.Add(userInfoLabel);

            // زر تسجيل الخروج
            // Logout button
            Button logoutBtn = new Button
            {
                Text = "تسجيل الخروج",
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F),
                Anchor = AnchorStyles.Top | AnchorStyles.Left
            };
            logoutBtn.FlatAppearance.BorderSize = 0;
            logoutBtn.Location = new Point(this.Width - logoutBtn.Width - 20, 15);
            logoutBtn.Click += LogoutBtn_Click;
            navigationPanel.Controls.Add(logoutBtn);

            this.Controls.Add(navigationPanel);
        }

        /// <summary>
        /// إنشاء زر التنقل
        /// Create navigation button
        /// </summary>
        private Button CreateNavButton(string text, int x)
        {
            Button btn = new Button
            {
                Text = text,
                Size = new Size(100, 40),
                Location = new Point(x, 10),
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btn.FlatAppearance.BorderSize = 0;
            btn.FlatAppearance.MouseOverBackColor = Color.FromArgb(30, 136, 229);
            return btn;
        }

        /// <summary>
        /// إنشاء قسم الترحيب
        /// Create welcome section
        /// </summary>
        private void CreateWelcomeSection()
        {
            welcomeLabel = new Label
            {
                Text = GetWelcomeMessage(),
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                AutoSize = true,
                Location = new Point(50, 80)
            };
            this.Controls.Add(welcomeLabel);

            Label dateLabel = new Label
            {
                Text = DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA")),
                Font = new Font("Tahoma", 12F),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(50, 110)
            };
            this.Controls.Add(dateLabel);
        }

        /// <summary>
        /// إنشاء لوحة الإحصائيات
        /// Create statistics panel
        /// </summary>
        private void CreateStatisticsPanel()
        {
            statisticsPanel = new Panel
            {
                Size = new Size(800, 150),
                Location = new Point(50, 150),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            Label statsTitle = new Label
            {
                Text = "إحصائيات النظام",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Location = new Point(20, 20),
                AutoSize = true
            };
            statisticsPanel.Controls.Add(statsTitle);

            this.Controls.Add(statisticsPanel);
        }

        /// <summary>
        /// إنشاء الأزرار الرئيسية
        /// Create main buttons
        /// </summary>
        private void CreateMainButtons()
        {
            // زر إضافة وثيقة جديدة
            // Add new document button
            Button addDocBtn = new Button
            {
                Text = "إضافة وثيقة جديدة",
                Size = new Size(200, 60),
                Location = new Point(50, 350),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 12F, FontStyle.Bold)
            };
            addDocBtn.FlatAppearance.BorderSize = 0;
            addDocBtn.Click += AddDocBtn_Click;
            this.Controls.Add(addDocBtn);

            // زر عرض الوثائق
            // View documents button
            Button viewDocsBtn = new Button
            {
                Text = "عرض الوثائق",
                Size = new Size(200, 60),
                Location = new Point(270, 350),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 12F, FontStyle.Bold)
            };
            viewDocsBtn.FlatAppearance.BorderSize = 0;
            viewDocsBtn.Click += ViewDocsBtn_Click;
            this.Controls.Add(viewDocsBtn);
        }

        /// <summary>
        /// تحميل الإحصائيات
        /// Load statistics
        /// </summary>
        private void LoadStatistics()
        {
            try
            {
                var stats = DocumentService.GetDocumentStatistics();
                
                // مسح الإحصائيات السابقة
                // Clear previous statistics
                if (statisticsPanel != null)
                {
                    var controlsToRemove = new Control[statisticsPanel.Controls.Count];
                    statisticsPanel.Controls.CopyTo(controlsToRemove, 0);
                    foreach (Control control in controlsToRemove)
                    {
                        if (control is Panel)
                            statisticsPanel.Controls.Remove(control);
                    }
                }

                // إنشاء بطاقات الإحصائيات
                // Create statistics cards
                CreateStatCard("إجمالي الوثائق", stats.TotalDocuments.ToString(), Color.FromArgb(33, 150, 243), 20, 60);
                CreateStatCard("الوثائق الصادرة", stats.OutgoingDocuments.ToString(), Color.FromArgb(76, 175, 80), 200, 60);
                CreateStatCard("الوثائق الواردة", stats.IncomingDocuments.ToString(), Color.FromArgb(255, 152, 0), 380, 60);
                CreateStatCard("وثائق هذا الشهر", stats.DocumentsThisMonth.ToString(), Color.FromArgb(156, 39, 176), 560, 60);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل الإحصائيات", ex);
            }
        }

        /// <summary>
        /// إنشاء بطاقة إحصائية
        /// Create statistics card
        /// </summary>
        private void CreateStatCard(string title, string value, Color color, int x, int y)
        {
            Panel card = new Panel
            {
                Size = new Size(160, 70),
                Location = new Point(x, y),
                BackColor = color
            };

            Label titleLabel = new Label
            {
                Text = title,
                ForeColor = Color.White,
                Font = new Font("Tahoma", 9F),
                Location = new Point(10, 10),
                AutoSize = true
            };

            Label valueLabel = new Label
            {
                Text = value,
                ForeColor = Color.White,
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                Location = new Point(10, 30),
                AutoSize = true
            };

            card.Controls.Add(titleLabel);
            card.Controls.Add(valueLabel);
            statisticsPanel.Controls.Add(card);
        }

        /// <summary>
        /// الحصول على رسالة الترحيب
        /// Get welcome message
        /// </summary>
        private string GetWelcomeMessage()
        {
            int hour = DateTime.Now.Hour;
            string greeting;

            if (hour < 12)
                greeting = "صباح الخير";
            else if (hour < 18)
                greeting = "مساء الخير";
            else
                greeting = "مساء الخير";

            return $"{greeting}، {AuthenticationService.CurrentUser?.FullName ?? AuthenticationService.CurrentUser?.Username}";
        }

        /// <summary>
        /// إعداد مؤقت الترحيب
        /// Setup welcome timer
        /// </summary>
        private void SetupWelcomeTimer()
        {
            welcomeTimer = new Timer
            {
                Interval = 60000 // دقيقة واحدة
            };
            welcomeTimer.Tick += (s, e) => {
                if (welcomeLabel != null)
                    welcomeLabel.Text = GetWelcomeMessage();
            };
            welcomeTimer.Start();
        }

        #region Event Handlers

        private void AddDocBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var addDocForm = new AddDocumentForm();
                if (addDocForm.ShowDialog() == DialogResult.OK)
                {
                    LoadStatistics(); // تحديث الإحصائيات
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج إضافة الوثيقة", ex);
                MessageBox.Show("حدث خطأ أثناء فتح نموذج إضافة الوثيقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewDocsBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var viewDocsForm = new ViewDocumentsForm();
                viewDocsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج عرض الوثائق", ex);
                MessageBox.Show("حدث خطأ أثناء فتح نموذج عرض الوثائق", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DepartmentsBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var deptForm = new DepartmentsForm();
                deptForm.ShowDialog();
                LoadStatistics(); // تحديث الإحصائيات
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج الأقسام", ex);
                MessageBox.Show("حدث خطأ أثناء فتح نموذج الأقسام", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SettingsBtn_Click(object sender, EventArgs e)
        {
            try
            {
                var settingsForm = new SettingsForm();
                settingsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج الإعدادات", ex);
                MessageBox.Show("حدث خطأ أثناء فتح نموذج الإعدادات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LogoutBtn_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                AuthenticationService.Logout();
                this.Close();
            }
        }

        #endregion

        /// <summary>
        /// تنظيف الموارد
        /// Cleanup resources
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                welcomeTimer?.Stop();
                welcomeTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
