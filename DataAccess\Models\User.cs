using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ali_Mola.DataAccess.Models
{
    /// <summary>
    /// نموذج المستخدم - يمثل المستخدمين في النظام
    /// User Model - Represents users in the system
    /// </summary>
    [Table("Users")]
    public class User
    {
        /// <summary>
        /// معرف المستخدم الفريد
        /// Unique user identifier
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int UserId { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// Username
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; }

        /// <summary>
        /// كلمة المرور المشفرة
        /// Encrypted password
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(255)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; }

        /// <summary>
        /// الاسم الكامل للمستخدم
        /// User's full name
        /// </summary>
        [StringLength(100)]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; }

        /// <summary>
        /// دور المستخدم في النظام
        /// User role in the system
        /// </summary>
        [Required]
        [StringLength(20)]
        [Display(Name = "الدور")]
        public string Role { get; set; } // "Admin" or "User"

        /// <summary>
        /// تاريخ إنشاء المستخدم
        /// User creation date
        /// </summary>
        [Required]
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تسجيل دخول
        /// Last login date
        /// </summary>
        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// حالة المستخدم (نشط/غير نشط)
        /// User status (Active/Inactive)
        /// </summary>
        [Required]
        [Display(Name = "الحالة")]
        public bool IsActive { get; set; }

        /// <summary>
        /// منشئ المستخدم
        /// User constructor
        /// </summary>
        public User()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
            Role = "User";
        }
    }
}
