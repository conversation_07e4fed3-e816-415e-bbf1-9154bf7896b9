using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج عرض الوثائق
    /// View documents form
    /// </summary>
    public partial class ViewDocumentsForm : Form
    {
        public ViewDocumentsForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            this.Text = "عرض الوثائق";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label tempLabel = new Label
            {
                Text = "نموذج عرض الوثائق قيد التطوير...",
                Font = new Font("Tahoma", 14F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(tempLabel);
        }
    }
}
