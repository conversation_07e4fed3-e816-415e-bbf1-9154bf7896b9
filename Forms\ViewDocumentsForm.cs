using System;
using System.Drawing;
using System.Windows.Forms;
using Ali_Mo<PERSON>.Utilities;

namespace <PERSON>_<PERSON>.Forms
{
    /// <summary>
    /// نموذج عرض الوثائق
    /// View documents form
    /// </summary>
    public partial class ViewDocumentsForm : Form
    {
        #region Controls
        private Panel searchPanel;
        private TextBox txtSearch;
        private ComboBox cmbDocumentType;
        private ComboBox cmbDepartment;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Button btnSearch;
        private Button btnClearSearch;
        private DataGridView dgvDocuments;
        private Button btnViewDocument;
        private Button btnClose;
        #endregion

        public ViewDocumentsForm()
        {
            InitializeComponent();
            SetupForm();
            LoadSampleData();
        }

        private void SetupForm()
        {
            this.Text = "عرض الوثائق";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateSearchPanel();
            CreateDataGrid();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء لوحة البحث
        /// Create search panel
        /// </summary>
        private void CreateSearchPanel()
        {
            searchPanel = new Panel
            {
                Size = new Size(1160, 100),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(searchPanel);

            // عنوان البحث
            // Search title
            Label titleLabel = new Label
            {
                Text = "البحث في الوثائق",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(150, 25),
                Location = new Point(20, 15)
            };
            searchPanel.Controls.Add(titleLabel);

            // مربع البحث
            // Search textbox
            Label searchLabel = new Label
            {
                Text = "البحث:",
                Size = new Size(60, 25),
                Location = new Point(20, 50),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(searchLabel);

            txtSearch = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(90, 50),
                PlaceholderText = "ابحث في الموضوع أو الرقم المسلسل..."
            };
            searchPanel.Controls.Add(txtSearch);

            // نوع الوثيقة
            // Document type
            Label typeLabel = new Label
            {
                Text = "النوع:",
                Size = new Size(50, 25),
                Location = new Point(310, 50),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(typeLabel);

            cmbDocumentType = new ComboBox
            {
                Size = new Size(100, 25),
                Location = new Point(370, 50),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbDocumentType.Items.AddRange(new[] { "الكل", "صادر", "وارد" });
            cmbDocumentType.SelectedIndex = 0;
            searchPanel.Controls.Add(cmbDocumentType);

            // القسم
            // Department
            Label deptLabel = new Label
            {
                Text = "القسم:",
                Size = new Size(50, 25),
                Location = new Point(490, 50),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(deptLabel);

            cmbDepartment = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(550, 50),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbDepartment.Items.AddRange(new[] { "جميع الأقسام", "القسم العام", "قسم الشؤون الإدارية", "قسم المالية" });
            cmbDepartment.SelectedIndex = 0;
            searchPanel.Controls.Add(cmbDepartment);

            // من تاريخ
            // From date
            Label fromLabel = new Label
            {
                Text = "من:",
                Size = new Size(30, 25),
                Location = new Point(720, 50),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(fromLabel);

            dtpFromDate = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(760, 50),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddMonths(-1)
            };
            searchPanel.Controls.Add(dtpFromDate);

            // إلى تاريخ
            // To date
            Label toLabel = new Label
            {
                Text = "إلى:",
                Size = new Size(30, 25),
                Location = new Point(900, 50),
                TextAlign = ContentAlignment.MiddleRight
            };
            searchPanel.Controls.Add(toLabel);

            dtpToDate = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(940, 50),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };
            searchPanel.Controls.Add(dtpToDate);

            // زر البحث
            // Search button
            btnSearch = new Button
            {
                Text = "بحث",
                Size = new Size(80, 30),
                Location = new Point(1070, 47),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F, FontStyle.Bold)
            };
            btnSearch.FlatAppearance.BorderSize = 0;
            btnSearch.Click += BtnSearch_Click;
            searchPanel.Controls.Add(btnSearch);

            // زر مسح البحث
            // Clear search button
            btnClearSearch = new Button
            {
                Text = "مسح",
                Size = new Size(60, 30),
                Location = new Point(1000, 47),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F)
            };
            btnClearSearch.FlatAppearance.BorderSize = 0;
            btnClearSearch.Click += BtnClearSearch_Click;
            searchPanel.Controls.Add(btnClearSearch);
        }

        /// <summary>
        /// إنشاء جدول البيانات
        /// Create data grid
        /// </summary>
        private void CreateDataGrid()
        {
            dgvDocuments = new DataGridView
            {
                Size = new Size(1160, 550),
                Location = new Point(20, 140),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                RowHeadersVisible = false
            };

            // إعداد الأعمدة
            // Setup columns
            dgvDocuments.Columns.Add("SerialNumber", "الرقم المسلسل");
            dgvDocuments.Columns.Add("EntryDate", "تاريخ الإدخال");
            dgvDocuments.Columns.Add("Type", "النوع");
            dgvDocuments.Columns.Add("DocumentDate", "تاريخ الوثيقة");
            dgvDocuments.Columns.Add("DocumentNumber", "رقم الوثيقة");
            dgvDocuments.Columns.Add("Subject", "الموضوع");
            dgvDocuments.Columns.Add("Department", "القسم");
            dgvDocuments.Columns.Add("FileBox", "الاضبارة");

            // تنسيق الأعمدة
            // Format columns
            dgvDocuments.Columns["SerialNumber"].Width = 150;
            dgvDocuments.Columns["EntryDate"].Width = 120;
            dgvDocuments.Columns["Type"].Width = 80;
            dgvDocuments.Columns["DocumentDate"].Width = 120;
            dgvDocuments.Columns["DocumentNumber"].Width = 120;
            dgvDocuments.Columns["Subject"].Width = 300;
            dgvDocuments.Columns["Department"].Width = 150;
            dgvDocuments.Columns["FileBox"].Width = 150;

            // تنسيق رأس الأعمدة
            // Format column headers
            dgvDocuments.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(25, 118, 210);
            dgvDocuments.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvDocuments.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dgvDocuments.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // تنسيق الصفوف
            // Format rows
            dgvDocuments.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvDocuments.DefaultCellStyle.BackColor = Color.White;
            dgvDocuments.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 248, 248);
            dgvDocuments.DefaultCellStyle.SelectionBackColor = Color.FromArgb(33, 150, 243);
            dgvDocuments.DefaultCellStyle.SelectionForeColor = Color.White;

            // أحداث الجدول
            // Grid events
            dgvDocuments.CellDoubleClick += DgvDocuments_CellDoubleClick;

            this.Controls.Add(dgvDocuments);
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر عرض الوثيقة
            // View document button
            btnViewDocument = new Button
            {
                Text = "عرض الوثيقة",
                Size = new Size(120, 40),
                Location = new Point(500, 710),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnViewDocument.FlatAppearance.BorderSize = 0;
            btnViewDocument.Click += BtnViewDocument_Click;
            this.Controls.Add(btnViewDocument);

            // زر إغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(120, 40),
                Location = new Point(640, 710),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);
        }

        /// <summary>
        /// تحميل بيانات تجريبية
        /// Load sample data
        /// </summary>
        private void LoadSampleData()
        {
            try
            {
                // إضافة بيانات تجريبية
                // Add sample data
                dgvDocuments.Rows.Add("صادر-1-2024-0001", "15/12/2024 10:30", "صادر", "15/12/2024", "123", "خطاب شكر وتقدير", "القسم العام", "اضبارة المراسلات");
                dgvDocuments.Rows.Add("وارد-1-2024-0001", "14/12/2024 14:20", "وارد", "14/12/2024", "456", "طلب معلومات", "قسم الشؤون الإدارية", "اضبارة عامة");
                dgvDocuments.Rows.Add("صادر-2-2024-0001", "13/12/2024 09:15", "صادر", "13/12/2024", "789", "قرار إداري", "قسم المالية", "اضبارة القرارات");
                dgvDocuments.Rows.Add("وارد-1-2024-0002", "12/12/2024 16:45", "وارد", "12/12/2024", "321", "شكوى", "القسم العام", "اضبارة الشكاوى");
                dgvDocuments.Rows.Add("صادر-1-2024-0002", "11/12/2024 11:30", "صادر", "11/12/2024", "654", "تعميم", "قسم الشؤون الإدارية", "اضبارة التعاميم");

                Logger.LogInfo("تم تحميل البيانات التجريبية لعرض الوثائق");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل البيانات التجريبية", ex);
            }
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث البحث
        /// Search event handler
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                // تطبيق فلاتر البحث (تجريبي)
                // Apply search filters (demo)
                string searchText = txtSearch.Text.Trim().ToLower();
                string documentType = cmbDocumentType.SelectedItem.ToString();
                string department = cmbDepartment.SelectedItem.ToString();

                foreach (DataGridViewRow row in dgvDocuments.Rows)
                {
                    bool visible = true;

                    // فلتر النص
                    // Text filter
                    if (!string.IsNullOrEmpty(searchText))
                    {
                        string subject = row.Cells["Subject"].Value?.ToString()?.ToLower() ?? "";
                        string serialNumber = row.Cells["SerialNumber"].Value?.ToString()?.ToLower() ?? "";

                        if (!subject.Contains(searchText) && !serialNumber.Contains(searchText))
                        {
                            visible = false;
                        }
                    }

                    // فلتر نوع الوثيقة
                    // Document type filter
                    if (documentType != "الكل")
                    {
                        string rowType = row.Cells["Type"].Value?.ToString() ?? "";
                        if (rowType != documentType)
                        {
                            visible = false;
                        }
                    }

                    // فلتر القسم
                    // Department filter
                    if (department != "جميع الأقسام")
                    {
                        string rowDepartment = row.Cells["Department"].Value?.ToString() ?? "";
                        if (rowDepartment != department)
                        {
                            visible = false;
                        }
                    }

                    row.Visible = visible;
                }

                Logger.LogInfo($"تم تطبيق البحث: {searchText}");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في البحث", ex);
                MessageBox.Show("حدث خطأ أثناء البحث", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث مسح البحث
        /// Clear search event handler
        /// </summary>
        private void BtnClearSearch_Click(object sender, EventArgs e)
        {
            try
            {
                txtSearch.Clear();
                cmbDocumentType.SelectedIndex = 0;
                cmbDepartment.SelectedIndex = 0;
                dtpFromDate.Value = DateTime.Today.AddMonths(-1);
                dtpToDate.Value = DateTime.Today;

                // إظهار جميع الصفوف
                // Show all rows
                foreach (DataGridViewRow row in dgvDocuments.Rows)
                {
                    row.Visible = true;
                }

                Logger.LogInfo("تم مسح فلاتر البحث");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في مسح البحث", ex);
            }
        }

        /// <summary>
        /// معالج حدث النقر المزدوج على الجدول
        /// Double click on grid event handler
        /// </summary>
        private void DgvDocuments_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                ViewSelectedDocument();
            }
        }

        /// <summary>
        /// معالج حدث عرض الوثيقة
        /// View document event handler
        /// </summary>
        private void BtnViewDocument_Click(object sender, EventArgs e)
        {
            ViewSelectedDocument();
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        /// <summary>
        /// عرض الوثيقة المحددة
        /// View selected document
        /// </summary>
        private void ViewSelectedDocument()
        {
            try
            {
                if (dgvDocuments.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvDocuments.SelectedRows[0];
                    string serialNumber = selectedRow.Cells["SerialNumber"].Value?.ToString() ?? "";
                    string subject = selectedRow.Cells["Subject"].Value?.ToString() ?? "";
                    string type = selectedRow.Cells["Type"].Value?.ToString() ?? "";
                    string documentNumber = selectedRow.Cells["DocumentNumber"].Value?.ToString() ?? "";
                    string department = selectedRow.Cells["Department"].Value?.ToString() ?? "";
                    string fileBox = selectedRow.Cells["FileBox"].Value?.ToString() ?? "";

                    // إنشاء وثيقة وهمية للعرض
                    // Create mock document for display
                    var document = new DataAccess.Models.Document
                    {
                        DocumentId = 1,
                        SerialNumber = serialNumber,
                        Type = type,
                        DocumentNumber = documentNumber,
                        DocumentDate = DateTime.Parse(selectedRow.Cells["DocumentDate"].Value?.ToString() ?? DateTime.Now.ToString()),
                        EntryDate = DateTime.Parse(selectedRow.Cells["EntryDate"].Value?.ToString() ?? DateTime.Now.ToString()),
                        Subject = subject,
                        From = "مرسل تجريبي",
                        To = "مستقبل تجريبي",
                        FileBox = new DataAccess.Models.FileBox
                        {
                            Name = fileBox,
                            Department = new DataAccess.Models.Department
                            {
                                Name = department
                            }
                        }
                    };

                    // فتح نافذة عرض تفاصيل الوثيقة
                    // Open document viewer window
                    using (var viewerForm = new DocumentViewerForm(document))
                    {
                        viewerForm.ShowDialog();
                    }

                    Logger.LogInfo($"تم عرض تفاصيل الوثيقة: {serialNumber}");
                }
                else
                {
                    MessageBox.Show("يرجى اختيار وثيقة لعرضها", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في عرض الوثيقة", ex);
                MessageBox.Show("حدث خطأ أثناء عرض الوثيقة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
