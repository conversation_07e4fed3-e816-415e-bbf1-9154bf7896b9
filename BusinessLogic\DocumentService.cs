using System;
using System.Collections.Generic;
using System.Linq;
using <PERSON>_<PERSON><PERSON>.DataAccess;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using Ali_Mola.Utilities;

namespace Ali_Mola.BusinessLogic
{
    /// <summary>
    /// خدمة الوثائق - تدير عمليات الوثائق
    /// Document Service - Manages document operations
    /// </summary>
    public class DocumentService
    {
        /// <summary>
        /// إضافة وثيقة جديدة
        /// Add new document
        /// </summary>
        /// <param name="document">الوثيقة</param>
        /// <returns>معرف الوثيقة الجديدة أو -1 في حالة الفشل</returns>
        public static int AddDocument(Document document)
        {
            try
            {
                if (document == null)
                    throw new ArgumentNullException(nameof(document));

                var context = new SimpleDataContext();

                // توليد الرقم المسلسل
                // Generate serial number
                document.SerialNumber = SerialNumberGenerator.GenerateSerialNumber(
                    document.Type, 1); // استخدام قسم افتراضي

                // تعيين تاريخ الإدخال
                // Set entry date
                document.EntryDate = DateTime.Now;
                document.DocumentId = context.Documents.Count + 1;

                // إضافة الوثيقة
                // Add document
                context.AddDocument(document);

                Logger.LogInfo(string.Format("تم إضافة وثيقة جديدة برقم مسلسل: {0}", document.SerialNumber));
                return document.DocumentId;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة الوثيقة", ex);
                return -1;
            }
        }

        /// <summary>
        /// تحديث وثيقة
        /// Update document
        /// </summary>
        /// <param name="document">الوثيقة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateDocument(Document document)
        {
            try
            {
                if (document == null)
                    throw new ArgumentNullException(nameof(document));

                var context = new SimpleDataContext();
                var existingDocument = context.Documents.FirstOrDefault(d => d.DocumentId == document.DocumentId);
                if (existingDocument == null)
                    return false;

                // تحديث البيانات
                // Update data
                existingDocument.Type = document.Type;
                existingDocument.DocumentNumber = document.DocumentNumber;
                existingDocument.DocumentDate = document.DocumentDate;
                existingDocument.Subject = document.Subject;
                existingDocument.From = document.From;
                existingDocument.To = document.To;
                existingDocument.FileBoxId = document.FileBoxId;

                Logger.LogInfo(string.Format("تم تحديث الوثيقة برقم مسلسل: {0}", existingDocument.SerialNumber));
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحديث الوثيقة", ex);
                return false;
            }
        }

        /// <summary>
        /// حذف وثيقة
        /// Delete document
        /// </summary>
        /// <param name="documentId">معرف الوثيقة</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteDocument(int documentId)
        {
            try
            {
                var context = new SimpleDataContext();
                var document = context.Documents.FirstOrDefault(d => d.DocumentId == documentId);

                if (document == null)
                    return false;

                // حذف الوثيقة
                // Delete document
                context.Documents.Remove(document);

                Logger.LogInfo(string.Format("تم حذف الوثيقة برقم مسلسل: {0}", document.SerialNumber));
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حذف الوثيقة", ex);
                return false;
            }
        }

        /// <summary>
        /// الحصول على وثيقة بالمعرف
        /// Get document by ID
        /// </summary>
        /// <param name="documentId">معرف الوثيقة</param>
        /// <returns>الوثيقة أو null</returns>
        public static Document GetDocument(int documentId)
        {
            try
            {
                var context = new SimpleDataContext();
                return context.Documents.FirstOrDefault(d => d.DocumentId == documentId);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على الوثيقة", ex);
                return null;
            }
        }

        /// <summary>
        /// البحث في الوثائق
        /// Search documents
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة الوثائق</returns>
        public static List<Document> SearchDocuments(DocumentSearchCriteria searchCriteria)
        {
            try
            {
                var context = new SimpleDataContext();
                var query = context.Documents.AsQueryable();

                // تطبيق فلاتر البحث
                // Apply search filters
                if (!string.IsNullOrEmpty(searchCriteria.SerialNumber))
                {
                    query = query.Where(d => d.SerialNumber.Contains(searchCriteria.SerialNumber));
                }

                if (!string.IsNullOrEmpty(searchCriteria.Subject))
                {
                    query = query.Where(d => d.Subject.Contains(searchCriteria.Subject));
                }

                if (!string.IsNullOrEmpty(searchCriteria.DocumentType))
                {
                    query = query.Where(d => d.Type == searchCriteria.DocumentType);
                }

                if (searchCriteria.FileBoxId.HasValue)
                {
                    query = query.Where(d => d.FileBoxId == searchCriteria.FileBoxId.Value);
                }

                if (searchCriteria.FromDate.HasValue)
                {
                    query = query.Where(d => d.DocumentDate >= searchCriteria.FromDate.Value);
                }

                if (searchCriteria.ToDate.HasValue)
                {
                    query = query.Where(d => d.DocumentDate <= searchCriteria.ToDate.Value);
                }

                if (searchCriteria.Year.HasValue)
                {
                    query = query.Where(d => d.DocumentDate.Year == searchCriteria.Year.Value);
                }

                // ترتيب النتائج
                // Order results
                return query.OrderByDescending(d => d.EntryDate).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في البحث في الوثائق", ex);
                return new List<Document>();
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الوثائق
        /// Get document statistics
        /// </summary>
        /// <returns>إحصائيات الوثائق</returns>
        public static DocumentStatistics GetDocumentStatistics()
        {
            try
            {
                using (var context = new SimpleDataContext())
                {
                    var simpleStats = context.GetStatistics();
                    var stats = new DocumentStatistics
                    {
                        TotalDocuments = simpleStats.TotalDocuments,
                        OutgoingDocuments = simpleStats.OutgoingDocuments,
                        IncomingDocuments = simpleStats.IncomingDocuments,
                        DocumentsThisMonth = simpleStats.DocumentsThisMonth,
                        DocumentsThisYear = simpleStats.DocumentsThisMonth // مؤقت
                    };

                    return stats;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على إحصائيات الوثائق", ex);
                return new DocumentStatistics();
            }
        }
    }

    /// <summary>
    /// معايير البحث في الوثائق
    /// Document search criteria
    /// </summary>
    public class DocumentSearchCriteria
    {
        public string SerialNumber { get; set; }
        public string Subject { get; set; }
        public string DocumentType { get; set; }
        public int? DepartmentId { get; set; }
        public int? FileBoxId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? Year { get; set; }
    }

    /// <summary>
    /// إحصائيات الوثائق
    /// Document statistics
    /// </summary>
    public class DocumentStatistics
    {
        public int TotalDocuments { get; set; }
        public int OutgoingDocuments { get; set; }
        public int IncomingDocuments { get; set; }
        public int DocumentsThisMonth { get; set; }
        public int DocumentsThisYear { get; set; }
    }
}
