using System;
using System.Linq;
using <PERSON>_Mo<PERSON>.DataAccess;

namespace Ali_Mola.Utilities
{
    /// <summary>
    /// مولد الأرقام المسلسلة - يوفر وظائف توليد الأرقام المسلسلة للوثائق
    /// Serial Number Generator - Provides serial number generation for documents
    /// </summary>
    public static class SerialNumberGenerator
    {
        /// <summary>
        /// توليد رقم مسلسل جديد للوثيقة
        /// Generate new serial number for document
        /// </summary>
        /// <param name="documentType">نوع الوثيقة (صادر/وارد)</param>
        /// <param name="departmentId">معرف القسم</param>
        /// <returns>الرقم المسلسل الجديد</returns>
        public static string GenerateSerialNumber(string documentType, int departmentId)
        {
            try
            {
                var context = new SimpleDataContext();
                    // الحصول على السنة الحالية
                    // Get current year
                    int currentYear = DateTime.Now.Year;
                    
                    // إنشاء بادئة الرقم المسلسل
                    // Create serial number prefix
                    string prefix = string.Format("{0}-{1}-{2}-", documentType, departmentId, currentYear);
                    
                    // البحث عن آخر رقم مسلسل لنفس النوع والقسم والسنة
                    // Find last serial number for same type, department and year
                    var lastDocument = context.Documents
                        .Where(d => d.SerialNumber.StartsWith(prefix))
                        .OrderByDescending(d => d.SerialNumber)
                        .FirstOrDefault();
                    
                    int nextNumber = 1;
                    
                    if (lastDocument != null)
                    {
                        // استخراج الرقم من آخر رقم مسلسل
                        // Extract number from last serial number
                        string lastSerialNumber = lastDocument.SerialNumber;
                        string numberPart = lastSerialNumber.Substring(prefix.Length);
                        
                        if (int.TryParse(numberPart, out int lastNumber))
                        {
                            nextNumber = lastNumber + 1;
                        }
                    }
                    
                    // إنشاء الرقم المسلسل الجديد مع تنسيق 4 أرقام
                    // Create new serial number with 4-digit formatting
                    return string.Format("{0}{1:D4}", prefix, nextNumber);
            }
            catch (Exception ex)
            {
                Logger.LogError("فشل في توليد الرقم المسلسل", ex);
                
                // في حالة الفشل، إنشاء رقم مسلسل بناءً على الوقت
                // In case of failure, create serial number based on time
                return string.Format("{0}-{1}-{2:yyyyMMddHHmmss}", documentType, departmentId, DateTime.Now);
            }
        }

        /// <summary>
        /// التحقق من صحة الرقم المسلسل
        /// Validate serial number format
        /// </summary>
        /// <param name="serialNumber">الرقم المسلسل</param>
        /// <returns>true إذا كان الرقم المسلسل صحيح</returns>
        public static bool IsValidSerialNumber(string serialNumber)
        {
            if (string.IsNullOrEmpty(serialNumber))
                return false;

            // تنسيق الرقم المسلسل: نوع-قسم-سنة-رقم
            // Serial number format: type-department-year-number
            string[] parts = serialNumber.Split('-');
            
            if (parts.Length != 4)
                return false;

            // التحقق من نوع الوثيقة
            // Check document type
            if (parts[0] != "صادر" && parts[0] != "وارد")
                return false;

            // التحقق من معرف القسم
            // Check department ID
            if (!int.TryParse(parts[1], out int departmentId) || departmentId <= 0)
                return false;

            // التحقق من السنة
            // Check year
            if (!int.TryParse(parts[2], out int year) || year < 2000 || year > DateTime.Now.Year + 1)
                return false;

            // التحقق من الرقم
            // Check number
            if (!int.TryParse(parts[3], out int number) || number <= 0)
                return false;

            return true;
        }

        /// <summary>
        /// استخراج معلومات من الرقم المسلسل
        /// Extract information from serial number
        /// </summary>
        /// <param name="serialNumber">الرقم المسلسل</param>
        /// <returns>معلومات الرقم المسلسل</returns>
        public static SerialNumberInfo ParseSerialNumber(string serialNumber)
        {
            if (!IsValidSerialNumber(serialNumber))
                return null;

            string[] parts = serialNumber.Split('-');
            
            return new SerialNumberInfo
            {
                DocumentType = parts[0],
                DepartmentId = int.Parse(parts[1]),
                Year = int.Parse(parts[2]),
                Number = int.Parse(parts[3])
            };
        }
    }

    /// <summary>
    /// معلومات الرقم المسلسل
    /// Serial number information
    /// </summary>
    public class SerialNumberInfo
    {
        public string DocumentType { get; set; }
        public int DepartmentId { get; set; }
        public int Year { get; set; }
        public int Number { get; set; }
    }
}
