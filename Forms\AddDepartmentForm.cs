﻿using System;
using System.Drawing;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.DataAccess.Models;
using <PERSON>_<PERSON><PERSON>.Utilities;

namespace <PERSON>_<PERSON>.Forms
{
    /// <summary>
    /// نموذج إضافة قسم جديد
    /// Add new department form
    /// </summary>
    public partial class AddDepartmentForm : Form
    {
        #region Controls
        private TextBox txtDepartmentName;
        private TextBox txtDescription;
        private Button btnSave;
        private Button btnCancel;
        #endregion

        /// <summary>
        /// منشئ نموذج إضافة القسم
        /// Add department form constructor
        /// </summary>
        public AddDepartmentForm()
        {
            InitializeComponent();
            SetupForm();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "إضافة قسم جديد";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(245, 245, 245);

            // إعداد الخط العربي
            // Setup Arabic font
            Font arabicFont = new Font("Tahoma", 10F, FontStyle.Regular);
            this.Font = arabicFont;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        /// <summary>
        /// إنشاء عناصر التحكم
        /// Create controls
        /// </summary>
        private void CreateControls()
        {
            // لوحة رئيسية
            // Main panel
            Panel mainPanel = new Panel
            {
                Size = new Size(460, 280),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(mainPanel);

            // عنوان النموذج
            // Form title
            Label titleLabel = new Label
            {
                Text = "إضافة قسم جديد",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(200, 30),
                Location = new Point(20, 20)
            };
            mainPanel.Controls.Add(titleLabel);

            // أيقونة القسم
            // Department icon
            Panel iconPanel = new Panel
            {
                Size = new Size(60, 60),
                Location = new Point(350, 15),
                BackColor = Color.FromArgb(25, 118, 210)
            };

            Label iconLabel = new Label
            {
                Text = "📁",
                Font = new Font("Segoe UI Emoji", 20F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            mainPanel.Controls.Add(iconPanel);

            // تسمية اسم القسم
            // Department name label
            Label nameLabel = new Label
            {
                Text = "اسم القسم:",
                Size = new Size(100, 25),
                Location = new Point(20, 80),
                TextAlign = ContentAlignment.MiddleRight
            };
            mainPanel.Controls.Add(nameLabel);

            // مربع نص اسم القسم
            // Department name textbox
            txtDepartmentName = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(130, 80),
                Font = new Font("Tahoma", 10F)
            };
            mainPanel.Controls.Add(txtDepartmentName);

            // تسمية الوصف
            // Description label
            Label descLabel = new Label
            {
                Text = "الوصف:",
                Size = new Size(100, 25),
                Location = new Point(20, 120),
                TextAlign = ContentAlignment.MiddleRight
            };
            mainPanel.Controls.Add(descLabel);

            // مربع نص الوصف
            // Description textbox
            txtDescription = new TextBox
            {
                Size = new Size(300, 80),
                Location = new Point(130, 120),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Font = new Font("Tahoma", 10F)
            };
            mainPanel.Controls.Add(txtDescription);

            // معلومات إضافية
            // Additional info
            Label infoLabel = new Label
            {
                Text = "ملاحظة: سيتم إنشاء اضبارة افتراضية للقسم الجديد",
                Size = new Size(400, 20),
                Location = new Point(20, 220),
                ForeColor = Color.Gray,
                Font = new Font("Tahoma", 9F)
            };
            mainPanel.Controls.Add(infoLabel);

            // زر الحفظ
            // Save button
            btnSave = new Button
            {
                Text = "حفظ القسم",
                Size = new Size(100, 35),
                Location = new Point(200, 250),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;
            mainPanel.Controls.Add(btnSave);

            // زر الإلغاء
            // Cancel button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 35),
                Location = new Point(320, 250),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            mainPanel.Controls.Add(btnCancel);

            // تعيين الأزرار الافتراضية
            // Set default buttons
            this.AcceptButton = btnSave;
            this.CancelButton = btnCancel;

            // تركيز على مربع اسم القسم
            // Focus on department name textbox
            txtDepartmentName.Focus();
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث الحفظ
        /// Save event handler
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                // Validate data
                if (!ValidateData())
                    return;

                // تعطيل الأزرار أثناء المعالجة
                // Disable buttons during processing
                btnSave.Enabled = false;
                btnCancel.Enabled = false;
                this.Cursor = Cursors.WaitCursor;

                // إنشاء القسم الجديد (محاكاة)
                // Create new department (simulation)
                var newDepartment = new Department
                {
                    Name = txtDepartmentName.Text.Trim(),
                    CreatedDate = DateTime.Now
                };

                // هنا سيتم حفظ القسم في قاعدة البيانات
                // Here the department will be saved to database
                
                Logger.LogInfo(string.Format("تم إنشاء قسم جديد: {0}", newDepartment.Name));
                
                MessageBox.Show("تم إنشاء القسم بنجاح!", "نجح الحفظ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حفظ القسم", ex);
                MessageBox.Show("حدث خطأ أثناء حفظ القسم", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // إعادة تمكين الأزرار
                // Re-enable buttons
                btnSave.Enabled = true;
                btnCancel.Enabled = true;
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// معالج حدث الإلغاء
        /// Cancel event handler
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate data
        /// </summary>
        private bool ValidateData()
        {
            if (string.IsNullOrWhiteSpace(txtDepartmentName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم القسم", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDepartmentName.Focus();
                return false;
            }

            if (txtDepartmentName.Text.Trim().Length < 3)
            {
                MessageBox.Show("اسم القسم يجب أن يكون 3 أحرف على الأقل", "خطأ في البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDepartmentName.Focus();
                return false;
            }

            return true;
        }
    }
}

