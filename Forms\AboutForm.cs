﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Ali_<PERSON><PERSON>.Utilities;

namespace <PERSON>_<PERSON>.Forms
{
    /// <summary>
    /// نموذج حول البرنامج
    /// About form
    /// </summary>
    public partial class AboutForm : Form
    {
        #region Controls
        private Panel logoPanel;
        private Label titleLabel;
        private Label versionLabel;
        private Label descriptionLabel;
        private Label developedByLabel;
        private Label copyrightLabel;
        private Button btnClose;
        private Button btnSystemInfo;
        #endregion

        /// <summary>
        /// منشئ نموذج حول البرنامج
        /// About form constructor
        /// </summary>
        public AboutForm()
        {
            InitializeComponent();
            SetupForm();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "حول البرنامج";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(245, 245, 245);

            // إعداد الخط العربي
            // Setup Arabic font
            Font arabicFont = new Font("Tahoma", 10F, FontStyle.Regular);
            this.Font = arabicFont;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateContent();
        }

        /// <summary>
        /// إنشاء محتوى النموذج
        /// Create form content
        /// </summary>
        private void CreateContent()
        {
            // لوحة رئيسية
            // Main panel
            Panel mainPanel = new Panel
            {
                Size = new Size(460, 530),
                Location = new Point(20, 20),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(mainPanel);

            // لوحة الشعار
            // Logo panel
            logoPanel = new Panel
            {
                Size = new Size(120, 120),
                Location = new Point(170, 30),
                BackColor = Color.FromArgb(25, 118, 210)
            };

            Label logoLabel = new Label
            {
                Text = "📁",
                Font = new Font("Segoe UI Emoji", 48F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            logoPanel.Controls.Add(logoLabel);
            mainPanel.Controls.Add(logoPanel);

            // عنوان البرنامج
            // Program title
            titleLabel = new Label
            {
                Text = "نظام الأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(400, 40),
                Location = new Point(30, 170),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(titleLabel);

            // العنوان الفرعي
            // Subtitle
            Label subtitleLabel = new Label
            {
                Text = "Electronic Archiving System",
                Font = new Font("Tahoma", 12F, FontStyle.Italic),
                ForeColor = Color.Gray,
                Size = new Size(400, 25),
                Location = new Point(30, 210),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(subtitleLabel);

            // رقم الإصدار
            // Version number
            versionLabel = new Label
            {
                Text = "الإصدار 1.0.0",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80),
                Size = new Size(400, 25),
                Location = new Point(30, 250),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(versionLabel);

            // وصف البرنامج
            // Program description
            descriptionLabel = new Label
            {
                Text = "نظام شامل لإدارة وأرشفة الوثائق الإلكترونية\n" +
                       "يوفر إمكانيات متقدمة للبحث والتصنيف والحفظ\n" +
                       "مع واجهة عربية سهلة الاستخدام\n\n" +
                       "الميزات الرئيسية:\n" +
                       "• إدارة الوثائق والمرفقات\n" +
                       "• البحث المتقدم والتقارير\n" +
                       "• إدارة الأقسام والاضبارات\n" +
                       "• النسخ الاحتياطي والاستعادة\n" +
                       "• نظام أمان متقدم",
                Font = new Font("Tahoma", 10F),
                ForeColor = Color.FromArgb(64, 64, 64),
                Size = new Size(400, 180),
                Location = new Point(30, 290),
                TextAlign = ContentAlignment.TopCenter
            };
            mainPanel.Controls.Add(descriptionLabel);

            // معلومات المطور
            // Developer info
            developedByLabel = new Label
            {
                Text = "تم التطوير بواسطة: فريق التطوير\nباستخدام: C# .NET Framework\nتاريخ البناء: " + DateTime.Now.ToString("yyyy-MM-dd"),
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.Gray,
                Size = new Size(400, 60),
                Location = new Point(30, 480),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(developedByLabel);

            // حقوق النشر
            // Copyright
            copyrightLabel = new Label
            {
                Text = string.Format("© {0} جميع الحقوق محفوظة", DateTime.Now.Year),
                Font = new Font("Tahoma", 8F),
                ForeColor = Color.Gray,
                Size = new Size(400, 20),
                Location = new Point(30, 540),
                TextAlign = ContentAlignment.MiddleCenter
            };
            mainPanel.Controls.Add(copyrightLabel);

            // زر معلومات النظام
            // System info button
            btnSystemInfo = new Button
            {
                Text = "معلومات النظام",
                Size = new Size(120, 35),
                Location = new Point(150, 570),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F)
            };
            btnSystemInfo.FlatAppearance.BorderSize = 0;
            btnSystemInfo.Click += BtnSystemInfo_Click;
            this.Controls.Add(btnSystemInfo);

            // زر الإغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(100, 35),
                Location = new Point(290, 570),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 9F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);

            // تعيين الأزرار الافتراضية
            // Set default buttons
            this.CancelButton = btnClose;
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث معلومات النظام
        /// System info event handler
        /// </summary>
        private void BtnSystemInfo_Click(object sender, EventArgs e)
        {
            try
            {
                string systemInfo = GetSystemInfo();
                
                MessageBox.Show(systemInfo, "معلومات النظام", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                Logger.LogInfo("تم عرض معلومات النظام");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في عرض معلومات النظام", ex);
                MessageBox.Show("حدث خطأ أثناء عرض معلومات النظام", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        /// <summary>
        /// الحصول على معلومات النظام
        /// Get system information
        /// </summary>
        private string GetSystemInfo()
        {
            try
            {
                return "معلومات النظام التفصيلية:\n" +
                       "==============================\n\n" +
                       "اسم النظام: نظام الأرشفة الإلكترونية\n" +
                       "الإصدار: 1.0.0\n" +
                       string.Format("تاريخ البناء: {0}\n\n", DateTime.Now.ToString("yyyy-MM-dd")) +
                       "معلومات البيئة:\n" +
                       string.Format("نظام التشغيل: {0}\n", Environment.OSVersion) +
                       string.Format("إصدار .NET: {0}\n", Environment.Version) +
                       string.Format("اسم الجهاز: {0}\n", Environment.MachineName) +
                       string.Format("اسم المستخدم: {0}\n", Environment.UserName) +
                       string.Format("مجلد التطبيق: {0}\n\n", AppDomain.CurrentDomain.BaseDirectory) +
                       "معلومات الذاكرة:\n" +
                       string.Format("الذاكرة المستخدمة: {0} ميجابايت\n", (GC.GetTotalMemory(false) / 1024.0 / 1024.0).ToString("F1")) +
                       string.Format("عدد المعالجات: {0}\n\n", Environment.ProcessorCount) +
                       "معلومات إضافية:\n" +
                       string.Format("وقت التشغيل: {0} دقيقة\n", (Environment.TickCount / 1000.0 / 60.0).ToString("F1")) +
                       string.Format("المنطقة الزمنية: {0}", TimeZoneInfo.Local.DisplayName);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على معلومات النظام", ex);
                return "خطأ في تحميل معلومات النظام";
            }
        }
    }
}

