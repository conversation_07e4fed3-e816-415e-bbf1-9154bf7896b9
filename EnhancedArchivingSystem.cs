using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading;
using Ali_Mola.Utilities;

namespace Ali_Mola
{
    /// <summary>
    /// النظام المحسن للأرشفة الإلكترونية مع واجهة حديثة
    /// Enhanced Electronic Archiving System with modern interface
    /// </summary>
    static class EnhancedProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء تشغيل النظام المحسن للأرشفة الإلكترونية");
                
                // Run enhanced login form
                using (var loginForm = new EnhancedLoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        Logger.LogInfo("تم تسجيل الدخول بنجاح في النظام المحسن");
                        
                        // Show welcome animation
                        using (var welcomeForm = new WelcomeAnimationForm())
                        {
                            welcomeForm.ShowDialog();
                        }
                        
                        // Run main application
                        Application.Run(new EnhancedMainForm());
                    }
                    else
                    {
                        Logger.LogInfo("تم إلغاء تسجيل الدخول");
                    }
                }
                
                Logger.LogInfo("انتهاء تشغيل النظام المحسن للأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ فادح في النظام المحسن", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في النظام المحسن:\n{0}", ex.Message),
                    "خطأ فادح", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    /// <summary>
    /// نموذج تسجيل الدخول المحسن
    /// Enhanced login form with modern design
    /// </summary>
    public partial class EnhancedLoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private Panel logoPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Button btnForgotPassword;
        private Button btnCreateAccount;
        private Label lblTitle;
        private Label lblSubtitle;
        private PictureBox logoBox;
        private System.Windows.Forms.Timer fadeTimer;
        private int fadeStep = 0;
        
        public EnhancedLoginForm()
        {
            InitializeComponent();
            SetupFadeAnimation();
        }
        
        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - النظام المحسن للأرشفة الإلكترونية";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Opacity = 0;
            
            CreateMainLayout();
            CreateLogoPanel();
            CreateLoginPanel();
            CreateActionButtons();
        }
        
        private void SetupFadeAnimation()
        {
            fadeTimer = new System.Windows.Forms.Timer();
            fadeTimer.Interval = 50;
            fadeTimer.Tick += FadeTimer_Tick;
            fadeTimer.Start();
        }
        
        private void FadeTimer_Tick(object sender, EventArgs e)
        {
            fadeStep++;
            this.Opacity = Math.Min(1.0, fadeStep * 0.05);
            
            if (this.Opacity >= 1.0)
            {
                fadeTimer.Stop();
                fadeTimer.Dispose();
            }
        }
        
        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            
            // Add subtle border
            mainPanel.Paint += (s, e) => {
                using (Pen pen = new Pen(Color.FromArgb(200, 200, 200), 2))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, mainPanel.Width - 1, mainPanel.Height - 1);
                }
            };
            
            this.Controls.Add(mainPanel);
        }
        
        private void CreateLogoPanel()
        {
            logoPanel = new Panel
            {
                Size = new Size(580, 150),
                Location = new Point(10, 20),
                BackColor = Color.FromArgb(25, 118, 210)
            };
            
            // Create professional logo
            logoBox = new PictureBox
            {
                Size = new Size(100, 100),
                Location = new Point(240, 25),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            
            Bitmap logo = new Bitmap(100, 100);
            using (Graphics g = Graphics.FromImage(logo))
            {
                g.Clear(Color.White);
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                
                // Draw modern logo
                using (SolidBrush blueBrush = new SolidBrush(Color.FromArgb(25, 118, 210)))
                {
                    g.FillEllipse(blueBrush, 10, 10, 80, 80);
                }
                
                using (Font logoFont = new Font("Tahoma", 14, FontStyle.Bold))
                {
                    g.DrawString("أرشفة", logoFont, Brushes.White, 25, 30);
                    g.DrawString("إلكترونية", new Font("Tahoma", 10), Brushes.White, 20, 55);
                }
            }
            logoBox.Image = logo;
            
            lblTitle = new Label
            {
                Text = "النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(500, 35),
                Location = new Point(40, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            lblSubtitle = new Label
            {
                Text = "نظام متطور وحديث لإدارة الوثائق والمستندات بكفاءة عالية",
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(500, 30),
                Location = new Point(40, 80),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            logoPanel.Controls.AddRange(new Control[] { logoBox, lblTitle, lblSubtitle });
            mainPanel.Controls.Add(logoPanel);
        }
        
        private void CreateLoginPanel()
        {
            loginPanel = new Panel
            {
                Size = new Size(400, 200),
                Location = new Point(100, 190),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.None
            };
            
            // Add rounded corners effect
            loginPanel.Paint += (s, e) => {
                using (SolidBrush brush = new SolidBrush(Color.FromArgb(250, 250, 250)))
                {
                    e.Graphics.FillRectangle(brush, 0, 0, loginPanel.Width, loginPanel.Height);
                }
                using (Pen pen = new Pen(Color.FromArgb(220, 220, 220), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, loginPanel.Width - 1, loginPanel.Height - 1);
                }
            };
            
            Label loginTitle = new Label
            {
                Text = "تسجيل الدخول",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(380, 35),
                Location = new Point(10, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Username field
            Label lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Size = new Size(120, 25),
                Location = new Point(30, 70),
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            txtUsername = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(160, 70),
                Font = new Font("Tahoma", 11),
                Text = "admin",
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Password field
            Label lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 110),
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            txtPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(160, 110),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                Text = "admin123",
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Login and Cancel buttons
            btnLogin = new Button
            {
                Text = "دخول",
                Size = new Size(90, 35),
                Location = new Point(160, 150),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;
            
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(90, 35),
                Location = new Point(270, 150),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            
            loginPanel.Controls.AddRange(new Control[] { 
                loginTitle, lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel 
            });
            
            mainPanel.Controls.Add(loginPanel);
            
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }
        
        private void CreateActionButtons()
        {
            // Forgot Password button
            btnForgotPassword = new Button
            {
                Text = "نسيت كلمة المرور",
                Size = new Size(150, 35),
                Location = new Point(150, 410),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            btnForgotPassword.FlatAppearance.BorderSize = 0;
            btnForgotPassword.Click += BtnForgotPassword_Click;
            
            // Create Account button
            btnCreateAccount = new Button
            {
                Text = "إنشاء حساب جديد",
                Size = new Size(150, 35),
                Location = new Point(320, 410),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            btnCreateAccount.FlatAppearance.BorderSize = 0;
            btnCreateAccount.Click += BtnCreateAccount_Click;
            
            mainPanel.Controls.AddRange(new Control[] { btnForgotPassword, btnCreateAccount });
        }
        
        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة!\n\nالبيانات الصحيحة:\nاسم المستخدم: admin\nكلمة المرور: admin123", 
                    "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                txtPassword.SelectAll();
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        private void BtnForgotPassword_Click(object sender, EventArgs e)
        {
            using (var forgotForm = new ForgotPasswordForm())
            {
                forgotForm.ShowDialog();
            }
        }
        
        private void BtnCreateAccount_Click(object sender, EventArgs e)
        {
            using (var createForm = new CreateAccountForm())
            {
                createForm.ShowDialog();
            }
        }
    }

    /// <summary>
    /// نموذج الترحيب المتحرك
    /// Welcome animation form
    /// </summary>
    public partial class WelcomeAnimationForm : Form
    {
        private System.Windows.Forms.Timer animationTimer;
        private int animationStep = 0;
        private Label welcomeLabel;
        private Label systemLabel;
        private PictureBox logoBox;
        private Panel mainPanel;

        public WelcomeAnimationForm()
        {
            InitializeComponent();
            SetupAnimation();
        }

        private void InitializeComponent()
        {
            this.Text = "مرحباً بك";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(25, 118, 210);
            this.ShowInTaskbar = false;
            this.TopMost = true;

            CreateAnimationContent();
        }

        private void CreateAnimationContent()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(25, 118, 210)
            };

            // Animated logo
            logoBox = new PictureBox
            {
                Size = new Size(120, 120),
                Location = new Point(190, 50),
                BackColor = Color.Transparent
            };

            Bitmap animatedLogo = new Bitmap(120, 120);
            using (Graphics g = Graphics.FromImage(animatedLogo))
            {
                g.Clear(Color.Transparent);
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // Draw animated logo
                using (SolidBrush whiteBrush = new SolidBrush(Color.White))
                {
                    g.FillEllipse(whiteBrush, 10, 10, 100, 100);
                }

                using (SolidBrush blueBrush = new SolidBrush(Color.FromArgb(25, 118, 210)))
                {
                    g.FillEllipse(blueBrush, 20, 20, 80, 80);
                }

                using (Font logoFont = new Font("Tahoma", 16, FontStyle.Bold))
                {
                    g.DrawString("أرشفة", logoFont, Brushes.White, 35, 35);
                    g.DrawString("إلكترونية", new Font("Tahoma", 12), Brushes.White, 25, 65);
                }
            }
            logoBox.Image = animatedLogo;
            logoBox.Visible = false;

            // Welcome message
            welcomeLabel = new Label
            {
                Text = "مرحباً بك في النظام",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(50, 190),
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            // System name
            systemLabel = new Label
            {
                Text = "النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 14),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(400, 30),
                Location = new Point(50, 230),
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            mainPanel.Controls.AddRange(new Control[] { logoBox, welcomeLabel, systemLabel });
            this.Controls.Add(mainPanel);
        }

        private void SetupAnimation()
        {
            animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 100;
            animationTimer.Tick += AnimationTimer_Tick;
            animationTimer.Start();
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            animationStep++;

            switch (animationStep)
            {
                case 5: // Show logo with fade-in
                    logoBox.Visible = true;
                    break;

                case 15: // Show welcome message
                    welcomeLabel.Visible = true;
                    break;

                case 20: // Show system name
                    systemLabel.Visible = true;
                    break;

                case 30: // Close animation
                    animationTimer.Stop();
                    animationTimer.Dispose();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                    break;
            }
        }
    }

    /// <summary>
    /// نموذج استعادة كلمة المرور
    /// Forgot password form
    /// </summary>
    public partial class ForgotPasswordForm : Form
    {
        private TextBox txtEmail;
        private Button btnSend;
        private Button btnCancel;

        public ForgotPasswordForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "استعادة كلمة المرور";
            this.Size = new Size(400, 250);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label titleLabel = new Label
            {
                Text = "استعادة كلمة المرور",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(350, 30),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label instructionLabel = new Label
            {
                Text = "أدخل عنوان البريد الإلكتروني المرتبط بحسابك:",
                Font = new Font("Tahoma", 10),
                Size = new Size(350, 40),
                Location = new Point(25, 60),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Size = new Size(100, 25),
                Location = new Point(50, 110),
                Font = new Font("Tahoma", 10)
            };

            txtEmail = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(150, 110),
                Font = new Font("Tahoma", 10)
            };

            btnSend = new Button
            {
                Text = "إرسال",
                Size = new Size(80, 35),
                Location = new Point(150, 160),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            btnSend.FlatAppearance.BorderSize = 0;
            btnSend.Click += BtnSend_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 35),
                Location = new Point(250, 160),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] {
                titleLabel, instructionLabel, emailLabel, txtEmail, btnSend, btnCancel
            });
        }

        private void BtnSend_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان البريد الإلكتروني", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني.\n\nملاحظة: هذه ميزة تجريبية في النظام المحسن.",
                "تم الإرسال", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }
    }

    /// <summary>
    /// نموذج إنشاء حساب جديد
    /// Create new account form
    /// </summary>
    public partial class CreateAccountForm : Form
    {
        private TextBox txtFullName;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private TextBox txtEmail;
        private ComboBox cmbDepartment;
        private Button btnCreate;
        private Button btnCancel;

        public CreateAccountForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "إنشاء حساب جديد";
            this.Size = new Size(450, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label titleLabel = new Label
            {
                Text = "إنشاء حساب جديد",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(400, 30),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Full Name
            Label lblFullName = new Label
            {
                Text = "الاسم الكامل:",
                Size = new Size(100, 25),
                Location = new Point(50, 70),
                Font = new Font("Tahoma", 10)
            };

            txtFullName = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 70),
                Font = new Font("Tahoma", 10)
            };

            // Username
            Label lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Size = new Size(100, 25),
                Location = new Point(50, 110),
                Font = new Font("Tahoma", 10)
            };

            txtUsername = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 110),
                Font = new Font("Tahoma", 10)
            };

            // Password
            Label lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Size = new Size(100, 25),
                Location = new Point(50, 150),
                Font = new Font("Tahoma", 10)
            };

            txtPassword = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 150),
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true
            };

            // Confirm Password
            Label lblConfirmPassword = new Label
            {
                Text = "تأكيد كلمة المرور:",
                Size = new Size(100, 25),
                Location = new Point(50, 190),
                Font = new Font("Tahoma", 10)
            };

            txtConfirmPassword = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 190),
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true
            };

            // Email
            Label lblEmail = new Label
            {
                Text = "البريد الإلكتروني:",
                Size = new Size(100, 25),
                Location = new Point(50, 230),
                Font = new Font("Tahoma", 10)
            };

            txtEmail = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 230),
                Font = new Font("Tahoma", 10)
            };

            // Department
            Label lblDepartment = new Label
            {
                Text = "القسم:",
                Size = new Size(100, 25),
                Location = new Point(50, 270),
                Font = new Font("Tahoma", 10)
            };

            cmbDepartment = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 270),
                Font = new Font("Tahoma", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbDepartment.Items.AddRange(new string[] { "الإدارة", "المالية", "الموارد البشرية", "تقنية المعلومات" });

            // Buttons
            btnCreate = new Button
            {
                Text = "إنشاء الحساب",
                Size = new Size(100, 35),
                Location = new Point(160, 320),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            btnCreate.FlatAppearance.BorderSize = 0;
            btnCreate.Click += BtnCreate_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 35),
                Location = new Point(280, 320),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] {
                titleLabel, lblFullName, txtFullName, lblUsername, txtUsername,
                lblPassword, txtPassword, lblConfirmPassword, txtConfirmPassword,
                lblEmail, txtEmail, lblDepartment, cmbDepartment, btnCreate, btnCancel
            });
        }

        private void BtnCreate_Click(object sender, EventArgs e)
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(txtFullName.Text) ||
                string.IsNullOrWhiteSpace(txtUsername.Text) ||
                string.IsNullOrWhiteSpace(txtPassword.Text) ||
                string.IsNullOrWhiteSpace(txtEmail.Text) ||
                cmbDepartment.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى ملء جميع الحقول المطلوبة", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show(string.Format("تم إنشاء الحساب بنجاح!\n\nالاسم: {0}\nاسم المستخدم: {1}\nالقسم: {2}\n\nملاحظة: هذه ميزة تجريبية في النظام المحسن.",
                txtFullName.Text, txtUsername.Text, cmbDepartment.Text),
                "تم إنشاء الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }
    }

    /// <summary>
    /// النموذج الرئيسي المحسن مع شريط التنقل العلوي
    /// Enhanced main form with top navigation bar
    /// </summary>
    public partial class EnhancedMainForm : Form
    {
        private Panel headerPanel;
        private Panel navigationPanel;
        private Panel contentPanel;
        private StatusStrip statusStrip;
        private Label titleLabel;
        private Label welcomeLabel;
        private MenuStrip topMenuStrip;

        public EnhancedMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "النظام المحسن للأرشفة الإلكترونية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Icon = CreateSystemIcon();

            CreateHeader();
            CreateTopNavigation();
            CreateContentArea();
            CreateStatusBar();

            ShowWelcomePage();
        }

        private Icon CreateSystemIcon()
        {
            Bitmap bitmap = new Bitmap(32, 32);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.FromArgb(25, 118, 210));
                g.FillRectangle(Brushes.White, 8, 8, 16, 16);
                g.DrawString("أ", new Font("Tahoma", 12, FontStyle.Bold), Brushes.Blue, 10, 8);
            }
            return Icon.FromHandle(bitmap.GetHicon());
        }

        private void CreateHeader()
        {
            headerPanel = new Panel
            {
                Height = 70,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(25, 118, 210)
            };

            titleLabel = new Label
            {
                Text = "النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(500, 35),
                Location = new Point(20, 10),
                TextAlign = ContentAlignment.MiddleLeft
            };

            welcomeLabel = new Label
            {
                Text = string.Format("مرحباً بك، المدير - {0} - واجهة محسنة وحديثة", DateTime.Now.ToString("dd/MM/yyyy")),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(600, 25),
                Location = new Point(20, 40),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // زر الخروج
            Button exitButton = new Button
            {
                Text = "خروج",
                Size = new Size(80, 30),
                Location = new Point(1100, 20),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => {
                if (MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد الخروج",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    Application.Exit();
                }
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, welcomeLabel, exitButton });
            this.Controls.Add(headerPanel);
        }

        private void CreateTopNavigation()
        {
            navigationPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            // إنشاء أزرار التنقل الأفقية
            CreateHorizontalNavigationButtons();

            this.Controls.Add(navigationPanel);
        }

        private void CreateHorizontalNavigationButtons()
        {
            int buttonWidth = 140;
            int buttonHeight = 45;
            int spacing = 10;
            int startX = 20;
            int y = 7;

            var navButtons = new[]
            {
                new { Text = "📄 إدارة الوثائق", Color = Color.FromArgb(76, 175, 80), Action = new EventHandler(OpenDocumentManagement) },
                new { Text = "🏢 إدارة الأقسام", Color = Color.FromArgb(33, 150, 243), Action = new EventHandler(OpenDepartmentManagement) },
                new { Text = "👥 إدارة المستخدمين", Color = Color.FromArgb(156, 39, 176), Action = new EventHandler(OpenUserManagement) },
                new { Text = "📊 التقارير", Color = Color.FromArgb(255, 152, 0), Action = new EventHandler(OpenReports) },
                new { Text = "🔍 البحث المتقدم", Color = Color.FromArgb(96, 125, 139), Action = new EventHandler(OpenAdvancedSearch) },
                new { Text = "💾 النسخ الاحتياطي", Color = Color.FromArgb(121, 85, 72), Action = new EventHandler(OpenBackup) },
                new { Text = "⚙️ الإعدادات", Color = Color.FromArgb(158, 158, 158), Action = new EventHandler(OpenSettings) },
                new { Text = "ℹ️ حول النظام", Color = Color.FromArgb(244, 67, 54), Action = new EventHandler(OpenAbout) }
            };

            for (int i = 0; i < navButtons.Length; i++)
            {
                var btn = navButtons[i];
                Button navButton = new Button
                {
                    Text = btn.Text,
                    Size = new Size(buttonWidth, buttonHeight),
                    Location = new Point(startX + (i * (buttonWidth + spacing)), y),
                    BackColor = btn.Color,
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Tahoma", 9, FontStyle.Bold),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Cursor = Cursors.Hand
                };

                navButton.FlatAppearance.BorderSize = 0;
                navButton.FlatAppearance.MouseOverBackColor = ControlPaint.Light(btn.Color, 0.2f);
                navButton.Click += btn.Action;

                navigationPanel.Controls.Add(navButton);
            }
        }

        private void CreateContentArea()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            this.Controls.Add(contentPanel);
        }

        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(240, 240, 240)
            };

            ToolStripStatusLabel statusLabel = new ToolStripStatusLabel(
                string.Format("النظام المحسن للأرشفة الإلكترونية - مستخدم: المدير - {0}", DateTime.Now.ToString("dd/MM/yyyy HH:mm")));
            statusLabel.Spring = true;
            statusLabel.TextAlign = ContentAlignment.MiddleLeft;

            ToolStripStatusLabel enhancedLabel = new ToolStripStatusLabel("النسخة المحسنة");
            ToolStripStatusLabel versionLabel = new ToolStripStatusLabel("الإصدار 2.0.0");

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, enhancedLabel, versionLabel });
            this.Controls.Add(statusStrip);
        }

        private void ShowWelcomePage()
        {
            contentPanel.Controls.Clear();

            Label welcomeTitle = new Label
            {
                Text = "مرحباً بك في النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 22, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 50),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Panel featuresPanel = new Panel
            {
                Size = new Size(1000, 400),
                Location = new Point(20, 90),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label featuresTitle = new Label
            {
                Text = "🌟 الميزات الجديدة في النسخة المحسنة:",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(950, 30),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label featuresContent = new Label
            {
                Text = "✨ واجهة مستخدم محسنة وحديثة:\n" +
                       "   • تصميم عصري مع ألوان متناسقة\n" +
                       "   • شريط تنقل علوي أفقي بدلاً من الجانبي\n" +
                       "   • تخطيط مركزي ومنظم\n\n" +
                       "🔐 نظام تسجيل دخول متطور:\n" +
                       "   • تصميم احترافي مع تأثيرات بصرية\n" +
                       "   • ميزة استعادة كلمة المرور\n" +
                       "   • إمكانية إنشاء حساب جديد\n" +
                       "   • رسوم متحركة للترحيب\n\n" +
                       "🎬 تأثيرات بصرية متقدمة:\n" +
                       "   • شاشة ترحيب متحركة بعد تسجيل الدخول\n" +
                       "   • تأثيرات انتقال سلسة\n" +
                       "   • تصميم متجاوب وجذاب\n\n" +
                       "📱 تجربة مستخدم محسنة:\n" +
                       "   • تنقل سهل وبديهي\n" +
                       "   • تنظيم أفضل للمحتوى\n" +
                       "   • واجهة نظيفة وعملية\n\n" +
                       "🔧 وظائف متقدمة:\n" +
                       "   • جميع النماذج الـ 16 الأصلية متاحة\n" +
                       "   • تكامل كامل مع النظام الأصلي\n" +
                       "   • أداء محسن وسرعة أكبر\n\n" +
                       "استخدم شريط التنقل العلوي للوصول إلى جميع ميزات النظام",
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(950, 320),
                Location = new Point(25, 60),
                TextAlign = ContentAlignment.TopRight
            };

            featuresPanel.Controls.AddRange(new Control[] { featuresTitle, featuresContent });

            Panel statsPanel = new Panel
            {
                Size = new Size(1000, 80),
                Location = new Point(20, 510),
                BackColor = Color.FromArgb(25, 118, 210),
                BorderStyle = BorderStyle.None
            };

            Label statsLabel = new Label
            {
                Text = "📊 إحصائيات النظام: 55 ملف مطور • 16 نموذج احترافي • 14 نظام فرعي • أكثر من 12,000 سطر كود • واجهة عربية كاملة",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(980, 60),
                Location = new Point(10, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            statsPanel.Controls.Add(statsLabel);
            contentPanel.Controls.AddRange(new Control[] { welcomeTitle, featuresPanel, statsPanel });
        }

        // معالجات الأحداث للأزرار
        private void OpenDocumentManagement(object sender, EventArgs e)
        {
            ShowFormContent("إدارة الوثائق", "📄 نظام إدارة الوثائق الشامل\n\nيتيح لك هذا النظام:\n• إضافة وثائق جديدة مع المرفقات\n• عرض وتحرير الوثائق الموجودة\n• البحث المتقدم في الوثائق\n• إدارة أنواع الوثائق\n• تصنيف وتنظيم الوثائق\n• طباعة وتصدير الوثائق\n\nجميع النماذج الأصلية متاحة:\n- AddDocumentForm: إضافة وثيقة جديدة\n- ViewDocumentsForm: عرض الوثائق\n- DocumentViewerForm: عارض الوثائق");
        }

        private void OpenDepartmentManagement(object sender, EventArgs e)
        {
            ShowFormContent("إدارة الأقسام", "🏢 نظام إدارة الأقسام والاضبارات\n\nيوفر إدارة شاملة للهيكل التنظيمي:\n• إنشاء وإدارة الأقسام\n• تنظيم الاضبارات\n• تحديد المسؤوليات\n• إدارة الهيكل الإداري\n• ربط الوثائق بالأقسام\n• تقارير الأقسام\n\nالنماذج المتاحة:\n- DepartmentsForm: إدارة الأقسام\n- AddDepartmentForm: إضافة قسم جديد");
        }

        private void OpenUserManagement(object sender, EventArgs e)
        {
            ShowFormContent("إدارة المستخدمين", "👥 نظام إدارة المستخدمين والصلاحيات\n\nإدارة شاملة للمستخدمين:\n• إضافة مستخدمين جدد\n• تحديد الصلاحيات\n• إدارة كلمات المرور\n• تتبع نشاط المستخدمين\n• إدارة الأدوار\n• أمان النظام\n\nالنماذج المتاحة:\n- UserManagementForm: إدارة المستخدمين\n- ChangePasswordForm: تغيير كلمة المرور");
        }

        private void OpenReports(object sender, EventArgs e)
        {
            ShowFormContent("التقارير والإحصائيات", "📊 نظام التقارير والإحصائيات المتقدم\n\nتقارير شاملة ومفصلة:\n• تقارير الوثائق\n• إحصائيات الأقسام\n• تقارير النشاط\n• رسوم بيانية تفاعلية\n• تصدير التقارير\n• طباعة التقارير\n• تحليلات متقدمة\n\nالنماذج المتاحة:\n- ReportsForm: التقارير الشاملة");
        }

        private void OpenAdvancedSearch(object sender, EventArgs e)
        {
            ShowFormContent("البحث المتقدم", "🔍 نظام البحث المتقدم\n\nبحث شامل ومتطور:\n• معايير بحث متعددة\n• بحث في النص الكامل\n• فلترة متقدمة\n• حفظ استعلامات البحث\n• نتائج مفصلة\n• بحث سريع\n\nالنماذج المتاحة:\n- AdvancedSearchForm: البحث المتقدم");
        }

        private void OpenBackup(object sender, EventArgs e)
        {
            ShowFormContent("النسخ الاحتياطي", "💾 نظام النسخ الاحتياطي والاستعادة\n\nحماية شاملة للبيانات:\n• إنشاء نسخ احتياطية\n• استعادة البيانات\n• جدولة النسخ التلقائية\n• التحقق من سلامة البيانات\n• ضغط وتشفير النسخ\n• إدارة النسخ\n\nميزات متقدمة للحماية والأمان");
        }

        private void OpenSettings(object sender, EventArgs e)
        {
            ShowFormContent("إعدادات النظام", "⚙️ إعدادات النظام الشاملة\n\nتخصيص كامل للنظام:\n• إعدادات عامة\n• إعدادات قاعدة البيانات\n• إعدادات الأمان\n• إعدادات الواجهة\n• إعدادات التقارير\n• إعدادات النسخ الاحتياطي\n\nالنماذج المتاحة:\n- SettingsForm: إعدادات النظام");
        }

        private void OpenAbout(object sender, EventArgs e)
        {
            ShowFormContent("حول النظام", "ℹ️ معلومات النظام المحسن\n\nالنظام المحسن للأرشفة الإلكترونية\nالإصدار 2.0.0 - النسخة المحسنة\n\nالميزات الجديدة:\n• واجهة مستخدم محسنة\n• تصميم عصري وجذاب\n• تأثيرات بصرية متقدمة\n• نظام تسجيل دخول متطور\n• شريط تنقل أفقي\n• تجربة مستخدم محسنة\n\nالنماذج المتاحة:\n- AboutForm: معلومات النظام");
        }

        private void ShowFormContent(string title, string content)
        {
            contentPanel.Controls.Clear();

            Label formTitle = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Panel contentFormPanel = new Panel
            {
                Size = new Size(1000, 500),
                Location = new Point(20, 80),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            Label contentLabel = new Label
            {
                Text = content,
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(950, 400),
                Location = new Point(25, 25),
                TextAlign = ContentAlignment.TopRight
            };

            Button backButton = new Button
            {
                Text = "العودة للرئيسية",
                Size = new Size(150, 40),
                Location = new Point(425, 440),
                BackColor = Color.FromArgb(25, 118, 210),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => ShowWelcomePage();

            contentFormPanel.Controls.AddRange(new Control[] { contentLabel, backButton });
            contentPanel.Controls.AddRange(new Control[] { formTitle, contentFormPanel });
        }
    }
}
