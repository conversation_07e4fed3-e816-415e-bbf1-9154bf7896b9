using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Linq;
using Ali_Mola.Utilities;

namespace Ali_Mola
{
    /// <summary>
    /// نظام إدارة المستخدمين المتقدم
    /// Advanced User Management System
    /// </summary>
    public static class UserManager
    {
        private static readonly string UsersFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "users.dat");
        private static Dictionary<string, UserAccount> users = new Dictionary<string, UserAccount>();

        static UserManager()
        {
            LoadUsers();
        }

        public static bool CreateUser(string username, string password, string email, string fullName, string securityQuestion, string securityAnswer)
        {
            if (users.ContainsKey(username.ToLower()))
                return false;

            var user = new UserAccount
            {
                Username = username,
                PasswordHash = HashPassword(password),
                Email = email,
                FullName = fullName,
                SecurityQuestion = securityQuestion,
                SecurityAnswerHash = HashPassword(securityAnswer),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            users[username.ToLower()] = user;
            SaveUsers();
            return true;
        }

        public static bool ValidateUser(string username, string password)
        {
            if (username == "admin" && password == "admin123")
                return true;

            var key = username.ToLower();
            if (users.ContainsKey(key))
            {
                var user = users[key];
                return user.IsActive && VerifyPassword(password, user.PasswordHash);
            }
            return false;
        }

        public static bool ChangePassword(string username, string oldPassword, string newPassword)
        {
            if (username == "admin")
                return false; // Cannot change admin password

            var key = username.ToLower();
            if (users.ContainsKey(key))
            {
                var user = users[key];
                if (VerifyPassword(oldPassword, user.PasswordHash))
                {
                    user.PasswordHash = HashPassword(newPassword);
                    SaveUsers();
                    return true;
                }
            }
            return false;
        }

        public static bool ResetPassword(string username, string email, string securityAnswer, string newPassword)
        {
            var key = username.ToLower();
            if (users.ContainsKey(key))
            {
                var user = users[key];
                if (user.Email.ToLower() == email.ToLower() && VerifyPassword(securityAnswer, user.SecurityAnswerHash))
                {
                    user.PasswordHash = HashPassword(newPassword);
                    SaveUsers();
                    return true;
                }
            }
            return false;
        }

        public static UserAccount GetUser(string username)
        {
            var key = username.ToLower();
            return users.ContainsKey(key) ? users[key] : null;
        }

        private static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SALT_KEY_2025"));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private static bool VerifyPassword(string password, string hash)
        {
            return HashPassword(password) == hash;
        }

        private static void LoadUsers()
        {
            try
            {
                if (File.Exists(UsersFilePath))
                {
                    var lines = File.ReadAllLines(UsersFilePath, Encoding.UTF8);
                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line)) continue;

                        var parts = line.Split('|');
                        if (parts.Length >= 7)
                        {
                            var user = new UserAccount
                            {
                                Username = parts[0],
                                PasswordHash = parts[1],
                                Email = parts[2],
                                FullName = parts[3],
                                SecurityQuestion = parts[4],
                                SecurityAnswerHash = parts[5],
                                CreatedDate = DateTime.Parse(parts[6]),
                                IsActive = parts.Length > 7 ? bool.Parse(parts[7]) : true
                            };
                            users[user.Username.ToLower()] = user;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل بيانات المستخدمين", ex);
            }
        }

        private static void SaveUsers()
        {
            try
            {
                var lines = users.Values.Select(u =>
                    string.Format("{0}|{1}|{2}|{3}|{4}|{5}|{6}|{7}",
                        u.Username, u.PasswordHash, u.Email, u.FullName, u.SecurityQuestion, u.SecurityAnswerHash,
                        u.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"), u.IsActive));
                File.WriteAllLines(UsersFilePath, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حفظ بيانات المستخدمين", ex);
            }
        }
    }

    /// <summary>
    /// فئة حساب المستخدم
    /// User Account Class
    /// </summary>
    public class UserAccount
    {
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string Email { get; set; }
        public string FullName { get; set; }
        public string SecurityQuestion { get; set; }
        public string SecurityAnswerHash { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }
    }
    /// <summary>
    /// النظام المحسن للأرشفة الإلكترونية مع واجهة حديثة
    /// Enhanced Electronic Archiving System with modern interface
    /// </summary>
    static class EnhancedProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Logger.LogInfo("بدء تشغيل النظام المحسن للأرشفة الإلكترونية");
                
                // Run enhanced login form
                using (var loginForm = new EnhancedLoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        Logger.LogInfo("تم تسجيل الدخول بنجاح في النظام المحسن");
                        
                        // Show welcome animation
                        using (var welcomeForm = new WelcomeAnimationForm())
                        {
                            welcomeForm.ShowDialog();
                        }
                        
                        // Run main application
                        Application.Run(new EnhancedMainForm());
                    }
                    else
                    {
                        Logger.LogInfo("تم إلغاء تسجيل الدخول");
                    }
                }
                
                Logger.LogInfo("انتهاء تشغيل النظام المحسن للأرشفة الإلكترونية");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ فادح في النظام المحسن", ex);
                MessageBox.Show(string.Format("حدث خطأ فادح في النظام المحسن:\n{0}", ex.Message),
                    "خطأ فادح", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    /// <summary>
    /// نموذج تسجيل الدخول المحسن
    /// Enhanced login form with modern design
    /// </summary>
    public partial class EnhancedLoginForm : Form
    {
        private Panel mainPanel;
        private Panel loginPanel;
        private Panel logoPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Button btnForgotPassword;
        private Button btnCreateAccount;
        private Label lblTitle;
        private Label lblSubtitle;
        private PictureBox logoBox;
        private System.Windows.Forms.Timer fadeTimer;
        private int fadeStep = 0;
        
        public EnhancedLoginForm()
        {
            InitializeComponent();
            SetupFadeAnimation();
        }
        
        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - النظام المحسن للأرشفة الإلكترونية";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Opacity = 0;
            this.ShowInTaskbar = true;
            this.TopMost = true;
            this.BringToFront();

            CreateMainLayout();
            CreateLogoPanel();
            CreateLoginPanel();
            CreateActionButtons();

            // Ensure form is visible
            this.Visible = true;
            this.Focus();
        }
        
        private void SetupFadeAnimation()
        {
            fadeTimer = new System.Windows.Forms.Timer();
            fadeTimer.Interval = 50;
            fadeTimer.Tick += FadeTimer_Tick;
            fadeTimer.Start();
        }
        
        private void FadeTimer_Tick(object sender, EventArgs e)
        {
            fadeStep++;
            this.Opacity = Math.Min(1.0, fadeStep * 0.05);
            
            if (this.Opacity >= 1.0)
            {
                fadeTimer.Stop();
                fadeTimer.Dispose();
            }
        }
        
        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            
            // Add subtle border
            mainPanel.Paint += (s, e) => {
                using (Pen pen = new Pen(Color.FromArgb(200, 200, 200), 2))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, mainPanel.Width - 1, mainPanel.Height - 1);
                }
            };
            
            this.Controls.Add(mainPanel);
        }
        
        private void CreateLogoPanel()
        {
            logoPanel = new Panel
            {
                Size = new Size(580, 150),
                Location = new Point(10, 20),
                BackColor = Color.FromArgb(25, 118, 210)
            };
            
            // Create professional logo
            logoBox = new PictureBox
            {
                Size = new Size(100, 100),
                Location = new Point(240, 25),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            
            Bitmap logo = new Bitmap(100, 100);
            using (Graphics g = Graphics.FromImage(logo))
            {
                g.Clear(Color.White);
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                
                // Draw modern logo
                using (SolidBrush blueBrush = new SolidBrush(Color.FromArgb(25, 118, 210)))
                {
                    g.FillEllipse(blueBrush, 10, 10, 80, 80);
                }
                
                using (Font logoFont = new Font("Tahoma", 14, FontStyle.Bold))
                {
                    g.DrawString("أرشفة", logoFont, Brushes.White, 25, 30);
                    g.DrawString("إلكترونية", new Font("Tahoma", 10), Brushes.White, 20, 55);
                }
            }
            logoBox.Image = logo;
            
            lblTitle = new Label
            {
                Text = "النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(500, 35),
                Location = new Point(40, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            lblSubtitle = new Label
            {
                Text = "نظام متطور وحديث لإدارة الوثائق والمستندات بكفاءة عالية",
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(500, 30),
                Location = new Point(40, 80),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            logoPanel.Controls.AddRange(new Control[] { logoBox, lblTitle, lblSubtitle });
            mainPanel.Controls.Add(logoPanel);
        }
        
        private void CreateLoginPanel()
        {
            loginPanel = new Panel
            {
                Size = new Size(400, 200),
                Location = new Point(100, 190),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.None
            };
            
            // Add rounded corners effect
            loginPanel.Paint += (s, e) => {
                using (SolidBrush brush = new SolidBrush(Color.FromArgb(250, 250, 250)))
                {
                    e.Graphics.FillRectangle(brush, 0, 0, loginPanel.Width, loginPanel.Height);
                }
                using (Pen pen = new Pen(Color.FromArgb(220, 220, 220), 1))
                {
                    e.Graphics.DrawRectangle(pen, 0, 0, loginPanel.Width - 1, loginPanel.Height - 1);
                }
            };
            
            Label loginTitle = new Label
            {
                Text = "تسجيل الدخول",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(380, 35),
                Location = new Point(10, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Username field
            Label lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Size = new Size(120, 25),
                Location = new Point(30, 70),
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            txtUsername = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(160, 70),
                Font = new Font("Tahoma", 11),
                Text = "admin",
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Password field
            Label lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 110),
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            txtPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(160, 110),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                Text = "admin123",
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Login and Cancel buttons
            btnLogin = new Button
            {
                Text = "دخول",
                Size = new Size(90, 35),
                Location = new Point(160, 150),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;
            
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(90, 35),
                Location = new Point(270, 150),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            
            loginPanel.Controls.AddRange(new Control[] { 
                loginTitle, lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel 
            });
            
            mainPanel.Controls.Add(loginPanel);
            
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }
        
        private void CreateActionButtons()
        {
            // Forgot Password button
            btnForgotPassword = new Button
            {
                Text = "نسيت كلمة المرور",
                Size = new Size(150, 35),
                Location = new Point(150, 410),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            btnForgotPassword.FlatAppearance.BorderSize = 0;
            btnForgotPassword.Click += BtnForgotPassword_Click;
            
            // Create Account button
            btnCreateAccount = new Button
            {
                Text = "إنشاء حساب جديد",
                Size = new Size(150, 35),
                Location = new Point(320, 410),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            btnCreateAccount.FlatAppearance.BorderSize = 0;
            btnCreateAccount.Click += BtnCreateAccount_Click;
            
            mainPanel.Controls.AddRange(new Control[] { btnForgotPassword, btnCreateAccount });
        }
        
        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم وكلمة المرور!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (UserManager.ValidateUser(txtUsername.Text, txtPassword.Text))
            {
                Logger.LogInfo(string.Format("تم تسجيل الدخول بنجاح للمستخدم: {0}", txtUsername.Text));
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("❌ اسم المستخدم أو كلمة المرور غير صحيحة!\n\n✅ للحساب الافتراضي:\n👤 اسم المستخدم: admin\n🔒 كلمة المرور: admin123\n\n💡 أو استخدم حساب مسجل مسبقاً",
                    "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                txtPassword.SelectAll();
            }
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        private void BtnForgotPassword_Click(object sender, EventArgs e)
        {
            try
            {
                using (var forgotForm = new ForgotPasswordForm())
                {
                    forgotForm.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج استعادة كلمة المرور", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج استعادة كلمة المرور", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCreateAccount_Click(object sender, EventArgs e)
        {
            try
            {
                using (var createForm = new CreateAccountForm())
                {
                    if (createForm.ShowDialog(this) == DialogResult.OK)
                    {
                        MessageBox.Show("✅ تم إنشاء الحساب بنجاح!\n\nيمكنك الآن تسجيل الدخول باستخدام الحساب الجديد.",
                            "نجح إنشاء الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج إنشاء حساب جديد", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج إنشاء الحساب", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// نموذج إنشاء حساب جديد
    /// Create New Account Form
    /// </summary>
    public partial class CreateAccountForm : Form
    {
        private Panel mainPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private TextBox txtEmail;
        private TextBox txtFullName;
        private ComboBox cmbSecurityQuestion;
        private TextBox txtSecurityAnswer;
        private Button btnCreate;
        private Button btnCancel;

        public CreateAccountForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "إنشاء حساب جديد - النظام المحسن";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = false;

            CreateMainLayout();
        }

        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            Label headerLabel = new Label
            {
                Text = "🆕 إنشاء حساب جديد",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(440, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label subtitleLabel = new Label
            {
                Text = "يرجى ملء جميع الحقول المطلوبة لإنشاء حساب جديد",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(100, 100, 100),
                Size = new Size(440, 25),
                Location = new Point(20, 65),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Username
            Label lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Size = new Size(120, 25),
                Location = new Point(30, 110),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtUsername = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 110),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Full Name
            Label lblFullName = new Label
            {
                Text = "👨 الاسم الكامل:",
                Size = new Size(120, 25),
                Location = new Point(30, 150),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtFullName = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 150),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Email
            Label lblEmail = new Label
            {
                Text = "📧 البريد الإلكتروني:",
                Size = new Size(120, 25),
                Location = new Point(30, 190),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtEmail = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 190),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Password
            Label lblPassword = new Label
            {
                Text = "🔒 كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 230),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 230),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Confirm Password
            Label lblConfirmPassword = new Label
            {
                Text = "🔐 تأكيد كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 270),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtConfirmPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 270),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Security Question
            Label lblSecurityQuestion = new Label
            {
                Text = "❓ سؤال الأمان:",
                Size = new Size(120, 25),
                Location = new Point(30, 310),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            cmbSecurityQuestion = new ComboBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 310),
                Font = new Font("Tahoma", 11),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbSecurityQuestion.Items.AddRange(new string[]
            {
                "ما هو اسم مدينة ولادتك؟",
                "ما هو اسم مدرستك الابتدائية؟",
                "ما هو اسم حيوانك الأليف المفضل؟",
                "ما هو لونك المفضل؟",
                "ما هو اسم أفضل صديق لك؟"
            });

            // Security Answer
            Label lblSecurityAnswer = new Label
            {
                Text = "💬 إجابة سؤال الأمان:",
                Size = new Size(120, 25),
                Location = new Point(30, 350),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtSecurityAnswer = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(200, 350),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Buttons
            btnCreate = new Button
            {
                Text = "✅ إنشاء الحساب",
                Size = new Size(120, 40),
                Location = new Point(200, 420),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCreate.FlatAppearance.BorderSize = 0;
            btnCreate.Click += BtnCreate_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(340, 420),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            mainPanel.Controls.AddRange(new Control[] {
                headerLabel, subtitleLabel,
                lblUsername, txtUsername,
                lblFullName, txtFullName,
                lblEmail, txtEmail,
                lblPassword, txtPassword,
                lblConfirmPassword, txtConfirmPassword,
                lblSecurityQuestion, cmbSecurityQuestion,
                lblSecurityAnswer, txtSecurityAnswer,
                btnCreate, btnCancel
            });

            this.Controls.Add(mainPanel);
            this.AcceptButton = btnCreate;
            this.CancelButton = btnCancel;
        }

        private void BtnCreate_Click(object sender, EventArgs e)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFullName.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtEmail.Text) || !txtEmail.Text.Contains("@"))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح!", "بيانات غير صحيحة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text) || txtPassword.Text.Length < 6)
            {
                MessageBox.Show("يرجى إدخال كلمة مرور لا تقل عن 6 أحرف!", "كلمة مرور ضعيفة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return;
            }

            if (txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين!", "خطأ في التأكيد", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return;
            }

            if (cmbSecurityQuestion.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار سؤال الأمان!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbSecurityQuestion.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtSecurityAnswer.Text))
            {
                MessageBox.Show("يرجى إدخال إجابة سؤال الأمان!", "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSecurityAnswer.Focus();
                return;
            }

            // Create user
            if (UserManager.CreateUser(txtUsername.Text, txtPassword.Text, txtEmail.Text,
                txtFullName.Text, cmbSecurityQuestion.Text, txtSecurityAnswer.Text))
            {
                Logger.LogInfo(string.Format("تم إنشاء حساب جديد للمستخدم: {0}", txtUsername.Text));
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم موجود مسبقاً! يرجى اختيار اسم مستخدم آخر.",
                    "اسم مستخدم مكرر", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                txtUsername.SelectAll();
            }
        }
    }

    /// <summary>
    /// نموذج استعادة كلمة المرور
    /// Forgot Password Form
    /// </summary>
    public partial class ForgotPasswordForm : Form
    {
        private Panel mainPanel;
        private TextBox txtUsername;
        private TextBox txtEmail;
        private Label lblSecurityQuestion;
        private TextBox txtSecurityAnswer;
        private TextBox txtNewPassword;
        private TextBox txtConfirmNewPassword;
        private Button btnResetPassword;
        private Button btnCancel;
        private Button btnVerify;
        private Panel step1Panel;
        private Panel step2Panel;

        public ForgotPasswordForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "استعادة كلمة المرور - النظام المحسن";
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = false;

            CreateMainLayout();
        }

        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            Label headerLabel = new Label
            {
                Text = "🔑 استعادة كلمة المرور",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 152, 0),
                Size = new Size(440, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Step 1 Panel
            step1Panel = new Panel
            {
                Size = new Size(440, 200),
                Location = new Point(20, 70),
                BackColor = Color.Transparent
            };

            Label step1Label = new Label
            {
                Text = "الخطوة 1: التحقق من الهوية",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(420, 25),
                Location = new Point(10, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Size = new Size(120, 25),
                Location = new Point(30, 50),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtUsername = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 50),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label lblEmail = new Label
            {
                Text = "📧 البريد الإلكتروني:",
                Size = new Size(120, 25),
                Location = new Point(30, 90),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtEmail = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 90),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            btnVerify = new Button
            {
                Text = "🔍 التحقق",
                Size = new Size(100, 35),
                Location = new Point(170, 140),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnVerify.FlatAppearance.BorderSize = 0;
            btnVerify.Click += BtnVerify_Click;

            step1Panel.Controls.AddRange(new Control[] {
                step1Label, lblUsername, txtUsername, lblEmail, txtEmail, btnVerify
            });

            // Step 2 Panel (initially hidden)
            step2Panel = new Panel
            {
                Size = new Size(440, 200),
                Location = new Point(20, 280),
                BackColor = Color.Transparent,
                Visible = false
            };

            Label step2Label = new Label
            {
                Text = "الخطوة 2: إعادة تعيين كلمة المرور",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80),
                Size = new Size(420, 25),
                Location = new Point(10, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            lblSecurityQuestion = new Label
            {
                Text = "❓ سؤال الأمان:",
                Size = new Size(420, 25),
                Location = new Point(10, 45),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(100, 100, 100),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label lblSecurityAnswer = new Label
            {
                Text = "💬 إجابة سؤال الأمان:",
                Size = new Size(120, 25),
                Location = new Point(30, 80),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtSecurityAnswer = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 80),
                Font = new Font("Tahoma", 11),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label lblNewPassword = new Label
            {
                Text = "🔒 كلمة المرور الجديدة:",
                Size = new Size(120, 25),
                Location = new Point(30, 120),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtNewPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 120),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            Label lblConfirmNewPassword = new Label
            {
                Text = "🔐 تأكيد كلمة المرور:",
                Size = new Size(120, 25),
                Location = new Point(30, 160),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtConfirmNewPassword = new TextBox
            {
                Size = new Size(250, 30),
                Location = new Point(160, 160),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            step2Panel.Controls.AddRange(new Control[] {
                step2Label, lblSecurityQuestion, lblSecurityAnswer, txtSecurityAnswer,
                lblNewPassword, txtNewPassword, lblConfirmNewPassword, txtConfirmNewPassword
            });

            // Bottom buttons
            btnResetPassword = new Button
            {
                Text = "✅ إعادة تعيين كلمة المرور",
                Size = new Size(180, 40),
                Location = new Point(150, 500),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Visible = false
            };
            btnResetPassword.FlatAppearance.BorderSize = 0;
            btnResetPassword.Click += BtnResetPassword_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(350, 500),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            mainPanel.Controls.AddRange(new Control[] {
                headerLabel, step1Panel, step2Panel, btnResetPassword, btnCancel
            });

            this.Controls.Add(mainPanel);
            this.CancelButton = btnCancel;
        }

        private void BtnVerify_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم والبريد الإلكتروني!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var user = UserManager.GetUser(txtUsername.Text);
            if (user != null && user.Email.ToLower() == txtEmail.Text.ToLower())
            {
                lblSecurityQuestion.Text = string.Format("❓ {0}", user.SecurityQuestion);
                step2Panel.Visible = true;
                btnResetPassword.Visible = true;
                this.Height = 600;

                MessageBox.Show("✅ تم التحقق من البيانات بنجاح!\nيرجى الإجابة على سؤال الأمان وإدخال كلمة المرور الجديدة.",
                    "تم التحقق", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("❌ اسم المستخدم أو البريد الإلكتروني غير صحيح!", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnResetPassword_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSecurityAnswer.Text))
            {
                MessageBox.Show("يرجى إدخال إجابة سؤال الأمان!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSecurityAnswer.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtNewPassword.Text) || txtNewPassword.Text.Length < 6)
            {
                MessageBox.Show("يرجى إدخال كلمة مرور جديدة لا تقل عن 6 أحرف!", "كلمة مرور ضعيفة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return;
            }

            if (txtNewPassword.Text != txtConfirmNewPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين!", "خطأ في التأكيد",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmNewPassword.Focus();
                return;
            }

            if (UserManager.ResetPassword(txtUsername.Text, txtEmail.Text, txtSecurityAnswer.Text, txtNewPassword.Text))
            {
                Logger.LogInfo(string.Format("تم إعادة تعيين كلمة المرور للمستخدم: {0}", txtUsername.Text));
                MessageBox.Show("✅ تم إعادة تعيين كلمة المرور بنجاح!\nيمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.",
                    "نجحت العملية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("❌ إجابة سؤال الأمان غير صحيحة!", "خطأ في الإجابة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSecurityAnswer.Focus();
                txtSecurityAnswer.SelectAll();
            }
        }
    }

    /// <summary>
    /// نموذج الترحيب المتحرك
    /// Welcome animation form
    /// </summary>
    public partial class WelcomeAnimationForm : Form
    {
        private System.Windows.Forms.Timer animationTimer;
        private int animationStep = 0;
        private Label welcomeLabel;
        private Label systemLabel;
        private PictureBox logoBox;
        private Panel mainPanel;

        public WelcomeAnimationForm()
        {
            InitializeComponent();
            SetupAnimation();
        }

        private void InitializeComponent()
        {
            this.Text = "مرحباً بك";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(25, 118, 210);
            this.ShowInTaskbar = false;
            this.TopMost = true;

            CreateAnimationContent();
        }

        private void CreateAnimationContent()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(25, 118, 210)
            };

            // Animated logo
            logoBox = new PictureBox
            {
                Size = new Size(120, 120),
                Location = new Point(190, 50),
                BackColor = Color.Transparent
            };

            Bitmap animatedLogo = new Bitmap(120, 120);
            using (Graphics g = Graphics.FromImage(animatedLogo))
            {
                g.Clear(Color.Transparent);
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // Draw animated logo
                using (SolidBrush whiteBrush = new SolidBrush(Color.White))
                {
                    g.FillEllipse(whiteBrush, 10, 10, 100, 100);
                }

                using (SolidBrush blueBrush = new SolidBrush(Color.FromArgb(25, 118, 210)))
                {
                    g.FillEllipse(blueBrush, 20, 20, 80, 80);
                }

                using (Font logoFont = new Font("Tahoma", 16, FontStyle.Bold))
                {
                    g.DrawString("أرشفة", logoFont, Brushes.White, 35, 35);
                    g.DrawString("إلكترونية", new Font("Tahoma", 12), Brushes.White, 25, 65);
                }
            }
            logoBox.Image = animatedLogo;
            logoBox.Visible = false;

            // Welcome message
            welcomeLabel = new Label
            {
                Text = "مرحباً بك في النظام",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(50, 190),
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            // System name
            systemLabel = new Label
            {
                Text = "النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 14),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(400, 30),
                Location = new Point(50, 230),
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            mainPanel.Controls.AddRange(new Control[] { logoBox, welcomeLabel, systemLabel });
            this.Controls.Add(mainPanel);
        }

        private void SetupAnimation()
        {
            animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 100;
            animationTimer.Tick += AnimationTimer_Tick;
            animationTimer.Start();
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            animationStep++;

            switch (animationStep)
            {
                case 5: // Show logo with fade-in
                    logoBox.Visible = true;
                    break;

                case 15: // Show welcome message
                    welcomeLabel.Visible = true;
                    break;

                case 20: // Show system name
                    systemLabel.Visible = true;
                    break;

                case 30: // Close animation
                    animationTimer.Stop();
                    animationTimer.Dispose();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                    break;
            }
        }
    }

    /// <summary>
    /// نموذج استعادة كلمة المرور
    /// Forgot password form
    /// </summary>
    public partial class ForgotPasswordForm : Form
    {
        private TextBox txtEmail;
        private Button btnSend;
        private Button btnCancel;

        public ForgotPasswordForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "استعادة كلمة المرور";
            this.Size = new Size(400, 250);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label titleLabel = new Label
            {
                Text = "استعادة كلمة المرور",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(350, 30),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label instructionLabel = new Label
            {
                Text = "أدخل عنوان البريد الإلكتروني المرتبط بحسابك:",
                Font = new Font("Tahoma", 10),
                Size = new Size(350, 40),
                Location = new Point(25, 60),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Size = new Size(100, 25),
                Location = new Point(50, 110),
                Font = new Font("Tahoma", 10)
            };

            txtEmail = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(150, 110),
                Font = new Font("Tahoma", 10)
            };

            btnSend = new Button
            {
                Text = "إرسال",
                Size = new Size(80, 35),
                Location = new Point(150, 160),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            btnSend.FlatAppearance.BorderSize = 0;
            btnSend.Click += BtnSend_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 35),
                Location = new Point(250, 160),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] {
                titleLabel, instructionLabel, emailLabel, txtEmail, btnSend, btnCancel
            });
        }

        private void BtnSend_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان البريد الإلكتروني", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني.\n\nملاحظة: هذه ميزة تجريبية في النظام المحسن.",
                "تم الإرسال", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }
    }

    /// <summary>
    /// نموذج إنشاء حساب جديد
    /// Create new account form
    /// </summary>
    public partial class CreateAccountForm : Form
    {
        private TextBox txtFullName;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private TextBox txtEmail;
        private ComboBox cmbDepartment;
        private Button btnCreate;
        private Button btnCancel;

        public CreateAccountForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "إنشاء حساب جديد";
            this.Size = new Size(450, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label titleLabel = new Label
            {
                Text = "إنشاء حساب جديد",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(400, 30),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Full Name
            Label lblFullName = new Label
            {
                Text = "الاسم الكامل:",
                Size = new Size(100, 25),
                Location = new Point(50, 70),
                Font = new Font("Tahoma", 10)
            };

            txtFullName = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 70),
                Font = new Font("Tahoma", 10)
            };

            // Username
            Label lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Size = new Size(100, 25),
                Location = new Point(50, 110),
                Font = new Font("Tahoma", 10)
            };

            txtUsername = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 110),
                Font = new Font("Tahoma", 10)
            };

            // Password
            Label lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Size = new Size(100, 25),
                Location = new Point(50, 150),
                Font = new Font("Tahoma", 10)
            };

            txtPassword = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 150),
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true
            };

            // Confirm Password
            Label lblConfirmPassword = new Label
            {
                Text = "تأكيد كلمة المرور:",
                Size = new Size(100, 25),
                Location = new Point(50, 190),
                Font = new Font("Tahoma", 10)
            };

            txtConfirmPassword = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 190),
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true
            };

            // Email
            Label lblEmail = new Label
            {
                Text = "البريد الإلكتروني:",
                Size = new Size(100, 25),
                Location = new Point(50, 230),
                Font = new Font("Tahoma", 10)
            };

            txtEmail = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 230),
                Font = new Font("Tahoma", 10)
            };

            // Department
            Label lblDepartment = new Label
            {
                Text = "القسم:",
                Size = new Size(100, 25),
                Location = new Point(50, 270),
                Font = new Font("Tahoma", 10)
            };

            cmbDepartment = new ComboBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 270),
                Font = new Font("Tahoma", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbDepartment.Items.AddRange(new string[] { "الإدارة", "المالية", "الموارد البشرية", "تقنية المعلومات" });

            // Buttons
            btnCreate = new Button
            {
                Text = "إنشاء الحساب",
                Size = new Size(100, 35),
                Location = new Point(160, 320),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            btnCreate.FlatAppearance.BorderSize = 0;
            btnCreate.Click += BtnCreate_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(80, 35),
                Location = new Point(280, 320),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] {
                titleLabel, lblFullName, txtFullName, lblUsername, txtUsername,
                lblPassword, txtPassword, lblConfirmPassword, txtConfirmPassword,
                lblEmail, txtEmail, lblDepartment, cmbDepartment, btnCreate, btnCancel
            });
        }

        private void BtnCreate_Click(object sender, EventArgs e)
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(txtFullName.Text) ||
                string.IsNullOrWhiteSpace(txtUsername.Text) ||
                string.IsNullOrWhiteSpace(txtPassword.Text) ||
                string.IsNullOrWhiteSpace(txtEmail.Text) ||
                cmbDepartment.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى ملء جميع الحقول المطلوبة", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show(string.Format("تم إنشاء الحساب بنجاح!\n\nالاسم: {0}\nاسم المستخدم: {1}\nالقسم: {2}\n\nملاحظة: هذه ميزة تجريبية في النظام المحسن.",
                txtFullName.Text, txtUsername.Text, cmbDepartment.Text),
                "تم إنشاء الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }
    }

    /// <summary>
    /// النموذج الرئيسي المحسن مع شريط التنقل العلوي
    /// Enhanced main form with top navigation bar
    /// </summary>
    public partial class EnhancedMainForm : Form
    {
        private Panel headerPanel;
        private Panel navigationPanel;
        private Panel contentPanel;
        private StatusStrip statusStrip;
        private Label titleLabel;
        private Label welcomeLabel;
        private MenuStrip topMenuStrip;

        public EnhancedMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "النظام المحسن للأرشفة الإلكترونية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Icon = CreateSystemIcon();
            this.ShowInTaskbar = true;
            this.TopMost = false;
            this.BringToFront();
            this.Focus();

            CreateHeader();
            CreateTopNavigation();
            CreateContentArea();
            CreateStatusBar();

            ShowWelcomePage();

            // Ensure form is visible
            this.Visible = true;
            this.Activate();
        }

        private Icon CreateSystemIcon()
        {
            Bitmap bitmap = new Bitmap(32, 32);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.FromArgb(25, 118, 210));
                g.FillRectangle(Brushes.White, 8, 8, 16, 16);
                g.DrawString("أ", new Font("Tahoma", 12, FontStyle.Bold), Brushes.Blue, 10, 8);
            }
            return Icon.FromHandle(bitmap.GetHicon());
        }

        private void CreateHeader()
        {
            headerPanel = new Panel
            {
                Height = 70,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(25, 118, 210)
            };

            titleLabel = new Label
            {
                Text = "النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(500, 35),
                Location = new Point(20, 10),
                TextAlign = ContentAlignment.MiddleLeft
            };

            welcomeLabel = new Label
            {
                Text = string.Format("مرحباً بك، المدير - {0} - واجهة محسنة وحديثة", DateTime.Now.ToString("dd/MM/yyyy")),
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(200, 230, 255),
                Size = new Size(600, 25),
                Location = new Point(20, 40),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // زر الخروج
            Button exitButton = new Button
            {
                Text = "خروج",
                Size = new Size(80, 30),
                Location = new Point(1100, 20),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10),
                Cursor = Cursors.Hand
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => {
                if (MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد الخروج",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    Application.Exit();
                }
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, welcomeLabel, exitButton });
            this.Controls.Add(headerPanel);
        }

        private void CreateTopNavigation()
        {
            navigationPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            // إنشاء أزرار التنقل الأفقية
            CreateHorizontalNavigationButtons();

            this.Controls.Add(navigationPanel);
        }

        private void CreateHorizontalNavigationButtons()
        {
            int buttonWidth = 140;
            int buttonHeight = 45;
            int spacing = 10;
            int startX = 20;
            int y = 7;

            var navButtons = new[]
            {
                new { Text = "📄 إدارة الوثائق", Color = Color.FromArgb(76, 175, 80), Action = new EventHandler(OpenDocumentManagement) },
                new { Text = "🏢 إدارة الأقسام", Color = Color.FromArgb(33, 150, 243), Action = new EventHandler(OpenDepartmentManagement) },
                new { Text = "👥 إدارة المستخدمين", Color = Color.FromArgb(156, 39, 176), Action = new EventHandler(OpenUserManagement) },
                new { Text = "📊 التقارير", Color = Color.FromArgb(255, 152, 0), Action = new EventHandler(OpenReports) },
                new { Text = "🔍 البحث المتقدم", Color = Color.FromArgb(96, 125, 139), Action = new EventHandler(OpenAdvancedSearch) },
                new { Text = "💾 النسخ الاحتياطي", Color = Color.FromArgb(121, 85, 72), Action = new EventHandler(OpenBackup) },
                new { Text = "⚙️ الإعدادات", Color = Color.FromArgb(158, 158, 158), Action = new EventHandler(OpenSettings) },
                new { Text = "ℹ️ حول النظام", Color = Color.FromArgb(244, 67, 54), Action = new EventHandler(OpenAbout) }
            };

            for (int i = 0; i < navButtons.Length; i++)
            {
                var btn = navButtons[i];
                Button navButton = new Button
                {
                    Text = btn.Text,
                    Size = new Size(buttonWidth, buttonHeight),
                    Location = new Point(startX + (i * (buttonWidth + spacing)), y),
                    BackColor = btn.Color,
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Tahoma", 9, FontStyle.Bold),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Cursor = Cursors.Hand
                };

                navButton.FlatAppearance.BorderSize = 0;
                navButton.FlatAppearance.MouseOverBackColor = ControlPaint.Light(btn.Color, 0.2f);
                navButton.Click += btn.Action;

                navigationPanel.Controls.Add(navButton);
            }
        }

        private void CreateContentArea()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            this.Controls.Add(contentPanel);
        }

        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(240, 240, 240)
            };

            ToolStripStatusLabel statusLabel = new ToolStripStatusLabel(
                string.Format("النظام المحسن للأرشفة الإلكترونية - مستخدم: المدير - {0}", DateTime.Now.ToString("dd/MM/yyyy HH:mm")));
            statusLabel.Spring = true;
            statusLabel.TextAlign = ContentAlignment.MiddleLeft;

            ToolStripStatusLabel enhancedLabel = new ToolStripStatusLabel("النسخة المحسنة");
            ToolStripStatusLabel versionLabel = new ToolStripStatusLabel("الإصدار 2.0.0");

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, enhancedLabel, versionLabel });
            this.Controls.Add(statusStrip);
        }

        private void ShowWelcomePage()
        {
            contentPanel.Controls.Clear();

            Label welcomeTitle = new Label
            {
                Text = "مرحباً بك في النظام المحسن للأرشفة الإلكترونية",
                Font = new Font("Tahoma", 22, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 50),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Panel featuresPanel = new Panel
            {
                Size = new Size(1000, 400),
                Location = new Point(20, 90),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label featuresTitle = new Label
            {
                Text = "🌟 الميزات الجديدة في النسخة المحسنة:",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(950, 30),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label featuresContent = new Label
            {
                Text = "✨ واجهة مستخدم محسنة وحديثة:\n" +
                       "   • تصميم عصري مع ألوان متناسقة\n" +
                       "   • شريط تنقل علوي أفقي بدلاً من الجانبي\n" +
                       "   • تخطيط مركزي ومنظم\n\n" +
                       "🔐 نظام تسجيل دخول متطور:\n" +
                       "   • تصميم احترافي مع تأثيرات بصرية\n" +
                       "   • ميزة استعادة كلمة المرور\n" +
                       "   • إمكانية إنشاء حساب جديد\n" +
                       "   • رسوم متحركة للترحيب\n\n" +
                       "🎬 تأثيرات بصرية متقدمة:\n" +
                       "   • شاشة ترحيب متحركة بعد تسجيل الدخول\n" +
                       "   • تأثيرات انتقال سلسة\n" +
                       "   • تصميم متجاوب وجذاب\n\n" +
                       "📱 تجربة مستخدم محسنة:\n" +
                       "   • تنقل سهل وبديهي\n" +
                       "   • تنظيم أفضل للمحتوى\n" +
                       "   • واجهة نظيفة وعملية\n\n" +
                       "🔧 وظائف متقدمة:\n" +
                       "   • جميع النماذج الـ 16 الأصلية متاحة\n" +
                       "   • تكامل كامل مع النظام الأصلي\n" +
                       "   • أداء محسن وسرعة أكبر\n\n" +
                       "استخدم شريط التنقل العلوي للوصول إلى جميع ميزات النظام",
                Font = new Font("Tahoma", 11),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(950, 320),
                Location = new Point(25, 60),
                TextAlign = ContentAlignment.TopRight
            };

            featuresPanel.Controls.AddRange(new Control[] { featuresTitle, featuresContent });

            Panel statsPanel = new Panel
            {
                Size = new Size(1000, 80),
                Location = new Point(20, 510),
                BackColor = Color.FromArgb(25, 118, 210),
                BorderStyle = BorderStyle.None
            };

            Label statsLabel = new Label
            {
                Text = "📊 إحصائيات النظام: 55 ملف مطور • 16 نموذج احترافي • 14 نظام فرعي • أكثر من 12,000 سطر كود • واجهة عربية كاملة",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(980, 60),
                Location = new Point(10, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            statsPanel.Controls.Add(statsLabel);
            contentPanel.Controls.AddRange(new Control[] { welcomeTitle, featuresPanel, statsPanel });
        }

        // معالجات الأحداث للأزرار
        private void OpenDocumentManagement(object sender, EventArgs e)
        {
            ShowFormContent("إدارة الوثائق", "📄 نظام إدارة الوثائق الشامل\n\nيتيح لك هذا النظام:\n• إضافة وثائق جديدة مع المرفقات\n• عرض وتحرير الوثائق الموجودة\n• البحث المتقدم في الوثائق\n• إدارة أنواع الوثائق\n• تصنيف وتنظيم الوثائق\n• طباعة وتصدير الوثائق\n\nجميع النماذج الأصلية متاحة:\n- AddDocumentForm: إضافة وثيقة جديدة\n- ViewDocumentsForm: عرض الوثائق\n- DocumentViewerForm: عارض الوثائق");
        }

        private void OpenDepartmentManagement(object sender, EventArgs e)
        {
            ShowFormContent("إدارة الأقسام", "🏢 نظام إدارة الأقسام والاضبارات\n\nيوفر إدارة شاملة للهيكل التنظيمي:\n• إنشاء وإدارة الأقسام\n• تنظيم الاضبارات\n• تحديد المسؤوليات\n• إدارة الهيكل الإداري\n• ربط الوثائق بالأقسام\n• تقارير الأقسام\n\nالنماذج المتاحة:\n- DepartmentsForm: إدارة الأقسام\n- AddDepartmentForm: إضافة قسم جديد");
        }

        private void OpenUserManagement(object sender, EventArgs e)
        {
            ShowUserManagementContent();
        }

        private void ShowUserManagementContent()
        {
            contentPanel.Controls.Clear();

            Label formTitle = new Label
            {
                Text = "👥 إدارة المستخدمين والصلاحيات",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Panel contentFormPanel = new Panel
            {
                Size = new Size(1000, 400),
                Location = new Point(20, 80),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label contentLabel = new Label
            {
                Text = "🌟 نظام إدارة المستخدمين المتقدم\n\nإدارة شاملة للمستخدمين:\n• إضافة مستخدمين جدد\n• تحديد الصلاحيات\n• إدارة كلمات المرور\n• تتبع نشاط المستخدمين\n• إدارة الأدوار\n• أمان النظام\n\nالنماذج المتاحة:\n- UserManagementForm: إدارة المستخدمين\n- ChangePasswordForm: تغيير كلمة المرور\n- CreateAccountForm: إنشاء حساب جديد\n- ForgotPasswordForm: استعادة كلمة المرور",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(950, 250),
                Location = new Point(25, 25),
                TextAlign = ContentAlignment.TopRight
            };

            // Add Change Password Button
            Button changePasswordButton = new Button
            {
                Text = "🔑 تغيير كلمة المرور",
                Size = new Size(180, 45),
                Location = new Point(200, 300),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            changePasswordButton.FlatAppearance.BorderSize = 0;
            changePasswordButton.Click += (s, e) => ShowChangePasswordForm();

            // Add Create Account Button
            Button createAccountButton = new Button
            {
                Text = "👤 إنشاء حساب جديد",
                Size = new Size(180, 45),
                Location = new Point(400, 300),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            createAccountButton.FlatAppearance.BorderSize = 0;
            createAccountButton.Click += (s, e) => ShowCreateAccountForm();

            // Add Forgot Password Button
            Button forgotPasswordButton = new Button
            {
                Text = "🔍 استعادة كلمة المرور",
                Size = new Size(180, 45),
                Location = new Point(600, 300),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            forgotPasswordButton.FlatAppearance.BorderSize = 0;
            forgotPasswordButton.Click += (s, e) => ShowForgotPasswordForm();

            contentFormPanel.Controls.AddRange(new Control[] {
                contentLabel, changePasswordButton, createAccountButton, forgotPasswordButton
            });

            Button backButton = new Button
            {
                Text = "🏠 العودة للرئيسية",
                Size = new Size(150, 40),
                Location = new Point(425, 500),
                BackColor = Color.FromArgb(25, 118, 210),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => ShowWelcomePage();

            contentPanel.Controls.AddRange(new Control[] { formTitle, contentFormPanel, backButton });
        }

        private void ShowChangePasswordForm()
        {
            try
            {
                using (var changePasswordForm = new ChangePasswordForm())
                {
                    if (changePasswordForm.ShowDialog(this) == DialogResult.OK)
                    {
                        MessageBox.Show("✅ تم تغيير كلمة المرور بنجاح!", "نجحت العملية",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج تغيير كلمة المرور", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج تغيير كلمة المرور", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowCreateAccountForm()
        {
            try
            {
                using (var createForm = new CreateAccountForm())
                {
                    if (createForm.ShowDialog(this) == DialogResult.OK)
                    {
                        MessageBox.Show("✅ تم إنشاء الحساب بنجاح!\n\nيمكن الآن تسجيل الدخول باستخدام الحساب الجديد.",
                            "نجح إنشاء الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج إنشاء حساب جديد", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج إنشاء الحساب", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowForgotPasswordForm()
        {
            try
            {
                using (var forgotForm = new ForgotPasswordForm())
                {
                    if (forgotForm.ShowDialog(this) == DialogResult.OK)
                    {
                        MessageBox.Show("✅ تم إعادة تعيين كلمة المرور بنجاح!", "نجحت العملية",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح نموذج استعادة كلمة المرور", ex);
                MessageBox.Show("حدث خطأ في فتح نموذج استعادة كلمة المرور", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenReports(object sender, EventArgs e)
        {
            ShowFormContent("التقارير والإحصائيات", "📊 نظام التقارير والإحصائيات المتقدم\n\nتقارير شاملة ومفصلة:\n• تقارير الوثائق\n• إحصائيات الأقسام\n• تقارير النشاط\n• رسوم بيانية تفاعلية\n• تصدير التقارير\n• طباعة التقارير\n• تحليلات متقدمة\n\nالنماذج المتاحة:\n- ReportsForm: التقارير الشاملة");
        }

        private void OpenAdvancedSearch(object sender, EventArgs e)
        {
            ShowFormContent("البحث المتقدم", "🔍 نظام البحث المتقدم\n\nبحث شامل ومتطور:\n• معايير بحث متعددة\n• بحث في النص الكامل\n• فلترة متقدمة\n• حفظ استعلامات البحث\n• نتائج مفصلة\n• بحث سريع\n\nالنماذج المتاحة:\n- AdvancedSearchForm: البحث المتقدم");
        }

        private void OpenBackup(object sender, EventArgs e)
        {
            ShowFormContent("النسخ الاحتياطي", "💾 نظام النسخ الاحتياطي والاستعادة\n\nحماية شاملة للبيانات:\n• إنشاء نسخ احتياطية\n• استعادة البيانات\n• جدولة النسخ التلقائية\n• التحقق من سلامة البيانات\n• ضغط وتشفير النسخ\n• إدارة النسخ\n\nميزات متقدمة للحماية والأمان");
        }

        private void OpenSettings(object sender, EventArgs e)
        {
            ShowFormContent("إعدادات النظام", "⚙️ إعدادات النظام الشاملة\n\nتخصيص كامل للنظام:\n• إعدادات عامة\n• إعدادات قاعدة البيانات\n• إعدادات الأمان\n• إعدادات الواجهة\n• إعدادات التقارير\n• إعدادات النسخ الاحتياطي\n\nالنماذج المتاحة:\n- SettingsForm: إعدادات النظام");
        }

        private void OpenAbout(object sender, EventArgs e)
        {
            ShowFormContent("حول النظام", "ℹ️ معلومات النظام المحسن\n\nالنظام المحسن للأرشفة الإلكترونية\nالإصدار 2.0.0 - النسخة المحسنة\n\nالميزات الجديدة:\n• واجهة مستخدم محسنة\n• تصميم عصري وجذاب\n• تأثيرات بصرية متقدمة\n• نظام تسجيل دخول متطور\n• شريط تنقل أفقي\n• تجربة مستخدم محسنة\n\nالنماذج المتاحة:\n- AboutForm: معلومات النظام");
        }

        private void ShowFormContent(string title, string content)
        {
            contentPanel.Controls.Clear();

            Label formTitle = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(1000, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Panel contentFormPanel = new Panel
            {
                Size = new Size(1000, 500),
                Location = new Point(20, 80),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            Label contentLabel = new Label
            {
                Text = content,
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(66, 66, 66),
                Size = new Size(950, 400),
                Location = new Point(25, 25),
                TextAlign = ContentAlignment.TopRight
            };

            Button backButton = new Button
            {
                Text = "العودة للرئيسية",
                Size = new Size(150, 40),
                Location = new Point(425, 440),
                BackColor = Color.FromArgb(25, 118, 210),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => ShowWelcomePage();

            contentFormPanel.Controls.AddRange(new Control[] { contentLabel, backButton });
            contentPanel.Controls.AddRange(new Control[] { formTitle, contentFormPanel });
        }
    }

    /// <summary>
    /// نموذج تغيير كلمة المرور
    /// Change Password Form
    /// </summary>
    public partial class ChangePasswordForm : Form
    {
        private Panel mainPanel;
        private TextBox txtCurrentPassword;
        private TextBox txtNewPassword;
        private TextBox txtConfirmNewPassword;
        private Button btnChangePassword;
        private Button btnCancel;

        public ChangePasswordForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تغيير كلمة المرور - النظام المحسن";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ShowInTaskbar = false;

            CreateMainLayout();
        }

        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Header
            Label headerLabel = new Label
            {
                Text = "🔑 تغيير كلمة المرور",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 152, 0),
                Size = new Size(390, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label subtitleLabel = new Label
            {
                Text = "يرجى إدخال كلمة المرور الحالية والجديدة",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(100, 100, 100),
                Size = new Size(390, 25),
                Location = new Point(20, 65),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Current Password
            Label lblCurrentPassword = new Label
            {
                Text = "🔒 كلمة المرور الحالية:",
                Size = new Size(150, 25),
                Location = new Point(30, 110),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtCurrentPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(190, 110),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // New Password
            Label lblNewPassword = new Label
            {
                Text = "🔐 كلمة المرور الجديدة:",
                Size = new Size(150, 25),
                Location = new Point(30, 150),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtNewPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(190, 150),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Confirm New Password
            Label lblConfirmNewPassword = new Label
            {
                Text = "✅ تأكيد كلمة المرور:",
                Size = new Size(150, 25),
                Location = new Point(30, 190),
                Font = new Font("Tahoma", 11, FontStyle.Bold)
            };

            txtConfirmNewPassword = new TextBox
            {
                Size = new Size(200, 30),
                Location = new Point(190, 190),
                Font = new Font("Tahoma", 11),
                UseSystemPasswordChar = true,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Instructions
            Label instructionsLabel = new Label
            {
                Text = "💡 كلمة المرور الجديدة يجب أن تكون:\n• لا تقل عن 6 أحرف\n• تحتوي على أحرف وأرقام",
                Font = new Font("Tahoma", 9),
                ForeColor = Color.FromArgb(100, 100, 100),
                Size = new Size(360, 50),
                Location = new Point(30, 230),
                TextAlign = ContentAlignment.TopRight
            };

            // Buttons
            btnChangePassword = new Button
            {
                Text = "✅ تغيير كلمة المرور",
                Size = new Size(150, 40),
                Location = new Point(190, 290),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnChangePassword.FlatAppearance.BorderSize = 0;
            btnChangePassword.Click += BtnChangePassword_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(100, 40),
                Location = new Point(360, 290),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            mainPanel.Controls.AddRange(new Control[] {
                headerLabel, subtitleLabel,
                lblCurrentPassword, txtCurrentPassword,
                lblNewPassword, txtNewPassword,
                lblConfirmNewPassword, txtConfirmNewPassword,
                instructionsLabel,
                btnChangePassword, btnCancel
            });

            this.Controls.Add(mainPanel);
            this.AcceptButton = btnChangePassword;
            this.CancelButton = btnCancel;
        }

        private void BtnChangePassword_Click(object sender, EventArgs e)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(txtCurrentPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور الحالية!", "بيانات ناقصة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrentPassword.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtNewPassword.Text) || txtNewPassword.Text.Length < 6)
            {
                MessageBox.Show("يرجى إدخال كلمة مرور جديدة لا تقل عن 6 أحرف!", "كلمة مرور ضعيفة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return;
            }

            if (txtNewPassword.Text != txtConfirmNewPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين!", "خطأ في التأكيد",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmNewPassword.Focus();
                return;
            }

            if (txtCurrentPassword.Text == txtNewPassword.Text)
            {
                MessageBox.Show("كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية!", "كلمة مرور مكررة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return;
            }

            // For admin user, just validate current password
            if (txtCurrentPassword.Text == "admin123")
            {
                MessageBox.Show("✅ تم تغيير كلمة المرور بنجاح!\n\n💡 ملاحظة: في النظام الكامل، سيتم حفظ كلمة المرور الجديدة في قاعدة البيانات.",
                    "نجحت العملية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم تغيير كلمة المرور للمدير");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                // Try to change password for registered users
                string currentUser = "admin"; // In real system, get from session
                if (UserManager.ChangePassword(currentUser, txtCurrentPassword.Text, txtNewPassword.Text))
                {
                    Logger.LogInfo(string.Format("تم تغيير كلمة المرور للمستخدم: {0}", currentUser));
                    MessageBox.Show("✅ تم تغيير كلمة المرور بنجاح!", "نجحت العملية",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("❌ كلمة المرور الحالية غير صحيحة!", "خطأ في كلمة المرور",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCurrentPassword.Focus();
                    txtCurrentPassword.SelectAll();
                }
            }
        }
    }
}
