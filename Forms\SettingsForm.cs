using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج الإعدادات
    /// Settings form
    /// </summary>
    public partial class SettingsForm : Form
    {
        public SettingsForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            this.Text = "الإعدادات";
            this.Size = new Size(700, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            Label tempLabel = new Label
            {
                Text = "نموذج الإعدادات قيد التطوير...",
                Font = new Font("Tahoma", 14F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(tempLabel);
        }
    }
}
