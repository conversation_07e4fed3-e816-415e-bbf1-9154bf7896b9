﻿using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using Ali_Mo<PERSON>.BusinessLogic;
using Ali_Mo<PERSON>.Utilities;

namespace Ali_Mola.Forms
{
    /// <summary>
    /// نموذج الإعدادات
    /// Settings form
    /// </summary>
    public partial class SettingsForm : Form
    {
        #region Controls
        private TabControl tabControl;
        private TabPage tabBackup;
        private TabPage tabUsers;
        private TabPage tabSystem;
        private Button btnCreateBackup;
        private Button btnRestoreBackup;
        private Button btnChangePassword;
        private Button btnClose;
        private Label lblCurrentUser;
        private Label lblSystemInfo;
        #endregion

        public SettingsForm()
        {
            InitializeComponent();
            SetupForm();
            LoadSettings();
        }

        private void SetupForm()
        {
            this.Text = "الإعدادات";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateTabControl();
            CreateBackupTab();
            CreateUsersTab();
            CreateSystemTab();
            CreateBottomButtons();
        }

        /// <summary>
        /// إنشاء التحكم في التبويبات
        /// Create tab control
        /// </summary>
        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Size = new Size(760, 500),
                Location = new Point(20, 20),
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(tabControl);

            // تبويب النسخ الاحتياطي
            // Backup tab
            tabBackup = new TabPage
            {
                Text = "النسخ الاحتياطي",
                BackColor = Color.White
            };
            tabControl.TabPages.Add(tabBackup);

            // تبويب المستخدمين
            // Users tab
            tabUsers = new TabPage
            {
                Text = "المستخدمين",
                BackColor = Color.White
            };
            tabControl.TabPages.Add(tabUsers);

            // تبويب النظام
            // System tab
            tabSystem = new TabPage
            {
                Text = "النظام",
                BackColor = Color.White
            };
            tabControl.TabPages.Add(tabSystem);
        }

        /// <summary>
        /// إنشاء تبويب النسخ الاحتياطي
        /// Create backup tab
        /// </summary>
        private void CreateBackupTab()
        {
            // عنوان التبويب
            // Tab title
            Label titleLabel = new Label
            {
                Text = "إدارة النسخ الاحتياطي",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };
            tabBackup.Controls.Add(titleLabel);

            // أيقونة النسخ الاحتياطي
            // Backup icon
            Panel iconPanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(600, 15),
                BackColor = Color.FromArgb(76, 175, 80)
            };

            Label iconLabel = new Label
            {
                Text = "💾",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            tabBackup.Controls.Add(iconPanel);

            // معلومات النسخ الاحتياطي
            // Backup information
            Label infoLabel = new Label
            {
                Text = "يمكنك إنشاء نسخة احتياطية من جميع بيانات النظام أو استعادة نسخة احتياطية سابقة.",
                Size = new Size(500, 40),
                Location = new Point(20, 60),
                ForeColor = Color.Gray
            };
            tabBackup.Controls.Add(infoLabel);

            // قسم إنشاء النسخة الاحتياطية
            // Create backup section
            GroupBox createGroup = new GroupBox
            {
                Text = "إنشاء نسخة احتياطية",
                Size = new Size(700, 120),
                Location = new Point(20, 120),
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            tabBackup.Controls.Add(createGroup);

            Label createInfo = new Label
            {
                Text = "سيتم إنشاء ملف نسخة احتياطية يحتوي على جميع البيانات بتنسيق التاريخ والوقت الحالي.",
                Size = new Size(600, 30),
                Location = new Point(20, 30),
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.Gray
            };
            createGroup.Controls.Add(createInfo);

            btnCreateBackup = new Button
            {
                Text = "إنشاء نسخة احتياطية",
                Size = new Size(150, 40),
                Location = new Point(20, 65),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnCreateBackup.FlatAppearance.BorderSize = 0;
            btnCreateBackup.Click += BtnCreateBackup_Click;
            createGroup.Controls.Add(btnCreateBackup);

            // قسم استعادة النسخة الاحتياطية
            // Restore backup section
            GroupBox restoreGroup = new GroupBox
            {
                Text = "استعادة نسخة احتياطية",
                Size = new Size(700, 120),
                Location = new Point(20, 260),
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            tabBackup.Controls.Add(restoreGroup);

            Label restoreInfo = new Label
            {
                Text = "تحذير: استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية!",
                Size = new Size(600, 30),
                Location = new Point(20, 30),
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.Red
            };
            restoreGroup.Controls.Add(restoreInfo);

            btnRestoreBackup = new Button
            {
                Text = "استعادة نسخة احتياطية",
                Size = new Size(150, 40),
                Location = new Point(20, 65),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnRestoreBackup.FlatAppearance.BorderSize = 0;
            btnRestoreBackup.Click += BtnRestoreBackup_Click;
            restoreGroup.Controls.Add(btnRestoreBackup);
        }

        /// <summary>
        /// إنشاء تبويب المستخدمين
        /// Create users tab
        /// </summary>
        private void CreateUsersTab()
        {
            // عنوان التبويب
            // Tab title
            Label titleLabel = new Label
            {
                Text = "إدارة المستخدمين",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };
            tabUsers.Controls.Add(titleLabel);

            // أيقونة المستخدمين
            // Users icon
            Panel iconPanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(600, 15),
                BackColor = Color.FromArgb(33, 150, 243)
            };

            Label iconLabel = new Label
            {
                Text = "👤",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            tabUsers.Controls.Add(iconPanel);

            // معلومات المستخدم الحالي
            // Current user information
            GroupBox currentUserGroup = new GroupBox
            {
                Text = "المستخدم الحالي",
                Size = new Size(700, 120),
                Location = new Point(20, 80),
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            tabUsers.Controls.Add(currentUserGroup);

            lblCurrentUser = new Label
            {
                Text = string.Format("المستخدم: {0}\nالاسم: {AuthenticationService.CurrentUser?.FullName}\nالدور: {AuthenticationService.CurrentUser?.Role}", AuthenticationService.CurrentUser?.Username),
                Size = new Size(400, 60),
                Location = new Point(20, 30),
                Font = new Font("Tahoma", 10F)
            };
            currentUserGroup.Controls.Add(lblCurrentUser);

            btnChangePassword = new Button
            {
                Text = "تغيير كلمة المرور",
                Size = new Size(130, 35),
                Location = new Point(450, 45),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnChangePassword.FlatAppearance.BorderSize = 0;
            btnChangePassword.Click += BtnChangePassword_Click;
            currentUserGroup.Controls.Add(btnChangePassword);

            // إدارة المستخدمين (للمدير فقط)
            // User management (admin only)
            if (AuthenticationService.IsAdmin())
            {
                GroupBox manageUsersGroup = new GroupBox
                {
                    Text = "إدارة المستخدمين",
                    Size = new Size(700, 120),
                    Location = new Point(20, 220),
                    Font = new Font("Tahoma", 10F, FontStyle.Bold)
                };
                tabUsers.Controls.Add(manageUsersGroup);

                Label manageInfo = new Label
                {
                    Text = "يمكنك إضافة مستخدمين جدد أو تعديل صلاحيات المستخدمين الموجودين.",
                    Size = new Size(500, 30),
                    Location = new Point(20, 30),
                    Font = new Font("Tahoma", 9F),
                    ForeColor = Color.Gray
                };
                manageUsersGroup.Controls.Add(manageInfo);

                Button btnManageUsers = new Button
                {
                    Text = "إدارة المستخدمين",
                    Size = new Size(130, 35),
                    Location = new Point(20, 65),
                    BackColor = Color.FromArgb(33, 150, 243),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Font = new Font("Tahoma", 10F)
                };
                btnManageUsers.FlatAppearance.BorderSize = 0;
                btnManageUsers.Click += BtnManageUsers_Click;
                manageUsersGroup.Controls.Add(btnManageUsers);
            }
        }

        /// <summary>
        /// إنشاء تبويب النظام
        /// Create system tab
        /// </summary>
        private void CreateSystemTab()
        {
            // عنوان التبويب
            // Tab title
            Label titleLabel = new Label
            {
                Text = "معلومات النظام",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(20, 20)
            };
            tabSystem.Controls.Add(titleLabel);

            // أيقونة النظام
            // System icon
            Panel iconPanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(600, 15),
                BackColor = Color.FromArgb(158, 158, 158)
            };

            Label iconLabel = new Label
            {
                Text = "⚙️",
                Font = new Font("Segoe UI Emoji", 24F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            iconPanel.Controls.Add(iconLabel);
            tabSystem.Controls.Add(iconPanel);

            // معلومات النظام
            // System information
            GroupBox systemInfoGroup = new GroupBox
            {
                Text = "معلومات النظام",
                Size = new Size(700, 200),
                Location = new Point(20, 80),
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            tabSystem.Controls.Add(systemInfoGroup);

            lblSystemInfo = new Label
            {
                Text = GetSystemInfo(),
                Size = new Size(650, 150),
                Location = new Point(20, 30),
                Font = new Font("Tahoma", 9F)
            };
            systemInfoGroup.Controls.Add(lblSystemInfo);

            // إعدادات النظام
            // System settings
            GroupBox settingsGroup = new GroupBox
            {
                Text = "إعدادات النظام",
                Size = new Size(700, 120),
                Location = new Point(20, 300),
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            tabSystem.Controls.Add(settingsGroup);

            Button btnCleanLogs = new Button
            {
                Text = "تنظيف السجلات",
                Size = new Size(120, 35),
                Location = new Point(20, 40),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnCleanLogs.FlatAppearance.BorderSize = 0;
            btnCleanLogs.Click += BtnCleanLogs_Click;
            settingsGroup.Controls.Add(btnCleanLogs);

            Button btnCleanTemp = new Button
            {
                Text = "تنظيف الملفات المؤقتة",
                Size = new Size(150, 35),
                Location = new Point(160, 40),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnCleanTemp.FlatAppearance.BorderSize = 0;
            btnCleanTemp.Click += BtnCleanTemp_Click;
            settingsGroup.Controls.Add(btnCleanTemp);
        }

        /// <summary>
        /// إنشاء الأزرار السفلية
        /// Create bottom buttons
        /// </summary>
        private void CreateBottomButtons()
        {
            // زر الإغلاق
            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(120, 40),
                Location = new Point(350, 540),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 11F)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += BtnClose_Click;
            this.Controls.Add(btnClose);
        }

        /// <summary>
        /// تحميل الإعدادات
        /// Load settings
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // تحديث معلومات المستخدم الحالي
                // Update current user information
                if (lblCurrentUser != null && AuthenticationService.CurrentUser != null)
                {
                    lblCurrentUser.Text = string.Format("المستخدم: {0}\n", AuthenticationService.CurrentUser.Username) +
                                         string.Format("الاسم: {0}\n", AuthenticationService.CurrentUser.FullName ?? "غير محدد") +
                                         string.Format("الدور: {0}", AuthenticationService.CurrentUser.Role);
                }

                Logger.LogInfo("تم تحميل إعدادات النظام");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحميل الإعدادات", ex);
            }
        }

        /// <summary>
        /// الحصول على معلومات النظام
        /// Get system information
        /// </summary>
        private string GetSystemInfo()
        {
            try
            {
                return "اسم النظام: نظام الأرشفة الإلكترونية\n" +
                       "الإصدار: 1.0.0\n" +
                       string.Format("تاريخ البناء: {0}\n", DateTime.Now.ToString("yyyy-MM-dd")) +
                       string.Format("نظام التشغيل: {0}\n", Environment.OSVersion) +
                       string.Format("إصدار .NET: {0}\n", Environment.Version) +
                       string.Format("اسم الجهاز: {0}\n", Environment.MachineName) +
                       string.Format("اسم المستخدم: {0}\n", Environment.UserName) +
                       string.Format("مجلد التطبيق: {0}", AppDomain.CurrentDomain.BaseDirectory);
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على معلومات النظام", ex);
                return "خطأ في تحميل معلومات النظام";
            }
        }

        #region Event Handlers

        /// <summary>
        /// معالج حدث إنشاء نسخة احتياطية
        /// Create backup event handler
        /// </summary>
        private void BtnCreateBackup_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog saveDialog = new SaveFileDialog())
                {
                    saveDialog.Title = "حفظ النسخة الاحتياطية";
                    saveDialog.Filter = "ملفات النسخ الاحتياطي|*.bak|جميع الملفات|*.*";
                    saveDialog.FileName = string.Format("أرشفة_إلكترونية_{0}.bak", DateTime.Now.ToString("yyyyMMdd_HHmmss"));

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        // محاكاة إنشاء النسخة الاحتياطية
                        // Simulate backup creation
                        btnCreateBackup.Enabled = false;
                        btnCreateBackup.Text = "جاري الإنشاء...";
                        this.Cursor = Cursors.WaitCursor;

                        // هنا سيتم تنفيذ عملية النسخ الاحتياطي الفعلية
                        // Here the actual backup process will be implemented
                        System.Threading.Thread.Sleep(2000); // محاكاة الوقت

                        File.WriteAllText(saveDialog.FileName, string.Format("نسخة احتياطية تم إنشاؤها في: {0}", DateTime.Now));

                        MessageBox.Show(string.Format("تم إنشاء النسخة الاحتياطية بنجاح!\nالمسار: {0}", saveDialog.FileName),
                            "نجح الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Logger.LogInfo(string.Format("تم إنشاء نسخة احتياطية: {0}", saveDialog.FileName));
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إنشاء النسخة الاحتياطية", ex);
                MessageBox.Show("حدث خطأ أثناء إنشاء النسخة الاحتياطية", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnCreateBackup.Enabled = true;
                btnCreateBackup.Text = "إنشاء نسخة احتياطية";
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// معالج حدث استعادة نسخة احتياطية
        /// Restore backup event handler
        /// </summary>
        private void BtnRestoreBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (MessageBox.Show("تحذير: استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية!\nهل أنت متأكد؟",
                    "تأكيد الاستعادة", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    using (OpenFileDialog openDialog = new OpenFileDialog())
                    {
                        openDialog.Title = "اختر ملف النسخة الاحتياطية";
                        openDialog.Filter = "ملفات النسخ الاحتياطي|*.bak|جميع الملفات|*.*";

                        if (openDialog.ShowDialog() == DialogResult.OK)
                        {
                            // محاكاة استعادة النسخة الاحتياطية
                            // Simulate backup restoration
                            btnRestoreBackup.Enabled = false;
                            btnRestoreBackup.Text = "جاري الاستعادة...";
                            this.Cursor = Cursors.WaitCursor;

                            // هنا سيتم تنفيذ عملية الاستعادة الفعلية
                            // Here the actual restoration process will be implemented
                            System.Threading.Thread.Sleep(3000); // محاكاة الوقت

                            MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح!\nسيتم إعادة تشغيل التطبيق.",
                                "نجحت الاستعادة", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            Logger.LogInfo(string.Format("تم استعادة نسخة احتياطية من: {0}", openDialog.FileName));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في استعادة النسخة الاحتياطية", ex);
                MessageBox.Show("حدث خطأ أثناء استعادة النسخة الاحتياطية", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnRestoreBackup.Enabled = true;
                btnRestoreBackup.Text = "استعادة نسخة احتياطية";
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// معالج حدث تغيير كلمة المرور
        /// Change password event handler
        /// </summary>
        private void BtnChangePassword_Click(object sender, EventArgs e)
        {
            try
            {
                using (var changePasswordForm = new ChangePasswordForm())
                {
                    if (changePasswordForm.ShowDialog() == DialogResult.OK)
                    {
                        MessageBox.Show("تم تغيير كلمة المرور بنجاح!", "نجح التغيير",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        Logger.LogInfo("تم تغيير كلمة مرور المستخدم");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تغيير كلمة المرور", ex);
                MessageBox.Show("حدث خطأ أثناء تغيير كلمة المرور", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث إدارة المستخدمين
        /// Manage users event handler
        /// </summary>
        private void BtnManageUsers_Click(object sender, EventArgs e)
        {
            try
            {
                using (var userManagementForm = new UserManagementForm())
                {
                    userManagementForm.ShowDialog();
                }
                Logger.LogInfo("تم فتح نافذة إدارة المستخدمين");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في فتح إدارة المستخدمين", ex);
                MessageBox.Show("حدث خطأ أثناء فتح إدارة المستخدمين", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث تنظيف السجلات
        /// Clean logs event handler
        /// </summary>
        private void BtnCleanLogs_Click(object sender, EventArgs e)
        {
            try
            {
                if (MessageBox.Show("هل تريد حذف السجلات القديمة (أكثر من 30 يوم)؟", "تأكيد التنظيف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    Logger.CleanupOldLogs();
                    MessageBox.Show("تم تنظيف السجلات القديمة بنجاح!", "نجح التنظيف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    Logger.LogInfo("تم تنظيف السجلات القديمة");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تنظيف السجلات", ex);
                MessageBox.Show("حدث خطأ أثناء تنظيف السجلات", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث تنظيف الملفات المؤقتة
        /// Clean temp files event handler
        /// </summary>
        private void BtnCleanTemp_Click(object sender, EventArgs e)
        {
            try
            {
                AttachmentService.CleanupTempFiles();
                MessageBox.Show("تم تنظيف الملفات المؤقتة بنجاح!", "نجح التنظيف",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                Logger.LogInfo("تم تنظيف الملفات المؤقتة");
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تنظيف الملفات المؤقتة", ex);
                MessageBox.Show("حدث خطأ أثناء تنظيف الملفات المؤقتة", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// معالج حدث الإغلاق
        /// Close event handler
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}

