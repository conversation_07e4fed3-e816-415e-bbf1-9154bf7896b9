using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Ali_Mo<PERSON>.DataAccess;
using Ali_Mo<PERSON>.DataAccess.Models;
using Ali_Mola.Utilities;

namespace Ali_Mola.BusinessLogic
{
    /// <summary>
    /// خدمة المرفقات - تدير عمليات المرفقات
    /// Attachment Service - Manages attachment operations
    /// </summary>
    public class AttachmentService
    {
        private static readonly string AttachmentsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments");

        /// <summary>
        /// تهيئة مجلد المرفقات
        /// Initialize attachments directory
        /// </summary>
        static AttachmentService()
        {
            try
            {
                if (!Directory.Exists(AttachmentsDirectory))
                {
                    Directory.CreateDirectory(AttachmentsDirectory);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("فشل في إنشاء مجلد المرفقات", ex);
            }
        }

        /// <summary>
        /// إضافة مرفق جديد
        /// Add new attachment
        /// </summary>
        /// <param name="documentId">معرف الوثيقة</param>
        /// <param name="sourceFilePath">مسار الملف المصدر</param>
        /// <param name="originalFileName">اسم الملف الأصلي</param>
        /// <returns>معرف المرفق الجديد أو -1 في حالة الفشل</returns>
        public static int AddAttachment(int documentId, string sourceFilePath, string originalFileName = null)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    throw new FileNotFoundException("الملف المصدر غير موجود");

                var context = new SimpleDataContext();
                {
                    // التحقق من وجود الوثيقة
                    // Check if document exists
                    var document = context.Documents.Find(documentId);
                    if (document == null)
                        throw new ArgumentException("الوثيقة المحددة غير موجودة");

                    // الحصول على معلومات الملف
                    // Get file information
                    var fileInfo = new FileInfo(sourceFilePath);
                    string fileExtension = fileInfo.Extension.ToLower();
                    string fileName = originalFileName ?? fileInfo.Name;

                    // التحقق من نوع الملف المدعوم
                    // Check supported file type
                    if (!IsSupportedFileType(fileExtension))
                        throw new ArgumentException($"نوع الملف غير مدعوم: {fileExtension}");

                    // إنشاء اسم ملف فريد
                    // Create unique file name
                    string uniqueFileName = $"{documentId}_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid():N}{fileExtension}";
                    string destinationPath = Path.Combine(AttachmentsDirectory, uniqueFileName);

                    // نسخ الملف
                    // Copy file
                    File.Copy(sourceFilePath, destinationPath);

                    // إنشاء سجل المرفق
                    // Create attachment record
                    var attachment = new Attachment
                    {
                        DocumentId = documentId,
                        FilePath = destinationPath,
                        FileName = fileName,
                        FileType = fileExtension.TrimStart('.').ToUpper(),
                        FileSize = fileInfo.Length
                    };

                    context.Attachments.Add(attachment);
                    context.SaveChanges();

                    Logger.LogInfo($"تم إضافة مرفق جديد: {fileName} للوثيقة {documentId}");
                    return attachment.AttachmentId;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في إضافة المرفق", ex);
                return -1;
            }
        }

        /// <summary>
        /// حذف مرفق
        /// Delete attachment
        /// </summary>
        /// <param name="attachmentId">معرف المرفق</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteAttachment(int attachmentId)
        {
            try
            {
                // تنفيذ مبسط - Simplified implementation
                if (true)
                {
                    // var attachment = context.Attachments.Find(attachmentId);
                    if (attachment == null)
                        return false;

                    // حذف الملف من القرص
                    // Delete file from disk
                    if (File.Exists(attachment.FilePath))
                    {
                        File.Delete(attachment.FilePath);
                    }

                    // حذف سجل المرفق
                    // Delete attachment record
                    // context.Attachments.Remove(attachment);
                    // context.SaveChanges();

                    Logger.LogInfo($"تم حذف المرفق: {attachment.FileName}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في حذف المرفق", ex);
                return false;
            }
        }

        /// <summary>
        /// الحصول على مرفق بالمعرف
        /// Get attachment by ID
        /// </summary>
        /// <param name="attachmentId">معرف المرفق</param>
        /// <returns>المرفق أو null</returns>
        public static Attachment GetAttachment(int attachmentId)
        {
            try
            {
                // تنفيذ مبسط - Simplified implementation
                if (true)
                {
                    return null; // تنفيذ مبسط
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على المرفق", ex);
                return null;
            }
        }

        /// <summary>
        /// الحصول على مرفقات وثيقة
        /// Get document attachments
        /// </summary>
        /// <param name="documentId">معرف الوثيقة</param>
        /// <returns>قائمة المرفقات</returns>
        public static List<Attachment> GetDocumentAttachments(int documentId)
        {
            try
            {
                // تنفيذ مبسط - Simplified implementation
                if (true)
                {
                    return new List<Attachment>(); // تنفيذ مبسط
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في الحصول على مرفقات الوثيقة", ex);
                return new List<Attachment>();
            }
        }

        /// <summary>
        /// نسخ مرفق إلى مجلد مؤقت للعرض
        /// Copy attachment to temp folder for viewing
        /// </summary>
        /// <param name="attachmentId">معرف المرفق</param>
        /// <returns>مسار الملف المؤقت</returns>
        public static string GetAttachmentForViewing(int attachmentId)
        {
            try
            {
                var attachment = GetAttachment(attachmentId);
                if (attachment == null || !File.Exists(attachment.FilePath))
                    return null;

                // إنشاء مجلد مؤقت
                // Create temp directory
                string tempDir = Path.Combine(Path.GetTempPath(), "ArchivingSystem");
                if (!Directory.Exists(tempDir))
                    Directory.CreateDirectory(tempDir);

                // نسخ الملف إلى المجلد المؤقت
                // Copy file to temp directory
                string tempFilePath = Path.Combine(tempDir, attachment.FileName);
                File.Copy(attachment.FilePath, tempFilePath, true);

                return tempFilePath;
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تحضير المرفق للعرض", ex);
                return null;
            }
        }

        /// <summary>
        /// التحقق من نوع الملف المدعوم
        /// Check if file type is supported
        /// </summary>
        /// <param name="fileExtension">امتداد الملف</param>
        /// <returns>true إذا كان نوع الملف مدعوم</returns>
        public static bool IsSupportedFileType(string fileExtension)
        {
            var supportedTypes = new[] { ".pdf", ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".docx", ".doc", ".txt" };
            return supportedTypes.Contains(fileExtension.ToLower());
        }

        /// <summary>
        /// الحصول على أنواع الملفات المدعومة
        /// Get supported file types
        /// </summary>
        /// <returns>قائمة أنواع الملفات المدعومة</returns>
        public static string[] GetSupportedFileTypes()
        {
            return new[] { ".pdf", ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".docx", ".doc", ".txt" };
        }

        /// <summary>
        /// تنظيف الملفات المؤقتة
        /// Clean up temporary files
        /// </summary>
        public static void CleanupTempFiles()
        {
            try
            {
                string tempDir = Path.Combine(Path.GetTempPath(), "ArchivingSystem");
                if (Directory.Exists(tempDir))
                {
                    var files = Directory.GetFiles(tempDir);
                    foreach (var file in files)
                    {
                        try
                        {
                            var fileInfo = new FileInfo(file);
                            if (fileInfo.CreationTime < DateTime.Now.AddHours(-1)) // حذف الملفات الأقدم من ساعة
                            {
                                File.Delete(file);
                            }
                        }
                        catch
                        {
                            // تجاهل أخطاء حذف الملفات المؤقتة
                            // Ignore temp file deletion errors
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تنظيف الملفات المؤقتة", ex);
            }
        }
    }
}
