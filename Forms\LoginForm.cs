using System;
using System.Drawing;
using System.Windows.Forms;
using <PERSON>_<PERSON><PERSON>.BusinessLogic;
using <PERSON>_<PERSON><PERSON>.Utilities;

namespace <PERSON>_<PERSON>.Forms
{
    /// <summary>
    /// نموذج تسجيل الدخول
    /// Login Form
    /// </summary>
    public partial class LoginForm : Form
    {
        /// <summary>
        /// منشئ نموذج تسجيل الدخول
        /// Login form constructor
        /// </summary>
        public LoginForm()
        {
            InitializeComponent();
            SetupForm();
        }

        /// <summary>
        /// إعداد النموذج
        /// Setup form
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النموذج الأساسية
            // Basic form settings
            this.Text = "نظام الأرشفة الإلكترونية - تسجيل الدخول";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 248, 255);

            // إعداد الخط العربي
            // Setup Arabic font
            Font arabicFont = new Font("Tahoma", 10F, FontStyle.Regular);
            this.Font = arabicFont;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        /// <summary>
        /// إنشاء عناصر التحكم
        /// Create controls
        /// </summary>
        private void CreateControls()
        {
            // عنوان النظام
            // System title
            Label titleLabel = new Label
            {
                Text = "نظام الأرشفة الإلكترونية",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                Size = new Size(300, 30),
                Location = new Point(50, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(titleLabel);

            // تسمية اسم المستخدم
            // Username label
            Label usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Size = new Size(100, 25),
                Location = new Point(50, 80),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(usernameLabel);

            // مربع نص اسم المستخدم
            // Username textbox
            txtUsername = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 80),
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(txtUsername);

            // تسمية كلمة المرور
            // Password label
            Label passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Size = new Size(100, 25),
                Location = new Point(50, 120),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(passwordLabel);

            // مربع نص كلمة المرور
            // Password textbox
            txtPassword = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(160, 120),
                UseSystemPasswordChar = true,
                Font = new Font("Tahoma", 10F)
            };
            this.Controls.Add(txtPassword);

            // زر تسجيل الدخول
            // Login button
            btnLogin = new Button
            {
                Text = "تسجيل الدخول",
                Size = new Size(100, 35),
                Location = new Point(160, 170),
                BackColor = Color.FromArgb(25, 118, 210),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;
            this.Controls.Add(btnLogin);

            // زر إلغاء
            // Cancel button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 35),
                Location = new Point(270, 170),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;
            this.Controls.Add(btnCancel);

            // تسمية معلومات افتراضية
            // Default credentials info
            Label infoLabel = new Label
            {
                Text = "المستخدم الافتراضي: admin\nكلمة المرور: admin123",
                Size = new Size(300, 40),
                Location = new Point(50, 220),
                ForeColor = Color.Gray,
                Font = new Font("Tahoma", 8F),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(infoLabel);

            // تعيين الزر الافتراضي والإلغاء
            // Set default and cancel buttons
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;

            // تركيز على مربع اسم المستخدم
            // Focus on username textbox
            txtUsername.Focus();
        }

        /// <summary>
        /// معالج حدث النقر على زر تسجيل الدخول
        /// Login button click event handler
        /// </summary>
        private void BtnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                // Validate input
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                // تعطيل الأزرار أثناء المعالجة
                // Disable buttons during processing
                btnLogin.Enabled = false;
                btnCancel.Enabled = false;
                this.Cursor = Cursors.WaitCursor;

                // محاولة تسجيل الدخول
                // Attempt login
                bool loginSuccess = AuthenticationService.Login(txtUsername.Text.Trim(), txtPassword.Text);

                if (loginSuccess)
                {
                    Logger.LogInfo($"تم تسجيل دخول ناجح للمستخدم: {txtUsername.Text}");
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError("خطأ في تسجيل الدخول", ex);
                MessageBox.Show("حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // إعادة تمكين الأزرار
                // Re-enable buttons
                btnLogin.Enabled = true;
                btnCancel.Enabled = true;
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// معالج حدث النقر على زر الإلغاء
        /// Cancel button click event handler
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// معالج حدث تحميل النموذج
        /// Form load event handler
        /// </summary>
        private void LoginForm_Load(object sender, EventArgs e)
        {
            // تنظيف الملفات المؤقتة عند بدء التطبيق
            // Clean up temp files on application start
            AttachmentService.CleanupTempFiles();
            Logger.CleanupOldLogs();
        }

        #region Designer Variables
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        #endregion
    }
}
